(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{3355:(e,t,a)=>{"use strict";a.d(t,{Assistant:()=>tg});var s=a(5155),r=a(2115),i=a(1745),l=a(9954),n=a(148),o=a(2081),d=a(2910),c=a(123),u=a(5058),x=a(8862),h=a(811),m=a(3793),f=a(7004),g=a(7156),b=a(9059),p=a(6853),v=a(8592),j=a(8010),w=a(8441),y=a(7126),N=a(7295),k=a(2907),C=a(1887),z=a(4773),S=a(4967),A=a(2042),_=a(1880),W=a(173),D=a(8997),I=a(3243),P=a(1557),T=a(8832),E=a(4616),L=a(9881),U=a(8979),O=a(5196),R=a(4357),F=a(3904),H=a(9917),M=a(2355),J=a(3052),X=a(3588),q=a(2596),B=a(9688);function K(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,B.QP)((0,q.$)(t))}function V(e){let{delayDuration:t=0,...a}=e;return(0,s.jsx)(X.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function Y(e){let{...t}=e;return(0,s.jsx)(V,{children:(0,s.jsx)(X.bL,{"data-slot":"tooltip",...t})})}function Z(e){let{...t}=e;return(0,s.jsx)(X.l9,{"data-slot":"tooltip-trigger",...t})}function G(e){let{className:t,sideOffset:a=0,children:r,...i}=e;return(0,s.jsx)(X.ZL,{children:(0,s.jsxs)(X.UC,{"data-slot":"tooltip-content",sideOffset:a,className:K("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...i,children:[r,(0,s.jsx)(X.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}var $=a(9708),Q=a(2085);let ee=(0,Q.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function et(e){let{className:t,variant:a,size:r,asChild:i=!1,...l}=e,n=i?$.DX:"button";return(0,s.jsx)(n,{"data-slot":"button",className:K(ee({variant:a,size:r,className:t})),...l})}let ea=(0,r.forwardRef)((e,t)=>{let{children:a,tooltip:r,side:i="bottom",className:l,...n}=e;return(0,s.jsx)(V,{children:(0,s.jsxs)(Y,{children:[(0,s.jsx)(Z,{asChild:!0,children:(0,s.jsxs)(et,{variant:"ghost",size:"icon",...n,className:K("size-6 p-1",l),ref:t,children:[a,(0,s.jsx)("span",{className:"sr-only",children:r})]})}),(0,s.jsx)(G,{side:i,children:r})]})})});ea.displayName="TooltipIconButton";var es=a(2900);a(8135);var er=a(274),ei=a(9912),el=a(3829),en=a(4823);let eo=(0,r.memo)(()=>(0,s.jsx)(er.i,{remarkPlugins:[en.A],className:"aui-md",components:ec})),ed=function(){let{copiedDuration:e=3e3}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,a]=(0,r.useState)(!1);return{isCopied:t,copyToClipboard:t=>{t&&navigator.clipboard.writeText(t).then(()=>{a(!0),setTimeout(()=>a(!1),e)})}}},ec=(0,ei.Y_)({h1:e=>{let{className:t,...a}=e;return(0,s.jsx)("h1",{className:K("mb-8 scroll-m-20 text-4xl font-extrabold tracking-tight last:mb-0",t),...a})},h2:e=>{let{className:t,...a}=e;return(0,s.jsx)("h2",{className:K("mb-4 mt-8 scroll-m-20 text-3xl font-semibold tracking-tight first:mt-0 last:mb-0",t),...a})},h3:e=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{className:K("mb-4 mt-6 scroll-m-20 text-2xl font-semibold tracking-tight first:mt-0 last:mb-0",t),...a})},h4:e=>{let{className:t,...a}=e;return(0,s.jsx)("h4",{className:K("mb-4 mt-6 scroll-m-20 text-xl font-semibold tracking-tight first:mt-0 last:mb-0",t),...a})},h5:e=>{let{className:t,...a}=e;return(0,s.jsx)("h5",{className:K("my-4 text-lg font-semibold first:mt-0 last:mb-0",t),...a})},h6:e=>{let{className:t,...a}=e;return(0,s.jsx)("h6",{className:K("my-4 font-semibold first:mt-0 last:mb-0",t),...a})},p:e=>{let{className:t,...a}=e;return(0,s.jsx)("p",{className:K("mb-5 mt-5 leading-7 first:mt-0 last:mb-0",t),...a})},a:e=>{let{className:t,...a}=e;return(0,s.jsx)("a",{className:K("text-primary font-medium underline underline-offset-4",t),...a})},blockquote:e=>{let{className:t,...a}=e;return(0,s.jsx)("blockquote",{className:K("border-l-2 pl-6 italic",t),...a})},ul:e=>{let{className:t,...a}=e;return(0,s.jsx)("ul",{className:K("my-5 ml-6 list-disc [&>li]:mt-2",t),...a})},ol:e=>{let{className:t,...a}=e;return(0,s.jsx)("ol",{className:K("my-5 ml-6 list-decimal [&>li]:mt-2",t),...a})},hr:e=>{let{className:t,...a}=e;return(0,s.jsx)("hr",{className:K("my-5 border-b",t),...a})},table:e=>{let{className:t,...a}=e;return(0,s.jsx)("table",{className:K("my-5 w-full border-separate border-spacing-0 overflow-y-auto",t),...a})},th:e=>{let{className:t,...a}=e;return(0,s.jsx)("th",{className:K("bg-muted px-4 py-2 text-left font-bold first:rounded-tl-lg last:rounded-tr-lg [&[align=center]]:text-center [&[align=right]]:text-right",t),...a})},td:e=>{let{className:t,...a}=e;return(0,s.jsx)("td",{className:K("border-b border-l px-4 py-2 text-left last:border-r [&[align=center]]:text-center [&[align=right]]:text-right",t),...a})},tr:e=>{let{className:t,...a}=e;return(0,s.jsx)("tr",{className:K("m-0 border-b p-0 first:border-t [&:last-child>td:first-child]:rounded-bl-lg [&:last-child>td:last-child]:rounded-br-lg",t),...a})},sup:e=>{let{className:t,...a}=e;return(0,s.jsx)("sup",{className:K("[&>a]:text-xs [&>a]:no-underline",t),...a})},pre:e=>{let{className:t,...a}=e;return(0,s.jsx)("pre",{className:K("overflow-x-auto rounded-b-lg bg-black p-4 text-white",t),...a})},code:function(e){let{className:t,...a}=e,r=(0,el.$7)();return(0,s.jsx)("code",{className:K(!r&&"bg-muted rounded border font-semibold",t),...a})},CodeHeader:e=>{let{language:t,code:a}=e,{isCopied:r,copyToClipboard:i}=ed();return(0,s.jsxs)("div",{className:"flex items-center justify-between gap-4 rounded-t-lg bg-zinc-900 px-4 py-2 text-sm font-semibold text-white",children:[(0,s.jsx)("span",{className:"lowercase [&>span]:text-xs",children:t}),(0,s.jsxs)(ea,{tooltip:"Copy",onClick:()=>{a&&!r&&i(a)},children:[!r&&(0,s.jsx)(R.A,{}),r&&(0,s.jsx)(O.A,{})]})]})}});var eu=a(7863),ex=a(6474);let eh=e=>{let{toolName:t,argsText:a,result:i}=e,[l,n]=(0,r.useState)(!0);return(0,s.jsxs)("div",{className:"mb-4 flex w-full flex-col gap-3 rounded-lg border py-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,s.jsx)(O.A,{className:"size-4"}),(0,s.jsxs)("p",{className:"",children:["Used tool: ",(0,s.jsx)("b",{children:t})]}),(0,s.jsx)("div",{className:"flex-grow"}),(0,s.jsx)(et,{onClick:()=>n(!l),children:l?(0,s.jsx)(eu.A,{}):(0,s.jsx)(ex.A,{})})]}),!l&&(0,s.jsxs)("div",{className:"flex flex-col gap-2 border-t pt-2",children:[(0,s.jsx)("div",{className:"px-4",children:(0,s.jsx)("pre",{className:"whitespace-pre-wrap",children:a})}),void 0!==i&&(0,s.jsxs)("div",{className:"border-t border-dashed px-4 pt-2",children:[(0,s.jsx)("p",{className:"font-semibold",children:"Result:"}),(0,s.jsx)("pre",{className:"whitespace-pre-wrap",children:"string"==typeof i?i:JSON.stringify(i,null,2)})]})]})]})},em=()=>(0,s.jsxs)(n.K,{className:"bg-background flex h-full flex-col",style:{"--thread-max-width":"48rem","--thread-padding-x":"1rem"},children:[(0,s.jsxs)(o.c,{className:"relative flex min-w-0 flex-1 flex-col gap-6 overflow-y-scroll",children:[(0,s.jsxs)("div",{className:"sticky top-2 z-10 mx-auto flex w-full max-w-[var(--thread-max-width)] justify-end gap-2 px-[var(--thread-padding-x)]",children:[(0,s.jsx)(d.A,{asChild:!0,children:(0,s.jsx)(et,{variant:"outline",size:"sm","aria-label":"New Chat",children:"New Chat"})}),(0,s.jsx)(c.v,{running:!0,children:(0,s.jsx)(u.W,{asChild:!0,children:(0,s.jsx)(et,{variant:"destructive",size:"sm","aria-label":"Stop generating",children:"Stop"})})})]}),(0,s.jsx)(eg,{}),(0,s.jsx)(x.eD,{components:{UserMessage:eN,EditComposer:eC,AssistantMessage:ew}}),(0,s.jsx)(c.v,{empty:!1,children:(0,s.jsx)(es.P.div,{className:"min-h-6 min-w-6 shrink-0"})})]}),(0,s.jsx)(ep,{})]}),ef=()=>(0,s.jsx)(h.P,{asChild:!0,children:(0,s.jsx)(ea,{tooltip:"Scroll to bottom",variant:"outline",className:"dark:bg-background dark:hover:bg-accent absolute -top-12 z-10 self-center rounded-full p-4 disabled:invisible",children:(0,s.jsx)(T.A,{})})}),eg=()=>(0,s.jsx)(m.R,{children:(0,s.jsx)("div",{className:"mx-auto flex w-full max-w-[var(--thread-max-width)] flex-grow flex-col px-[var(--thread-padding-x)]",children:(0,s.jsx)("div",{className:"flex w-full flex-grow flex-col items-center justify-center",children:(0,s.jsxs)("div",{className:"flex size-full flex-col justify-center px-8 md:mt-20",children:[(0,s.jsx)(es.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{delay:.5},className:"text-2xl font-semibold",children:"Hello there!"}),(0,s.jsx)(es.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{delay:.6},className:"text-muted-foreground/65 text-2xl",children:"How can I help you today?"})]})})})}),eb=()=>(0,s.jsx)("div",{className:"grid w-full gap-2 sm:grid-cols-2",children:[{title:"What are the advantages",label:"of using Assistant Cloud?",action:"What are the advantages of using Assistant Cloud?"},{title:"Write code to",label:"demonstrate topological sorting",action:"Write code to demonstrate topological sorting"},{title:"Help me write an essay",label:"about AI chat applications",action:"Help me write an essay about AI chat applications"},{title:"What is the weather",label:"in San Francisco?",action:"What is the weather in San Francisco?"}].map((e,t)=>(0,s.jsx)(es.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{delay:.05*t},className:"[&:nth-child(n+3)]:hidden sm:[&:nth-child(n+3)]:block",children:(0,s.jsx)(f.u,{prompt:e.action,method:"replace",autoSend:!0,asChild:!0,children:(0,s.jsxs)(et,{variant:"ghost",className:"dark:hover:bg-accent/60 h-auto w-full flex-1 flex-wrap items-start justify-start gap-1 rounded-xl border px-4 py-3.5 text-left text-sm sm:flex-col","aria-label":e.action,children:[(0,s.jsx)("span",{className:"font-medium",children:e.title}),(0,s.jsx)("p",{className:"text-muted-foreground",children:e.label})]})})},"suggested-action-".concat(e.title,"-").concat(t)))}),ep=()=>(0,s.jsxs)("div",{className:"bg-background relative mx-auto flex w-full max-w-[var(--thread-max-width)] flex-col gap-4 px-[var(--thread-padding-x)] pb-4 md:pb-6",children:[(0,s.jsx)(ef,{}),(0,s.jsx)(m.R,{children:(0,s.jsx)(eb,{})}),(0,s.jsxs)(g.U,{className:"focus-within::ring-offset-2 relative flex w-full flex-col rounded-2xl focus-within:ring-2 focus-within:ring-black dark:focus-within:ring-white",children:[(0,s.jsx)(b.e,{placeholder:"Send a message...",className:"bg-muted border-border dark:border-muted-foreground/15 focus:outline-primary placeholder:text-muted-foreground max-h-[calc(50dvh)] min-h-16 w-full resize-none rounded-t-2xl border-x border-t px-4 pt-2 pb-3 text-base outline-none",rows:1,autoFocus:!0,"aria-label":"Message input"}),(0,s.jsx)(ev,{})]})]}),ev=()=>(0,s.jsxs)("div",{className:"bg-muted border-border dark:border-muted-foreground/15 relative flex items-center justify-between rounded-b-2xl border-x border-b p-2",children:[(0,s.jsx)(ea,{tooltip:"Attach file",variant:"ghost",className:"hover:bg-foreground/15 dark:hover:bg-background/50 scale-115 p-3.5",onClick:()=>{console.log("Attachment clicked - not implemented")},children:(0,s.jsx)(E.A,{})}),(0,s.jsx)(c.v,{running:!1,children:(0,s.jsx)(p.y,{asChild:!0,children:(0,s.jsx)(et,{type:"submit",variant:"default",className:"dark:border-muted-foreground/90 border-muted-foreground/60 hover:bg-primary/75 size-8 rounded-full border","aria-label":"Send message",children:(0,s.jsx)(L.A,{className:"size-5"})})})}),(0,s.jsx)(c.v,{running:!0,children:(0,s.jsx)(u.W,{asChild:!0,children:(0,s.jsx)(et,{type:"button",variant:"default",className:"dark:border-muted-foreground/90 border-muted-foreground/60 hover:bg-primary/75 size-8 rounded-full border","aria-label":"Stop generating",children:(0,s.jsx)(U.A,{className:"size-3.5 fill-white dark:size-4 dark:fill-black"})})})})]}),ej=()=>(0,s.jsx)(v.z,{children:(0,s.jsx)(j.A,{className:"border-destructive bg-destructive/10 dark:bg-destructive/5 text-destructive mt-2 rounded-md border p-3 text-sm dark:text-red-200",children:(0,s.jsx)(w.D,{className:"line-clamp-2"})})}),ew=()=>(0,s.jsx)(y.X,{asChild:!0,children:(0,s.jsxs)(es.P.div,{className:"relative mx-auto grid w-full max-w-[var(--thread-max-width)] grid-cols-[auto_auto_1fr] grid-rows-[auto_1fr] px-[var(--thread-padding-x)] py-4",initial:{y:5,opacity:0},animate:{y:0,opacity:1},"data-role":"assistant",children:[(0,s.jsx)("div",{className:"ring-border bg-background col-start-1 row-start-1 flex size-8 shrink-0 items-center justify-center rounded-full ring-1",children:(0,s.jsx)(eS,{size:14})}),(0,s.jsxs)("div",{className:"text-foreground col-span-2 col-start-2 row-start-1 ml-4 leading-7 break-words",children:[(0,s.jsx)(N.r,{components:{Text:eo,tools:{Fallback:eh}}}),(0,s.jsx)(ej,{})]}),(0,s.jsx)(ey,{}),(0,s.jsx)(ez,{className:"col-start-2 row-start-2 mr-2 -ml-2"})]})}),ey=()=>(0,s.jsxs)(k.v,{hideWhenRunning:!0,autohide:"not-last",autohideFloat:"single-branch",className:"text-muted-foreground data-floating:bg-background col-start-3 row-start-2 mt-3 ml-3 flex gap-1 data-floating:absolute data-floating:mt-2 data-floating:rounded-md data-floating:border data-floating:p-1 data-floating:shadow-sm",children:[(0,s.jsx)(C.I,{asChild:!0,children:(0,s.jsxs)(ea,{tooltip:"Copy",children:[(0,s.jsx)(z.s,{copied:!0,children:(0,s.jsx)(O.A,{})}),(0,s.jsx)(z.s,{copied:!1,children:(0,s.jsx)(R.A,{})})]})}),(0,s.jsx)(S.i,{asChild:!0,children:(0,s.jsx)(ea,{tooltip:"Refresh",children:(0,s.jsx)(F.A,{})})})]}),eN=()=>(0,s.jsx)(y.X,{asChild:!0,children:(0,s.jsxs)(es.P.div,{className:"mx-auto grid w-full max-w-[var(--thread-max-width)] auto-rows-auto grid-cols-[minmax(72px,1fr)_auto] gap-y-1 px-[var(--thread-padding-x)] py-4 [&:where(>*)]:col-start-2",initial:{y:5,opacity:0},animate:{y:0,opacity:1},"data-role":"user",children:[(0,s.jsx)(ek,{}),(0,s.jsx)("div",{className:"bg-muted text-foreground col-start-2 rounded-3xl px-5 py-2.5 break-words",children:(0,s.jsx)(N.r,{components:{Text:eo}})}),(0,s.jsx)(ez,{className:"col-span-full col-start-1 row-start-3 -mr-1 justify-end"})]})}),ek=()=>(0,s.jsx)(k.v,{hideWhenRunning:!0,autohide:"not-last",className:"col-start-1 mt-2.5 mr-3 flex flex-col items-end",children:(0,s.jsx)(A.l,{asChild:!0,children:(0,s.jsx)(ea,{tooltip:"Edit",children:(0,s.jsx)(H.A,{})})})}),eC=()=>(0,s.jsx)("div",{className:"mx-auto flex w-full max-w-[var(--thread-max-width)] flex-col gap-4 px-[var(--thread-padding-x)]",children:(0,s.jsxs)(g.U,{className:"bg-muted ml-auto flex w-full max-w-7/8 flex-col rounded-xl",children:[(0,s.jsx)(b.e,{className:"text-foreground flex min-h-[60px] w-full resize-none bg-transparent p-4 outline-none",autoFocus:!0}),(0,s.jsxs)("div",{className:"mx-3 mb-3 flex items-center justify-center gap-2 self-end",children:[(0,s.jsx)(u.W,{asChild:!0,children:(0,s.jsx)(et,{variant:"ghost",size:"sm","aria-label":"Cancel edit",children:"Cancel"})}),(0,s.jsx)(p.y,{asChild:!0,children:(0,s.jsx)(et,{size:"sm","aria-label":"Update message",children:"Update"})})]})]})}),ez=e=>{let{className:t,...a}=e;return(0,s.jsxs)(_.Y,{hideWhenSingleBranch:!0,className:K("text-muted-foreground inline-flex items-center text-xs",t),...a,children:[(0,s.jsx)(W.v,{asChild:!0,children:(0,s.jsx)(ea,{tooltip:"Previous",children:(0,s.jsx)(M.A,{})})}),(0,s.jsxs)("span",{className:"font-medium",children:[(0,s.jsx)(D.l,{})," / ",(0,s.jsx)(I.h,{})]}),(0,s.jsx)(P.H,{asChild:!0,children:(0,s.jsx)(ea,{tooltip:"Next",children:(0,s.jsx)(J.A,{})})})]})},eS=e=>{let{size:t=14}=e;return(0,s.jsx)("svg",{width:t,height:t,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M8 0L9.79611 6.20389L16 8L9.79611 9.79611L8 16L6.20389 9.79611L0 8L6.20389 6.20389L8 0Z",fill:"currentColor"})})};var eA=a(2432),e_=a(7489);function eW(e){let{className:t,orientation:a="horizontal",decorative:r=!0,...i}=e;return(0,s.jsx)(e_.b,{"data-slot":"separator-root",decorative:r,orientation:a,className:K("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...i})}var eD=a(3651),eI=a(4416);function eP(e){let{...t}=e;return(0,s.jsx)(eD.bL,{"data-slot":"sheet",...t})}function eT(e){let{...t}=e;return(0,s.jsx)(eD.ZL,{"data-slot":"sheet-portal",...t})}function eE(e){let{className:t,...a}=e;return(0,s.jsx)(eD.hJ,{"data-slot":"sheet-overlay",className:K("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function eL(e){let{className:t,children:a,side:r="right",...i}=e;return(0,s.jsxs)(eT,{children:[(0,s.jsx)(eE,{}),(0,s.jsxs)(eD.UC,{"data-slot":"sheet-content",className:K("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===r&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===r&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===r&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===r&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...i,children:[a,(0,s.jsxs)(eD.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,s.jsx)(eI.A,{className:"size-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function eU(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sheet-header",className:K("flex flex-col gap-1.5 p-4",t),...a})}function eO(e){let{className:t,...a}=e;return(0,s.jsx)(eD.hE,{"data-slot":"sheet-title",className:K("text-foreground font-semibold",t),...a})}function eR(e){let{className:t,...a}=e;return(0,s.jsx)(eD.VY,{"data-slot":"sheet-description",className:K("text-muted-foreground text-sm",t),...a})}let eF=r.createContext(null);function eH(){let e=r.useContext(eF);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function eM(e){let{defaultOpen:t=!0,open:a,onOpenChange:i,className:l,style:n,children:o,...d}=e,c=function(){let[e,t]=r.useState(void 0);return r.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),a=()=>{t(window.innerWidth<768)};return e.addEventListener("change",a),t(window.innerWidth<768),()=>e.removeEventListener("change",a)},[]),!!e}(),[u,x]=r.useState(!1),[h,m]=r.useState(t),f=null!=a?a:h,g=r.useCallback(e=>{let t="function"==typeof e?e(f):e;i?i(t):m(t),document.cookie="".concat("sidebar_state","=").concat(t,"; path=/; max-age=").concat(604800)},[i,f]),b=r.useCallback(()=>c?x(e=>!e):g(e=>!e),[c,g,x]);r.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),b())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[b]);let p=f?"expanded":"collapsed",v=r.useMemo(()=>({state:p,open:f,setOpen:g,isMobile:c,openMobile:u,setOpenMobile:x,toggleSidebar:b}),[p,f,g,c,u,x,b]);return(0,s.jsx)(eF.Provider,{value:v,children:(0,s.jsx)(V,{delayDuration:0,children:(0,s.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...n},className:K("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",l),...d,children:o})})})}function eJ(e){let{side:t="left",variant:a="sidebar",collapsible:r="offcanvas",className:i,children:l,...n}=e,{isMobile:o,state:d,openMobile:c,setOpenMobile:u}=eH();return"none"===r?(0,s.jsx)("div",{"data-slot":"sidebar",className:K("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",i),...n,children:l}):o?(0,s.jsx)(eP,{open:c,onOpenChange:u,...n,children:(0,s.jsxs)(eL,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:[(0,s.jsxs)(eU,{className:"sr-only",children:[(0,s.jsx)(eO,{children:"Sidebar"}),(0,s.jsx)(eR,{children:"Displays the mobile sidebar."})]}),(0,s.jsx)("div",{className:"flex h-full w-full flex-col",children:l})]})}):(0,s.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":d,"data-collapsible":"collapsed"===d?r:"","data-variant":a,"data-side":t,"data-slot":"sidebar",children:[(0,s.jsx)("div",{"data-slot":"sidebar-gap",className:K("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===a||"inset"===a?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,s.jsx)("div",{"data-slot":"sidebar-container",className:K("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===a||"inset"===a?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",i),...n,children:(0,s.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:l})})]})}function eX(e){let{className:t,onClick:a,...r}=e,{toggleSidebar:i}=eH();return(0,s.jsxs)(et,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:K("size-7",t),onClick:e=>{null==a||a(e),i()},...r,children:[(0,s.jsx)(eA.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function eq(e){let{className:t,...a}=e,{toggleSidebar:r}=eH();return(0,s.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:K("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",t),...a})}function eB(e){let{className:t,...a}=e;return(0,s.jsx)("main",{"data-slot":"sidebar-inset",className:K("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",t),...a})}function eK(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:K("flex flex-col gap-2 p-2",t),...a})}function eV(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:K("flex flex-col gap-2 p-2",t),...a})}function eY(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:K("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...a})}function eZ(e){let{className:t,...a}=e;return(0,s.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:K("flex w-full min-w-0 flex-col gap-1",t),...a})}function eG(e){let{className:t,...a}=e;return(0,s.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:K("group/menu-item relative",t),...a})}let e$=(0,Q.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function eQ(e){let{asChild:t=!1,isActive:a=!1,variant:r="default",size:i="default",tooltip:l,className:n,...o}=e,d=t?$.DX:"button",{isMobile:c,state:u}=eH(),x=(0,s.jsx)(d,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":i,"data-active":a,className:K(e$({variant:r,size:i}),n),...o});return l?("string"==typeof l&&(l={children:l}),(0,s.jsxs)(Y,{children:[(0,s.jsx)(Z,{asChild:!0,children:x}),(0,s.jsx)(G,{side:"right",align:"center",hidden:"collapsed"!==u||c,...l})]})):x}var e0=a(8804),e2=a(9099),e1=a(6874),e5=a.n(e1),e4=a(1604),e3=a(100),e8=a(5586),e6=a(8494),e9=a(3932),e7=a(2724),te=a(9022);let tt=()=>(0,s.jsxs)(e4.C,{className:"flex flex-col items-stretch gap-1.5",children:[(0,s.jsx)(ta,{}),(0,s.jsx)(ts,{})]}),ta=()=>(0,s.jsx)(d.A,{asChild:!0,children:(0,s.jsxs)(et,{className:"data-active:bg-muted hover:bg-muted flex items-center justify-start gap-1 rounded-lg px-2.5 py-2 text-start",variant:"ghost",children:[(0,s.jsx)(E.A,{}),"New Thread"]})}),ts=()=>(0,s.jsx)(e3.g,{components:{ThreadListItem:tr}}),tr=()=>(0,s.jsxs)(e8.P,{className:"data-active:bg-muted hover:bg-muted focus-visible:bg-muted focus-visible:ring-ring flex items-center gap-2 rounded-lg transition-all focus-visible:outline-none focus-visible:ring-2",children:[(0,s.jsx)(e6.r,{className:"flex-grow px-3 py-2 text-start",children:(0,s.jsx)(ti,{})}),(0,s.jsx)(tl,{})]}),ti=()=>(0,s.jsx)("p",{className:"text-sm",children:(0,s.jsx)(e9.T,{fallback:"New Chat"})}),tl=()=>(0,s.jsx)(e7.L,{asChild:!0,children:(0,s.jsx)(ea,{className:"hover:text-foreground/60 p-4 text-foreground ml-auto mr-1 size-4",variant:"ghost",tooltip:"Archive thread",children:(0,s.jsx)(te.A,{})})});function tn(e){let{...t}=e;return(0,s.jsxs)(eJ,{...t,children:[(0,s.jsx)(eK,{children:(0,s.jsx)(eZ,{children:(0,s.jsx)(eG,{children:(0,s.jsx)(eQ,{size:"lg",asChild:!0,children:(0,s.jsxs)(e5(),{href:"https://assistant-ui.com",target:"_blank",children:[(0,s.jsx)("div",{className:"flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground",children:(0,s.jsx)(e0.A,{className:"size-4"})}),(0,s.jsx)("div",{className:"flex flex-col gap-0.5 leading-none",children:(0,s.jsx)("span",{className:"font-semibold",children:"assistant-ui"})})]})})})})}),(0,s.jsx)(eY,{children:(0,s.jsx)(tt,{})}),(0,s.jsx)(eq,{}),(0,s.jsx)(eV,{children:(0,s.jsx)(eZ,{children:(0,s.jsx)(eG,{children:(0,s.jsx)(eQ,{size:"lg",asChild:!0,children:(0,s.jsxs)(e5(),{href:"https://github.com/assistant-ui/assistant-ui",target:"_blank",children:[(0,s.jsx)("div",{className:"flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground",children:(0,s.jsx)(e2.A,{className:"size-4"})}),(0,s.jsxs)("div",{className:"flex flex-col gap-0.5 leading-none",children:[(0,s.jsx)("span",{className:"font-semibold",children:"GitHub"}),(0,s.jsx)("span",{className:"",children:"View Source"})]})]})})})})})]})}function to(e){let{...t}=e;return(0,s.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...t})}function td(e){let{className:t,...a}=e;return(0,s.jsx)("ol",{"data-slot":"breadcrumb-list",className:K("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",t),...a})}function tc(e){let{className:t,...a}=e;return(0,s.jsx)("li",{"data-slot":"breadcrumb-item",className:K("inline-flex items-center gap-1.5",t),...a})}function tu(e){let{asChild:t,className:a,...r}=e,i=t?$.DX:"a";return(0,s.jsx)(i,{"data-slot":"breadcrumb-link",className:K("hover:text-foreground transition-colors",a),...r})}function tx(e){let{className:t,...a}=e;return(0,s.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:K("text-foreground font-normal",t),...a})}function th(e){let{children:t,className:a,...r}=e;return(0,s.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:K("[&>svg]:size-3.5",a),...r,children:null!=t?t:(0,s.jsx)(J.A,{})})}class tm{connectWebSocket(){try{this.ws=new WebSocket(this.config.wsUrl),this.ws.onopen=()=>{console.log("Connected to backend WebSocket")},this.ws.onmessage=e=>{try{let t=JSON.parse(e.data);this.handleWebSocketMessage(t)}catch(e){console.error("Error parsing WebSocket message:",e)}},this.ws.onclose=()=>{console.log("WebSocket connection closed"),this.ws&&setTimeout(()=>this.connectWebSocket(),3e3)},this.ws.onerror=e=>{console.error("WebSocket error:",e)}}catch(e){console.error("Failed to connect WebSocket:",e),setTimeout(()=>this.connectWebSocket(),5e3)}}handleWebSocketMessage(e){var t,a,s,r;switch(e.type){case"device_status":this.deviceConnected=e.connected,this.deviceInfo=e.deviceInfo,null==(t=(a=this.config).onDeviceStatusChange)||t.call(a,this.deviceConnected,this.deviceInfo);break;case"images":e.images&&Array.isArray(e.images)&&(this.currentImages=e.images,null==(s=(r=this.config).onImagesReceived)||s.call(r,e.images),console.log("Received ".concat(e.images.length," images")));break;case"error":console.error("Device error:",e.message);break;default:console.log("Unknown message type:",e.type)}}sendDeviceCommand(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.ws&&this.ws.readyState===WebSocket.OPEN)this.ws.send(JSON.stringify({type:e,...t}));else throw Error("Not connected to backend")}captureImages(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.sendDeviceCommand("capture",{count:e})}sendToDevice(){this.sendDeviceCommand("send")}getDeviceStatus(){return{connected:this.deviceConnected,deviceInfo:this.deviceInfo}}getCurrentImages(){return this.currentImages}createAdapter(){return{async *run(e){let{messages:t,abortSignal:a}=e;try{var s;let e,r=t.findLast(e=>"user"===e.role),i=[],l="";if(r){if("string"==typeof r.content)l=r.content;else if(Array.isArray(r.content)){for(let e of r.content)if("text"===e.type)l+=e.text;else if("image"===e.type){let t=e.image.replace(/^data:image\/[a-z]+;base64,/,"");i.push(t)}}}let n=i.length>0?i:this.currentImages||[];if(!n||0===n.length)return void(yield{content:[{type:"text",text:"No images are available for analysis. Please capture some images first using the 'Capture' button, or ensure your device is connected."}]});try{e=await fetch("".concat(this.config.backendUrl,"/api/chat"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({images:n,context:l.trim()}),signal:a})}catch(e){yield{content:[{type:"text",text:"Unable to connect to the backend server at ".concat(this.config.backendUrl,". Please ensure the backend is running and accessible.")}]};return}if(!e.ok)return void(yield{content:[{type:"text",text:"Backend server error (".concat(e.status,"): ").concat(e.statusText,". Please check the backend logs.")}]});let o=null==(s=e.body)?void 0:s.getReader(),d=new TextDecoder;if(!o)throw Error("No response body");let c="";for(;;){let{done:e,value:t}=await o.read();if(e)break;for(let e of d.decode(t).split("\n"))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)return;try{let e=JSON.parse(t);if(e.text)c+=e.text,yield{content:[{type:"text",text:c}]};else if(e.error)throw Error(e.error)}catch(e){}}}}catch(e){console.error("Analysis error:",e),yield{content:[{type:"text",text:"An error occurred during analysis: ".concat(e instanceof Error?e.message:"Unknown error",". Please try again.")}]}}}}}destroy(){this.ws&&(this.ws.close(),this.ws=null)}constructor(e){this.ws=null,this.deviceConnected=!1,this.deviceInfo=null,this.currentImages=[],this.config=e,this.connectWebSocket()}}let tf=e=>{let{images:t,className:a=""}=e;return 0===t.length?null:(0,s.jsxs)("div",{className:"backdrop-blur-md bg-black/70 text-white p-3 rounded-xl shadow-xl ".concat(a),children:[(0,s.jsx)("h3",{className:"m-0 mb-2 text-sm text-slate-300",children:"Captured Images"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[t.slice(0,3).map((e,t)=>(0,s.jsx)("img",{src:"data:image/jpeg;base64,".concat(e),alt:"Capture ".concat(t+1),className:"w-14 h-14 object-cover rounded-md border border-white/20"},t)),t.length>3&&(0,s.jsxs)("div",{className:"text-slate-300 text-sm px-2",children:["+",t.length-3," more"]})]})]})},tg=()=>{let[e,t]=(0,r.useState)(!1),[a,n]=(0,r.useState)(null),[o,d]=(0,r.useState)([]),[c,u]=(0,r.useState)(null);(0,r.useEffect)(()=>{let e=new tm({backendUrl:"http://localhost:8080",wsUrl:"ws://localhost:8081",onDeviceStatusChange:(e,a)=>{t(e),n(a)},onImagesReceived:e=>d(e)});return u(e),()=>e.destroy()},[]);let x=(0,r.useMemo)(()=>({async *run(){yield{content:[{type:"text",text:"Initializing..."}]}}}),[]),h=(0,i.v)(c?c.createAdapter():x);return(0,s.jsx)(l.o,{runtime:h,children:(0,s.jsx)(eM,{children:(0,s.jsxs)("div",{className:"flex h-dvh w-full pr-0.5",children:[(0,s.jsx)(tn,{}),(0,s.jsxs)(eB,{children:[(0,s.jsxs)("header",{className:"flex h-16 shrink-0 items-center gap-2 border-b px-4",children:[(0,s.jsx)(eX,{}),(0,s.jsx)(eW,{orientation:"vertical",className:"mr-2 h-4"}),(0,s.jsx)(to,{children:(0,s.jsxs)(td,{children:[(0,s.jsx)(tc,{className:"hidden md:block",children:(0,s.jsx)(tu,{href:"https://www.assistant-ui.com/docs/getting-started",target:"_blank",rel:"noopener noreferrer",children:"Build Your Own ChatGPT UX"})}),(0,s.jsx)(th,{className:"hidden md:block"}),(0,s.jsx)(tc,{children:(0,s.jsx)(tx,{children:"Starter Template"})})]})}),(0,s.jsxs)("div",{className:"ml-auto rounded-full px-3 py-1 text-sm border flex items-center gap-2",children:[(0,s.jsx)("span",{className:"inline-block size-2 rounded-full ".concat(e?"bg-green-500":"bg-red-500")}),(0,s.jsx)("span",{children:e?"Device Connected":"Device Disconnected"}),a&&(0,s.jsxs)("span",{className:"text-muted-foreground",children:["(",a.name," - ",a.host,":",a.port,")"]})]})]}),(0,s.jsxs)("div",{className:"relative flex-1 overflow-hidden",children:[(0,s.jsxs)("div",{className:"pointer-events-none absolute inset-0 z-10",children:[(0,s.jsx)("div",{className:"pointer-events-auto absolute left-4 top-4 flex gap-2",children:(0,s.jsx)("button",{onClick:()=>null==c?void 0:c.captureImages(1),disabled:!e,className:"rounded-md bg-emerald-600 text-white px-3 py-2 text-sm shadow hover:bg-emerald-500 disabled:opacity-50",children:"Capture"})}),(0,s.jsx)("div",{className:"pointer-events-auto absolute right-4 top-4 flex gap-2",children:(0,s.jsx)("button",{onClick:()=>null==c?void 0:c.sendToDevice(),disabled:!e,className:"rounded-md bg-amber-600 text-white px-3 py-2 text-sm shadow hover:bg-amber-500 disabled:opacity-50",children:"Send"})}),(0,s.jsx)("div",{className:"pointer-events-auto absolute left-4 bottom-4",children:(0,s.jsx)(tf,{images:o})})]}),(0,s.jsx)(em,{})]})]})]})})})}},7355:(e,t,a)=>{Promise.resolve().then(a.bind(a,3355))}},e=>{e.O(0,[150,411,441,964,358],()=>e(e.s=7355)),_N_E=e.O()}]);