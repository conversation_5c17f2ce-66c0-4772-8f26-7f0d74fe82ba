globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/chat/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"894":{"*":{"id":"6346","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"7173","name":"*","chunks":[],"async":false}},"3355":{"*":{"id":"405","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"8827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"7924","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"99","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"8243","name":"*","chunks":[],"async":false}},"8175":{"*":{"id":"5587","name":"*","chunks":[],"async":false}},"8393":{"*":{"id":"5227","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"2763","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/client/components/builtin/global-error.js":{"id":8393,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/esm/client/components/builtin/global-error.js":{"id":8393,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/client/components/client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/esm/client/components/client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/client/components/client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/esm/client/components/client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/client/components/layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/esm/client/components/layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/client/components/render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"id":8175,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/dist/esm/lib/metadata/generate/icon-mark.js":{"id":8175,"name":"*","chunks":[],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":5688,"name":"*","chunks":["177","static/chunks/app/layout-afec0725fa483857.js"],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":9432,"name":"*","chunks":["177","static/chunks/app/layout-afec0725fa483857.js"],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/app/globals.css":{"id":9324,"name":"*","chunks":["177","static/chunks/app/layout-afec0725fa483857.js"],"async":false},"/Users/<USER>/work/Instrument/frontend-assistant-ui/app/assistant.tsx":{"id":3355,"name":"*","chunks":["411","static/chunks/411-e366dfd3d8ad071d.js","974","static/chunks/app/page-efb8ef792e98cfd3.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/work/Instrument/frontend-assistant-ui/":[],"/Users/<USER>/work/Instrument/frontend-assistant-ui/app/layout":[{"inlined":false,"path":"static/css/9cfdc6f99700562a.css"}],"/Users/<USER>/work/Instrument/frontend-assistant-ui/app/page":[{"inlined":false,"path":"static/css/ffacb3b6719cc2cf.css"}],"/Users/<USER>/work/Instrument/frontend-assistant-ui/app/api/chat/route":[]},"rscModuleMapping":{"894":{"*":{"id":"6444","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"1307","name":"*","chunks":[],"async":false}},"3355":{"*":{"id":"4086","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"2089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"6042","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"9477","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"9345","name":"*","chunks":[],"async":false}},"8175":{"*":{"id":"4817","name":"*","chunks":[],"async":false}},"8393":{"*":{"id":"6133","name":"*","chunks":[],"async":false}},"9324":{"*":{"id":"2704","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"6577","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}