[{"/Users/<USER>/work/Instrument/frontend-assistant-ui/app/api/chat/route.ts": "1", "/Users/<USER>/work/Instrument/frontend-assistant-ui/app/assistant.tsx": "2", "/Users/<USER>/work/Instrument/frontend-assistant-ui/app/layout.tsx": "3", "/Users/<USER>/work/Instrument/frontend-assistant-ui/app/page.tsx": "4", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/app-sidebar.tsx": "5", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/image-preview.tsx": "6", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/markdown-text.tsx": "7", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/thread-list.tsx": "8", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/thread.tsx": "9", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/tool-fallback.tsx": "10", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/tooltip-icon-button.tsx": "11", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/breadcrumb.tsx": "12", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/button.tsx": "13", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/input.tsx": "14", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/separator.tsx": "15", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/sheet.tsx": "16", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/sidebar.tsx": "17", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/skeleton.tsx": "18", "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/tooltip.tsx": "19", "/Users/<USER>/work/Instrument/frontend-assistant-ui/lib/instrument-runtime.ts": "20", "/Users/<USER>/work/Instrument/frontend-assistant-ui/lib/utils.ts": "21"}, {"size": 389, "mtime": 1754963035000, "results": "22", "hashOfConfig": "23"}, {"size": 5212, "mtime": 1755760349353, "results": "24", "hashOfConfig": "23"}, {"size": 702, "mtime": 1754963035000, "results": "25", "hashOfConfig": "23"}, {"size": 101, "mtime": 1754963035000, "results": "26", "hashOfConfig": "23"}, {"size": 2110, "mtime": 1754963035000, "results": "27", "hashOfConfig": "23"}, {"size": 919, "mtime": 1755711109476, "results": "28", "hashOfConfig": "23"}, {"size": 4910, "mtime": 1754963035000, "results": "29", "hashOfConfig": "23"}, {"size": 2007, "mtime": 1754963035000, "results": "30", "hashOfConfig": "23"}, {"size": 15438, "mtime": 1755715905958, "results": "31", "hashOfConfig": "23"}, {"size": 1459, "mtime": 1754963035000, "results": "32", "hashOfConfig": "23"}, {"size": 1135, "mtime": 1754963035000, "results": "33", "hashOfConfig": "23"}, {"size": 2357, "mtime": 1754963035000, "results": "34", "hashOfConfig": "23"}, {"size": 2132, "mtime": 1754963035000, "results": "35", "hashOfConfig": "23"}, {"size": 967, "mtime": 1754963035000, "results": "36", "hashOfConfig": "23"}, {"size": 704, "mtime": 1754963035000, "results": "37", "hashOfConfig": "23"}, {"size": 4090, "mtime": 1754963035000, "results": "38", "hashOfConfig": "23"}, {"size": 21633, "mtime": 1754963035000, "results": "39", "hashOfConfig": "23"}, {"size": 276, "mtime": 1754963035000, "results": "40", "hashOfConfig": "23"}, {"size": 1900, "mtime": 1754963035000, "results": "41", "hashOfConfig": "23"}, {"size": 8043, "mtime": 1755760403505, "results": "42", "hashOfConfig": "23"}, {"size": 169, "mtime": 1754963035000, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1hp1n61", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/work/Instrument/frontend-assistant-ui/app/api/chat/route.ts", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/app/assistant.tsx", ["107", "108"], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/app/layout.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/app/page.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/app-sidebar.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/image-preview.tsx", ["109"], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/markdown-text.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/thread-list.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/thread.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/tool-fallback.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/tooltip-icon-button.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/breadcrumb.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/button.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/input.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/separator.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/sheet.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/sidebar.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/skeleton.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/tooltip.tsx", [], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/lib/instrument-runtime.ts", ["110", "111", "112", "113"], [], "/Users/<USER>/work/Instrument/frontend-assistant-ui/lib/utils.ts", [], [], {"ruleId": "114", "severity": 2, "message": "115", "line": 55, "column": 75, "nodeType": "116", "messageId": "117", "endLine": 55, "endColumn": 78, "suggestions": "118"}, {"ruleId": "114", "severity": 2, "message": "115", "line": 63, "column": 80, "nodeType": "116", "messageId": "117", "endLine": 63, "endColumn": 83, "suggestions": "119"}, {"ruleId": "120", "severity": 1, "message": "121", "line": 16, "column": 11, "nodeType": "122", "endLine": 21, "endColumn": 13}, {"ruleId": "114", "severity": 2, "message": "115", "line": 67, "column": 40, "nodeType": "116", "messageId": "117", "endLine": 67, "endColumn": 43, "suggestions": "123"}, {"ruleId": "114", "severity": 2, "message": "115", "line": 93, "column": 53, "nodeType": "116", "messageId": "117", "endLine": 93, "endColumn": 56, "suggestions": "124"}, {"ruleId": "125", "severity": 1, "message": "126", "line": 183, "column": 20, "nodeType": null, "messageId": "127", "endLine": 183, "endColumn": 30}, {"ruleId": "125", "severity": 1, "message": "128", "line": 245, "column": 26, "nodeType": null, "messageId": "127", "endLine": 245, "endColumn": 36}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["129", "130"], ["131", "132"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["133", "134"], ["135", "136"], "@typescript-eslint/no-unused-vars", "'fetchError' is defined but never used.", "unusedVar", "'parseError' is defined but never used.", {"messageId": "137", "fix": "138", "desc": "139"}, {"messageId": "140", "fix": "141", "desc": "142"}, {"messageId": "137", "fix": "143", "desc": "139"}, {"messageId": "140", "fix": "144", "desc": "142"}, {"messageId": "137", "fix": "145", "desc": "139"}, {"messageId": "140", "fix": "146", "desc": "142"}, {"messageId": "137", "fix": "147", "desc": "139"}, {"messageId": "140", "fix": "148", "desc": "142"}, "suggestUnknown", {"range": "149", "text": "150"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "151", "text": "152"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "153", "text": "150"}, {"range": "154", "text": "152"}, {"range": "155", "text": "150"}, {"range": "156", "text": "152"}, {"range": "157", "text": "150"}, {"range": "158", "text": "152"}, [1842, 1845], "unknown", [1842, 1845], "never", [2043, 2046], [2043, 2046], [1828, 1831], [1828, 1831], [2611, 2614], [2611, 2614]]