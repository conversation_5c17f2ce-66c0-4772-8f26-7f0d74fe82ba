import UIKit
import AVFoundation

class ViewController: UIViewController {

    // MARK: - UI Elements (created programmatically)
    private var previewView: UIView!
    private var captureButton: UIButton!
    private var statusLabel: UILabel!
    
    // MARK: - Properties
    private var cameraManager: CameraManager!
    private var networkManager: NetworkManager!
    private var imageBatchManager: ImageBatchManager!
    
    private var isCapturing = false
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupManagers()
        setupUI()
        requestCameraPermission()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        startServices()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        stopServices()
    }
    
    // MARK: - Setup Methods
    private func setupManagers() {
        // Initialize managers
        cameraManager = CameraManager()
        networkManager = NetworkManager()
        imageBatchManager = ImageBatchManager()
        
        // Set delegates
        cameraManager.delegate = self
        networkManager.delegate = self
        imageBatchManager.delegate = self
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground

        // Create preview view
        previewView = UIView()
        previewView.backgroundColor = .black
        previewView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(previewView)

        // Create capture button
        captureButton = UIButton(type: .system)
        captureButton.setTitle("📸", for: .normal)
        captureButton.titleLabel?.font = UIFont.systemFont(ofSize: 30)
        captureButton.backgroundColor = .systemBlue
        captureButton.layer.cornerRadius = 25
        captureButton.translatesAutoresizingMaskIntoConstraints = false
        captureButton.addTarget(self, action: #selector(captureButtonTapped(_:)), for: .touchUpInside)
        view.addSubview(captureButton)

        // Create status label
        statusLabel = UILabel()
        statusLabel.text = "Ready"
        statusLabel.textAlignment = .center
        statusLabel.font = UIFont.systemFont(ofSize: 17)
        statusLabel.textColor = .label
        statusLabel.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(statusLabel)

        // Setup constraints
        setupConstraints()

        // Initial UI state
        updateUI()
    }

    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // Preview view constraints
            previewView.leadingAnchor.constraint(equalTo: view.safeAreaLayoutGuide.leadingAnchor, constant: 16),
            previewView.trailingAnchor.constraint(equalTo: view.safeAreaLayoutGuide.trailingAnchor, constant: -16),
            previewView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 16),
            previewView.heightAnchor.constraint(equalToConstant: 400),

            // Capture button constraints
            captureButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            captureButton.topAnchor.constraint(equalTo: previewView.bottomAnchor, constant: 20),
            captureButton.widthAnchor.constraint(equalToConstant: 50),
            captureButton.heightAnchor.constraint(equalToConstant: 50),

            // Status label constraints
            statusLabel.leadingAnchor.constraint(equalTo: view.safeAreaLayoutGuide.leadingAnchor, constant: 16),
            statusLabel.trailingAnchor.constraint(equalTo: view.safeAreaLayoutGuide.trailingAnchor, constant: -16),
            statusLabel.topAnchor.constraint(equalTo: captureButton.bottomAnchor, constant: 20)
        ])
    }
    
    private func requestCameraPermission() {
        AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
            DispatchQueue.main.async {
                if granted {
                    self?.setupCameraPreview()
                } else {
                    self?.showCameraPermissionAlert()
                }
            }
        }
    }
    
    private func setupCameraPreview() {
        guard let previewLayer = cameraManager.getPreviewLayer() else { return }
        
        previewLayer.frame = previewView.bounds
        previewView.layer.addSublayer(previewLayer)
        
        // Add tap to focus gesture
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(focusTapped(_:)))
        previewView.addGestureRecognizer(tapGesture)
    }
    
    private func startServices() {
        cameraManager.startSession()
        networkManager.startService()
        
        // Cleanup old batches
        imageBatchManager.cleanupOldBatches()
    }
    
    private func stopServices() {
        cameraManager.stopSession()
        networkManager.stopService()
    }
    
    // MARK: - UI Updates
    private func updateUI() {
        let batchCount = imageBatchManager.getCurrentBatchCount()
        // batchCountLabel.text = "Batch: \(batchCount) images"  // Temporarily commented
        
        // sendButton.isEnabled = batchCount > 0  // Temporarily commented
        // clearBatchButton.isEnabled = batchCount > 0  // Temporarily commented
        
        captureButton.isEnabled = !isCapturing
        
        // Update status label with batch info for now
        statusLabel.text = isCapturing ? "Capturing..." : "Ready - Batch: \(batchCount) images"
    }
    
    private func updateConnectionStatus(_ connected: Bool) {
        // connectionStatusView.backgroundColor = connected ? .systemGreen : .systemRed  // Temporarily commented
        statusLabel.text = connected ? "Connected to MacBook" : "Searching for MacBook..."
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
    
    private func showCameraPermissionAlert() {
        let alert = UIAlertController(
            title: "Camera Permission Required",
            message: "This app needs camera access to capture screen content. Please enable camera access in Settings.",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Settings", style: .default) { _ in
            if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsURL)
            }
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        present(alert, animated: true)
    }
    
    // MARK: - Actions
    @objc func captureButtonTapped(_ sender: UIButton) {
        guard !isCapturing else { return }
        
        isCapturing = true
        updateUI()
        
        // Visual feedback
        UIView.animate(withDuration: 0.1, animations: {
            self.captureButton.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.captureButton.transform = .identity
            }
        }
        
        cameraManager.capturePhoto()
    }
    
    // Temporarily commented out actions:
    /*
    @IBAction func sendButtonTapped(_ sender: UIButton) {
        let imageData = imageBatchManager.getCompressedImageData()
        networkManager.sendImages(imageData)
        
        // Visual feedback
        sender.backgroundColor = .systemOrange
        UIView.animate(withDuration: 0.5) {
            sender.backgroundColor = .systemGreen
        }
        
        statusLabel.text = "Sent \(imageData.count) images"
    }
    
    @IBAction func clearBatchButtonTapped(_ sender: UIButton) {
        let alert = UIAlertController(
            title: "Clear Batch",
            message: "Are you sure you want to clear all \(imageBatchManager.getCurrentBatchCount()) images?",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Clear", style: .destructive) { _ in
            self.imageBatchManager.clearCurrentBatch()
            self.statusLabel.text = "Batch cleared"
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        present(alert, animated: true)
    }
    
    @IBAction func zoomSliderChanged(_ sender: UISlider) {
        cameraManager.setZoomFactor(CGFloat(sender.value))
    }
    */
    
    @objc private func focusTapped(_ gesture: UITapGestureRecognizer) {
        let point = gesture.location(in: previewView)
        
        // Add focus animation
        let focusView = UIView(frame: CGRect(x: 0, y: 0, width: 80, height: 80))
        focusView.center = point
        focusView.layer.borderColor = UIColor.yellow.cgColor
        focusView.layer.borderWidth = 2
        focusView.layer.cornerRadius = 40
        focusView.alpha = 0
        
        previewView.addSubview(focusView)
        
        UIView.animate(withDuration: 0.3, animations: {
            focusView.alpha = 1
            focusView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        }) { _ in
            UIView.animate(withDuration: 0.5, animations: {
                focusView.alpha = 0
            }) { _ in
                focusView.removeFromSuperview()
            }
        }
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // Update preview layer frame
        if let previewLayer = cameraManager.getPreviewLayer() {
            previewLayer.frame = previewView.bounds
        }
    }
}

// MARK: - CameraManagerDelegate
extension ViewController: CameraManagerDelegate {
    func cameraManager(_ manager: CameraManager, didCaptureImage image: UIImage) {
        DispatchQueue.main.async {
            self.isCapturing = false
            
            // Add metadata - temporarily removed zoomSlider reference
            let metadata: [String: Any] = [
                "zoomFactor": 0.5, // self.zoomSlider.value,  // Temporarily hardcoded
                "timestamp": ISO8601DateFormatter().string(from: Date())
            ]
            
            self.imageBatchManager.addImage(image, metadata: metadata)
            self.updateUI()
            
            // Visual feedback
            self.view.backgroundColor = .white
            UIView.animate(withDuration: 0.1) {
                self.view.backgroundColor = .systemBackground
            }
            
            self.statusLabel.text = "Image captured"
        }
    }
    
    func cameraManager(_ manager: CameraManager, didFailWithError error: Error) {
        DispatchQueue.main.async {
            self.isCapturing = false
            self.updateUI()
            self.showAlert(title: "Camera Error", message: error.localizedDescription)
        }
    }
    
    func cameraManagerDidStartSession(_ manager: CameraManager) {
        DispatchQueue.main.async {
            self.statusLabel.text = "Camera ready"
        }
    }
    
    func cameraManagerDidStopSession(_ manager: CameraManager) {
        DispatchQueue.main.async {
            self.statusLabel.text = "Camera stopped"
        }
    }
}

// MARK: - NetworkManagerDelegate
extension ViewController: NetworkManagerDelegate {
    func networkManager(_ manager: NetworkManager, didReceiveCommand command: NetworkCommand) {
        DispatchQueue.main.async {
            self.handleNetworkCommand(command)
        }
    }
    
    func networkManager(_ manager: NetworkManager, didChangeConnectionStatus connected: Bool) {
        DispatchQueue.main.async {
            self.updateConnectionStatus(connected)
        }
    }
    
    func networkManager(_ manager: NetworkManager, didFailWithError error: Error) {
        DispatchQueue.main.async {
            self.showAlert(title: "Network Error", message: error.localizedDescription)
        }
    }
    
    private func handleNetworkCommand(_ command: NetworkCommand) {
        switch command.type {
        case "capture":
            let count = command.parameters["count"] as? Int ?? 1
            for _ in 0..<count {
                if !isCapturing {
                    captureButtonTapped(captureButton)
                }
            }
            networkManager.sendAcknowledgment(for: "capture")
            
        case "send":
            // Temporarily simulate send action
            let imageData = imageBatchManager.getCompressedImageData()
            networkManager.sendImages(imageData)
            statusLabel.text = "Sent \(imageData.count) images"
            networkManager.sendAcknowledgment(for: "send")
            
        case "clear":
            imageBatchManager.clearCurrentBatch()
            networkManager.sendAcknowledgment(for: "clear")
            
        case "status":
            let response: [String: Any] = [
                "type": "status",
                "batchCount": imageBatchManager.getCurrentBatchCount(),
                "isCapturing": isCapturing,
                "storageInfo": imageBatchManager.getStorageInfo()
            ]
            networkManager.sendResponse(response)
            
        default:
            print("Unknown command: \(command.type)")
        }
    }
}

// MARK: - ImageBatchManagerDelegate
extension ViewController: ImageBatchManagerDelegate {
    func imageBatchManager(_ manager: ImageBatchManager, didUpdateBatchCount count: Int) {
        DispatchQueue.main.async {
            self.updateUI()
        }
    }
    
    func imageBatchManager(_ manager: ImageBatchManager, didFailWithError error: Error) {
        DispatchQueue.main.async {
            self.showAlert(title: "Storage Error", message: error.localizedDescription)
        }
    }
}
