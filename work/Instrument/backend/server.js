require('dotenv').config({ path: '../.env' });
const express = require('express');
const { WebSocketServer, WebSocket } = require('ws');
const { Bonjour } = require('bonjour-service');
const cors = require('cors');
const Anthropic = require('@anthropic-ai/sdk');

const app = express();
const PORT = process.env.BACKEND_PORT || 3000;
const WS_PORT = process.env.WEBSOCKET_PORT || 8081;

// Middleware
app.use(cors());
app.use(express.json({ limit: process.env.MAX_IMAGE_SIZE || '50mb' }));

// Global state
let deviceInfo = null;
let deviceConnection = null;
let frontendClients = new Set();
let isDiscovering = false;

// Initialize Anthropic client
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

// Bonjour service discovery
const bonjour = new Bonjour();

// WebSocket server for frontend connections
const wss = new WebSocketServer({ port: WS_PORT });

console.log(`🚀 Starting The Instrument Backend Server...`);
console.log(`📡 WebSocket server listening on port ${WS_PORT}`);

// WebSocket connection handler for frontend clients
wss.on('connection', (ws) => {
  console.log('🔗 Frontend client connected');
  frontendClients.add(ws);
  
  // Send current device status to new client
  ws.send(JSON.stringify({
    type: 'device_status',
    connected: !!deviceConnection,
    deviceInfo: deviceInfo
  }));

  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      handleFrontendMessage(data);
    } catch (error) {
      console.error('❌ Error parsing frontend message:', error);
    }
  });

  ws.on('close', () => {
    console.log('🔌 Frontend client disconnected');
    frontendClients.delete(ws);
  });

  ws.on('error', (error) => {
    console.error('❌ Frontend WebSocket error:', error);
    frontendClients.delete(ws);
  });
});

// Handle messages from frontend
function handleFrontendMessage(data) {
  console.log('📨 Received from frontend:', data.type);
  
  if (!deviceConnection || deviceConnection.readyState !== WebSocket.OPEN) {
    console.log('⚠️ No device connection available');
    broadcastToFrontend({
      type: 'error',
      message: 'Device not connected'
    });
    return;
  }

  // Forward command to device
  deviceConnection.send(JSON.stringify(data));
}

// Broadcast message to all connected frontend clients
function broadcastToFrontend(message) {
  const messageStr = JSON.stringify(message);
  frontendClients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(messageStr);
    }
  });
}

// Device discovery function
function startDeviceDiscovery() {
  if (isDiscovering) return;

  isDiscovering = true;
  console.log('🔍 Starting device discovery...');

  const serviceName = process.env.DEVICE_SERVICE_NAME || '_instrument._tcp';
  console.log(`🔍 Looking for service type: ${serviceName}`);

  // Try different discovery approaches
  console.log('🔍 Method 1: Using bonjour.find()');
  const browser = bonjour.find({ type: serviceName }, (service) => {
    console.log('📱 Found device via find():', service.name);
    console.log('📱 Device details:', {
      name: service.name,
      type: service.type,
      port: service.port,
      addresses: service.addresses,
      referer: service.referer,
      host: service.host,
      fqdn: service.fqdn
    });

    deviceInfo = {
      name: service.name,
      host: service.host || service.referer?.address || service.addresses?.[0],
      port: service.port
    };

    console.log('📱 Extracted device info:', deviceInfo);
    connectToDevice();
  });

  browser.on('up', (service) => {
    console.log('📱 Service up:', service.name, service.type);
  });

  browser.on('down', (service) => {
    console.log('📱 Service down:', service.name, service.type);
  });

  // Try alternative discovery method
  console.log('🔍 Method 2: Using bonjour.findOne()');
  bonjour.findOne({ type: serviceName }, (service) => {
    if (service) {
      console.log('📱 Found device via findOne():', service.name);
      console.log('📱 Device details:', service);
    } else {
      console.log('📱 No device found via findOne()');
    }
  });

  // Try browsing for all services to see what's available
  console.log('🔍 Method 3: Browsing all services');
  const allBrowser = bonjour.find({}, (service) => {
    if (service.type && service.type.includes('instrument')) {
      console.log('📱 Found instrument-related service:', service.name, service.type);
    }
  });

  // Stop discovery after timeout
  setTimeout(() => {
    if (isDiscovering) {
      console.log('⏰ Device discovery timeout - will retry in 10 seconds');
      isDiscovering = false;

      // Retry discovery after a delay
      setTimeout(() => {
        if (!deviceConnection) {
          startDeviceDiscovery();
        }
      }, 10000);
    }
  }, process.env.DEVICE_DISCOVERY_TIMEOUT || 30000);
}

// Connect to discovered device
function connectToDevice() {
  if (!deviceInfo || deviceConnection) return;
  
  const wsUrl = `ws://${deviceInfo.host}:${deviceInfo.port}`;
  console.log(`🔗 Connecting to device at ${wsUrl}`);
  
  try {
    deviceConnection = new WebSocket(wsUrl);
    
    deviceConnection.on('open', () => {
      console.log('✅ Connected to device');
      isDiscovering = false;
      
      broadcastToFrontend({
        type: 'device_status',
        connected: true,
        deviceInfo: deviceInfo
      });
    });
    
    deviceConnection.on('message', (message) => {
      try {
        const data = JSON.parse(message);
        console.log('📨 Received from device:', data.type);
        
        // Forward device messages to frontend
        broadcastToFrontend(data);
      } catch (error) {
        console.error('❌ Error parsing device message:', error);
      }
    });
    
    deviceConnection.on('close', () => {
      console.log('🔌 Device disconnected');
      deviceConnection = null;
      
      broadcastToFrontend({
        type: 'device_status',
        connected: false,
        deviceInfo: null
      });
      
      // Attempt reconnection after delay
      setTimeout(() => {
        if (!deviceConnection) {
          console.log('🔄 Attempting to reconnect to device...');
          connectToDevice();
        }
      }, 5000);
    });
    
    deviceConnection.on('error', (error) => {
      console.error('❌ Device connection error:', error);
      deviceConnection = null;
      
      // Retry discovery after error
      setTimeout(() => {
        if (!deviceConnection) {
          startDeviceDiscovery();
        }
      }, 5000);
    });
    
  } catch (error) {
    console.error('❌ Failed to connect to device:', error);
    deviceConnection = null;
  }
}

// Routes
app.get('/api/status', (req, res) => {
  res.json({
    connected: !!deviceConnection && deviceConnection.readyState === WebSocket.OPEN,
    deviceInfo: deviceInfo,
    timestamp: new Date().toISOString()
  });
});

// LLM Chat endpoint with streaming support
app.post('/api/chat', async (req, res) => {
  try {
    const { images, context } = req.body;

    if (!images || !Array.isArray(images) || images.length === 0) {
      return res.status(400).json({ error: 'Images array is required' });
    }

    console.log(`🤖 Processing ${images.length} images with Claude...`);

    // Set headers for streaming
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');

    // Prepare messages for Claude
    const content = [];

    // Add context if provided
    if (context && context.trim()) {
      content.push({
        type: 'text',
        text: `Context: ${context.trim()}\n\nPlease analyze the following screen capture(s):`
      });
    } else {
      content.push({
        type: 'text',
        text: 'Please analyze the following screen capture(s) and extract any relevant text, code, or visual information. Provide a clear, structured summary of what you see:'
      });
    }

    // Add images to content
    images.forEach((imageBase64, index) => {
      // Remove data URL prefix if present
      const base64Data = imageBase64.replace(/^data:image\/[a-z]+;base64,/, '');

      content.push({
        type: 'image',
        source: {
          type: 'base64',
          media_type: 'image/jpeg', // Assuming JPEG, could be made dynamic
          data: base64Data
        }
      });
    });

    // Create streaming message with Claude
    const stream = await anthropic.messages.create({
      model: process.env.CLAUDE_MODEL || 'claude-sonnet-4-20250514',
      max_tokens: 4000,
      messages: [{
        role: 'user',
        content: content
      }],
      stream: true
    });

    // Stream the response
    for await (const chunk of stream) {
      if (chunk.type === 'content_block_delta' && chunk.delta.text) {
        const data = `data: ${JSON.stringify({ text: chunk.delta.text })}\n\n`;
        res.write(data);
      }
    }

    // End the stream
    res.write('data: [DONE]\n\n');
    res.end();

  } catch (error) {
    console.error('❌ LLM processing error:', error);

    if (!res.headersSent) {
      res.status(500).json({
        error: 'Failed to process images with LLM',
        details: error.message
      });
    } else {
      res.write(`data: ${JSON.stringify({ error: error.message })}\n\n`);
      res.end();
    }
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`🌐 HTTP server listening on port ${PORT}`);
  console.log(`📋 API available at http://localhost:${PORT}/api`);
  
  // Start device discovery
  startDeviceDiscovery();
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down gracefully...');
  
  if (deviceConnection) {
    deviceConnection.close();
  }
  
  wss.close();
  bonjour.destroy();
  process.exit(0);
});
