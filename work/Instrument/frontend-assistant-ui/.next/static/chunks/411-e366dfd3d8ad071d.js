(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[411],{97:(e,t,n)=>{"use strict";function r(e,t){function n(n){let r=e(n);return r?r[t]:null}return{[t]:function(e){let t,r=!1;"function"==typeof e?t=e:e&&"object"==typeof e&&(r=!!e.optional,t=e.selector);let i=n({optional:r});return i?t?i(t):i():null},[`${t}Store`]:n}}n.d(t,{o:()=>r})},100:(e,t,n)=>{"use strict";n.d(t,{g:()=>l});var r=n(2115),i=n(3357),o=n(7119),s=n(5155),a=(0,r.memo)(e=>{let{index:t,archived:n=!1,components:a}=e,l=(0,o.Ij)(),u=(0,r.useMemo)(()=>n?l.threads.getArchivedItemByIndex(t):l.threads.getItemByIndex(t),[l,t,n]),c=a.ThreadListItem;return(0,s.jsx)(i.X,{runtime:u,children:(0,s.jsx)(c,{})})},(e,t)=>e.index===t.index&&e.archived===t.archived&&e.components.ThreadListItem===t.components.ThreadListItem);a.displayName="ThreadListPrimitive.ItemByIndex";var l=e=>{let{archived:t=!1,components:n}=e,i=(0,o._K)(e=>t?e.archivedThreads.length:e.threads.length);return(0,r.useMemo)(()=>Array.from({length:i},(e,r)=>(0,s.jsx)(a,{index:r,archived:t,components:n},r)),[i,t,n])};l.displayName="ThreadListPrimitive.Items"},123:(e,t,n)=>{"use strict";n.d(t,{v:()=>i});var r=n(5455),i=e=>{let{children:t,...n}=e;return(e=>(0,r._W)(t=>(!0!==e.empty||0===t.messages.length)&&(!1!==e.empty||0!==t.messages.length)&&(!0!==e.running||!!t.isRunning)&&(!1!==e.running||!t.isRunning)&&(!0!==e.disabled||!!t.isDisabled)&&(!1!==e.disabled||!t.isDisabled)&&!0))(n)?t:null};i.displayName="ThreadPrimitive.If"},148:(e,t,n)=>{"use strict";n.d(t,{K:()=>s});var r=n(3655),i=n(2115),o=n(5155),s=(0,i.forwardRef)((e,t)=>(0,o.jsx)(r.sG.div,{...e,ref:t}));s.displayName="ThreadPrimitive.Root"},173:(e,t,n)=>{"use strict";n.d(t,{v:()=>s});var r=n(8716),i=n(2115),o=n(6882),s=(0,r.x)("BranchPickerPrimitive.Previous",()=>{let e=(0,o.LN)(),t=(0,o.JX)(e=>e.branchNumber<=1),n=(0,i.useCallback)(()=>{e.switchToBranch({position:"previous"})},[e]);return t?null:n})},274:(e,t,n)=>{"use strict";n.d(t,{i:()=>nD});var r={};n.r(r),n.d(r,{cg:()=>s.c,a2:()=>a.a2,D0:()=>a.D0});var i={};n.r(i),n.d(i,{boolean:()=>k,booleanish:()=>S,commaOrSpaceSeparated:()=>A,commaSeparated:()=>C,number:()=>T,overloadedBoolean:()=>E,spaceSeparated:()=>_});var o={};n.r(o),n.d(o,{attentionMarkers:()=>tv,contentInitial:()=>td,disable:()=>tb,document:()=>th,flow:()=>tp,flowInitial:()=>tf,insideSpan:()=>ty,string:()=>tm,text:()=>tg});var s=n(1246),a=n(6906),l=n(2500),u=n(2115),c=n(4093);let h=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,d=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,f={};function p(e,t){return((t||f).jsx?d:h).test(e)}let m=/[ \t\n\f\r]/g;function g(e){return""===e.replace(m,"")}class y{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function v(e,t){let n={},r={};for(let t of e)Object.assign(n,t.property),Object.assign(r,t.normal);return new y(n,r,t)}function b(e){return e.toLowerCase()}y.prototype.normal={},y.prototype.property={},y.prototype.space=void 0;class x{constructor(e,t){this.attribute=t,this.property=e}}x.prototype.attribute="",x.prototype.booleanish=!1,x.prototype.boolean=!1,x.prototype.commaOrSpaceSeparated=!1,x.prototype.commaSeparated=!1,x.prototype.defined=!1,x.prototype.mustUseProperty=!1,x.prototype.number=!1,x.prototype.overloadedBoolean=!1,x.prototype.property="",x.prototype.spaceSeparated=!1,x.prototype.space=void 0;let w=0,k=I(),S=I(),E=I(),T=I(),_=I(),C=I(),A=I();function I(){return 2**++w}let P=Object.keys(i);class R extends x{constructor(e,t,n,r){let o=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",r),"number"==typeof n)for(;++o<P.length;){let e=P[o];!function(e,t,n){n&&(e[t]=n)}(this,P[o],(n&i[e])===i[e])}}}function M(e){let t={},n={};for(let[r,i]of Object.entries(e.properties)){let o=new R(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(o.mustUseProperty=!0),t[r]=o,n[b(r)]=r,n[b(o.attribute)]=r}return new y(t,n,e.space)}R.prototype.defined=!0;let O=M({properties:{ariaActiveDescendant:null,ariaAtomic:S,ariaAutoComplete:null,ariaBusy:S,ariaChecked:S,ariaColCount:T,ariaColIndex:T,ariaColSpan:T,ariaControls:_,ariaCurrent:null,ariaDescribedBy:_,ariaDetails:null,ariaDisabled:S,ariaDropEffect:_,ariaErrorMessage:null,ariaExpanded:S,ariaFlowTo:_,ariaGrabbed:S,ariaHasPopup:null,ariaHidden:S,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:_,ariaLevel:T,ariaLive:null,ariaModal:S,ariaMultiLine:S,ariaMultiSelectable:S,ariaOrientation:null,ariaOwns:_,ariaPlaceholder:null,ariaPosInSet:T,ariaPressed:S,ariaReadOnly:S,ariaRelevant:null,ariaRequired:S,ariaRoleDescription:_,ariaRowCount:T,ariaRowIndex:T,ariaRowSpan:T,ariaSelected:S,ariaSetSize:T,ariaSort:null,ariaValueMax:T,ariaValueMin:T,ariaValueNow:T,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function D(e,t){return t in e?e[t]:t}function L(e,t){return D(e,t.toLowerCase())}let j=M({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:C,acceptCharset:_,accessKey:_,action:null,allow:null,allowFullScreen:k,allowPaymentRequest:k,allowUserMedia:k,alt:null,as:null,async:k,autoCapitalize:null,autoComplete:_,autoFocus:k,autoPlay:k,blocking:_,capture:null,charSet:null,checked:k,cite:null,className:_,cols:T,colSpan:null,content:null,contentEditable:S,controls:k,controlsList:_,coords:T|C,crossOrigin:null,data:null,dateTime:null,decoding:null,default:k,defer:k,dir:null,dirName:null,disabled:k,download:E,draggable:S,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:k,formTarget:null,headers:_,height:T,hidden:E,high:T,href:null,hrefLang:null,htmlFor:_,httpEquiv:_,id:null,imageSizes:null,imageSrcSet:null,inert:k,inputMode:null,integrity:null,is:null,isMap:k,itemId:null,itemProp:_,itemRef:_,itemScope:k,itemType:_,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:k,low:T,manifest:null,max:null,maxLength:T,media:null,method:null,min:null,minLength:T,multiple:k,muted:k,name:null,nonce:null,noModule:k,noValidate:k,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:k,optimum:T,pattern:null,ping:_,placeholder:null,playsInline:k,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:k,referrerPolicy:null,rel:_,required:k,reversed:k,rows:T,rowSpan:T,sandbox:_,scope:null,scoped:k,seamless:k,selected:k,shadowRootClonable:k,shadowRootDelegatesFocus:k,shadowRootMode:null,shape:null,size:T,sizes:null,slot:null,span:T,spellCheck:S,src:null,srcDoc:null,srcLang:null,srcSet:null,start:T,step:null,style:null,tabIndex:T,target:null,title:null,translate:null,type:null,typeMustMatch:k,useMap:null,value:S,width:T,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:_,axis:null,background:null,bgColor:null,border:T,borderColor:null,bottomMargin:T,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:k,declare:k,event:null,face:null,frame:null,frameBorder:null,hSpace:T,leftMargin:T,link:null,longDesc:null,lowSrc:null,marginHeight:T,marginWidth:T,noResize:k,noHref:k,noShade:k,noWrap:k,object:null,profile:null,prompt:null,rev:null,rightMargin:T,rules:null,scheme:null,scrolling:S,standby:null,summary:null,text:null,topMargin:T,valueType:null,version:null,vAlign:null,vLink:null,vSpace:T,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:k,disableRemotePlayback:k,prefix:null,property:null,results:T,security:null,unselectable:null},space:"html",transform:L}),B=M({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:A,accentHeight:T,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:T,amplitude:T,arabicForm:null,ascent:T,attributeName:null,attributeType:null,azimuth:T,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:T,by:null,calcMode:null,capHeight:T,className:_,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:T,diffuseConstant:T,direction:null,display:null,dur:null,divisor:T,dominantBaseline:null,download:k,dx:null,dy:null,edgeMode:null,editable:null,elevation:T,enableBackground:null,end:null,event:null,exponent:T,externalResourcesRequired:null,fill:null,fillOpacity:T,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:C,g2:C,glyphName:C,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:T,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:T,horizOriginX:T,horizOriginY:T,id:null,ideographic:T,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:T,k:T,k1:T,k2:T,k3:T,k4:T,kernelMatrix:A,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:T,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:T,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:T,overlineThickness:T,paintOrder:null,panose1:null,path:null,pathLength:T,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:_,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:T,pointsAtY:T,pointsAtZ:T,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:A,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:A,rev:A,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:A,requiredFeatures:A,requiredFonts:A,requiredFormats:A,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:T,specularExponent:T,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:T,strikethroughThickness:T,string:null,stroke:null,strokeDashArray:A,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:T,strokeOpacity:T,strokeWidth:null,style:null,surfaceScale:T,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:A,tabIndex:T,tableValues:null,target:null,targetX:T,targetY:T,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:A,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:T,underlineThickness:T,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:T,values:null,vAlphabetic:T,vMathematical:T,vectorEffect:null,vHanging:T,vIdeographic:T,version:null,vertAdvY:T,vertOriginX:T,vertOriginY:T,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:T,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:D}),N=M({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),F=M({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:L}),z=M({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),U=v([O,j,N,F,z],"html"),V=v([O,B,N,F,z],"svg"),H=/[A-Z]/g,W=/-[a-z]/g,$=/^data[-\w.:]+$/i;function q(e){return"-"+e.toLowerCase()}function K(e){return e.charAt(1).toUpperCase()}let Y={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var X=n(3724);let G=Z("end"),J=Z("start");function Z(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function Q(e){return e&&"object"==typeof e?"position"in e||"type"in e?et(e.position):"start"in e||"end"in e?et(e):"line"in e||"column"in e?ee(e):"":""}function ee(e){return en(e&&e.line)+":"+en(e&&e.column)}function et(e){return ee(e&&e.start)+"-"+ee(e&&e.end)}function en(e){return e&&"number"==typeof e?e:1}class er extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",i={},o=!1;if(t&&(i="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!i.cause&&e&&(o=!0,r=e.message,i.cause=e),!i.ruleId&&!i.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?i.ruleId=n:(i.source=n.slice(0,e),i.ruleId=n.slice(e+1))}if(!i.place&&i.ancestors&&i.ancestors){let e=i.ancestors[i.ancestors.length-1];e&&(i.place=e.position)}let s=i.place&&"start"in i.place?i.place.start:i.place;this.ancestors=i.ancestors||void 0,this.cause=i.cause||void 0,this.column=s?s.column:void 0,this.fatal=void 0,this.file="",this.message=r,this.line=s?s.line:void 0,this.name=Q(i.place)||"1:1",this.place=i.place||void 0,this.reason=this.message,this.ruleId=i.ruleId||void 0,this.source=i.source||void 0,this.stack=o&&i.cause&&"string"==typeof i.cause.stack?i.cause.stack:"",this.actual=void 0,this.expected=void 0,this.note=void 0,this.url=void 0}}er.prototype.file="",er.prototype.name="",er.prototype.reason="",er.prototype.message="",er.prototype.stack="",er.prototype.column=void 0,er.prototype.line=void 0,er.prototype.ancestors=void 0,er.prototype.cause=void 0,er.prototype.fatal=void 0,er.prototype.place=void 0,er.prototype.ruleId=void 0,er.prototype.source=void 0;let ei={}.hasOwnProperty,eo=new Map,es=/[A-Z]/g,ea=new Set(["table","tbody","thead","tfoot","tr"]),el=new Set(["td","th"]),eu="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function ec(e,t,n){return"element"===t.type?function(e,t,n){let r=e.schema;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(e.schema=V),e.ancestors.push(t);let i=ep(e,t.tagName,!1),o=function(e,t){let n,r,i={};for(r in t.properties)if("children"!==r&&ei.call(t.properties,r)){let o=function(e,t,n){let r=function(e,t){let n=b(t),r=t,i=x;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&$.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(W,K);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!W.test(e)){let n=e.replace(H,q);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}i=R}return new i(r,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e,t){let n={};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return X(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new er("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=eu+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t,n={};for(t in e)ei.call(e,t)&&(n[function(e){let t=e.replace(es,eg);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?Y[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(o){let[r,s]=o;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof s&&el.has(t.tagName)?n=s:i[r]=s}}return n&&((i.style||(i.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),i}(e,t),s=ef(e,t);return ea.has(t.tagName)&&(s=s.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&g(e.value):g(e))})),eh(e,o,i,t),ed(o,s),e.ancestors.pop(),e.schema=r,e.create(t,i,o,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return(0,c.ok)("ExpressionStatement"===n.type),e.evaluater.evaluateExpression(n.expression)}em(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema;"svg"===t.name&&"html"===r.space&&(e.schema=V),e.ancestors.push(t);let i=null===t.name?e.Fragment:ep(e,t.name,!0),o=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];(0,c.ok)("ExpressionStatement"===t.type);let i=t.expression;(0,c.ok)("ObjectExpression"===i.type);let o=i.properties[0];(0,c.ok)("SpreadElement"===o.type),Object.assign(n,e.evaluater.evaluateExpression(o.argument))}else em(e,t.position);else{let i,o=r.name;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];(0,c.ok)("ExpressionStatement"===t.type),i=e.evaluater.evaluateExpression(t.expression)}else em(e,t.position);else i=null===r.value||r.value;n[o]=i}return n}(e,t),s=ef(e,t);return eh(e,o,i,t),ed(o,s),e.ancestors.pop(),e.schema=r,e.create(t,i,o,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);em(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return ed(r,ef(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?t.value:void 0}function eh(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function ed(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function ef(e,t){let n=[],r=-1,i=e.passKeys?new Map:eo;for(;++r<t.children.length;){let o,s=t.children[r];if(e.passKeys){let e="element"===s.type?s.tagName:"mdxJsxFlowElement"===s.type||"mdxJsxTextElement"===s.type?s.name:void 0;if(e){let t=i.get(e)||0;o=e+"-"+t,i.set(e,t+1)}}let a=ec(e,s,o);void 0!==a&&n.push(a)}return n}function ep(e,t,n){let r;if(n)if(t.includes(".")){let e,n=t.split("."),i=-1;for(;++i<n.length;){let t=p(n[i])?{type:"Identifier",name:n[i]}:{type:"Literal",value:n[i]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(i&&"Literal"===t.type),optional:!1}:t}(0,c.ok)(e,"always a result"),r=e}else r=p(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return ei.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);em(e)}function em(e,t){let n=new er("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=eu+"#cannot-handle-mdx-estrees-without-createevaluater",n}function eg(e){return"-"+e.toLowerCase()}let ey={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var ev=n(5155),eb=n(4392),ex=n(1603);class ew{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?1/0:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),1/0);return n&&ek(this.left,n),r.reverse()}pop(){return this.setCursor(1/0),this.left.pop()}push(e){this.setCursor(1/0),this.left.push(e)}pushMany(e){this.setCursor(1/0),ek(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),ek(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length))if(e<this.left.length){let t=this.left.splice(e,1/0);ek(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,1/0);ek(this.left,t.reverse())}}}function ek(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function eS(e){let t,n,r,i,o,s,a,l={},u=-1,c=new ew(e);for(;++u<c.length;){for(;u in l;)u=l[u];if(t=c.get(u),u&&"chunkFlow"===t[1].type&&"listItemPrefix"===c.get(u-1)[1].type&&((r=0)<(s=t[1]._tokenizer.events).length&&"lineEndingBlank"===s[r][1].type&&(r+=2),r<s.length&&"content"===s[r][1].type))for(;++r<s.length&&"content"!==s[r][1].type;)"chunkText"===s[r][1].type&&(s[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(l,function(e,t){let n,r,i=e.get(t)[1],o=e.get(t)[2],s=t-1,a=[],l=i._tokenizer;!l&&(l=o.parser[i.contentType](i.start),i._contentTypeTextTrailing&&(l._contentTypeTextTrailing=!0));let u=l.events,c=[],h={},d=-1,f=i,p=0,m=0,g=[0];for(;f;){for(;e.get(++s)[1]!==f;);a.push(s),!f._tokenizer&&(n=o.sliceStream(f),f.next||n.push(null),r&&l.defineSkip(f.start),f._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(n),f._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),r=f,f=f.next}for(f=i;++d<u.length;)"exit"===u[d][0]&&"enter"===u[d-1][0]&&u[d][1].type===u[d-1][1].type&&u[d][1].start.line!==u[d][1].end.line&&(m=d+1,g.push(m),f._tokenizer=void 0,f.previous=void 0,f=f.next);for(l.events=[],f?(f._tokenizer=void 0,f.previous=void 0):g.pop(),d=g.length;d--;){let t=u.slice(g[d],g[d+1]),n=a.pop();c.push([n,n+t.length-1]),e.splice(n,2,t)}for(c.reverse(),d=-1;++d<c.length;)h[p+c[d][0]]=p+c[d][1],p+=c[d][1]-c[d][0]-1;return h}(c,u)),u=l[u],a=!0);else if(t[1]._container){for(r=u,n=void 0;r--;)if("lineEnding"===(i=c.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(c.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else if("linePrefix"===i[1].type||"listItemIndent"===i[1].type);else break;n&&(t[1].end={...c.get(n)[1].start},(o=c.slice(n,u)).unshift(t),c.splice(n,u-n+1,o))}}return(0,ex.m)(e,0,1/0,c.slice(0)),!a}var eE=n(9381),eT=n(4581),e_=n(2556);let eC={tokenize:function(e){let t,n=e.attempt(this.parser.constructs.contentInitial,function(t){return null===t?void e.consume(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,eT.N)(e,n,"linePrefix"))},function(n){return e.enter("paragraph"),function n(r){let i=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=i),t=i,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return(0,e_.HP)(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},eA={tokenize:function(e){let t,n,r,i=this,o=[],s=0;return a;function a(t){if(s<o.length){let n=o[s];return i.containerState=n[1],e.attempt(n[0].continuation,l,u)(t)}return u(t)}function l(e){if(s++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&y();let r=i.events.length,o=r;for(;o--;)if("exit"===i.events[o][0]&&"chunkFlow"===i.events[o][1].type){n=i.events[o][1].end;break}g(s);let a=r;for(;a<i.events.length;)i.events[a][1].end={...n},a++;return(0,ex.m)(i.events,o+1,0,i.events.slice(r)),i.events.length=a,u(e)}return a(e)}function u(n){if(s===o.length){if(!t)return d(n);if(t.currentConstruct&&t.currentConstruct.concrete)return p(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check(eI,c,h)(n)}function c(e){return t&&y(),g(s),d(e)}function h(e){return i.parser.lazy[i.now().line]=s!==o.length,r=i.now().offset,p(e)}function d(t){return i.containerState={},e.attempt(eI,f,p)(t)}function f(e){return s++,o.push([i.currentConstruct,i.containerState]),d(e)}function p(r){if(null===r){t&&y(),g(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){m(e.exit("chunkFlow"),!0),g(0),e.consume(n);return}return(0,e_.HP)(n)?(e.consume(n),m(e.exit("chunkFlow")),s=0,i.interrupt=void 0,a):(e.consume(n),t)}(r)}function m(e,o){let a=i.sliceStream(e);if(o&&a.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(a),i.parser.lazy[e.start.line]){let e,n,o=t.events.length;for(;o--;)if(t.events[o][1].start.offset<r&&(!t.events[o][1].end||t.events[o][1].end.offset>r))return;let a=i.events.length,l=a;for(;l--;)if("exit"===i.events[l][0]&&"chunkFlow"===i.events[l][1].type){if(e){n=i.events[l][1].end;break}e=!0}for(g(s),o=a;o<i.events.length;)i.events[o][1].end={...n},o++;(0,ex.m)(i.events,l+1,0,i.events.slice(a)),i.events.length=o}}function g(t){let n=o.length;for(;n-- >t;){let t=o[n];i.containerState=t[1],t[0].exit.call(i,e)}o.length=t}function y(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}},eI={tokenize:function(e,t,n){return(0,eT.N)(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};var eP=n(5333);let eR={resolve:function(e){return eS(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?i(t):(0,e_.HP)(t)?e.check(eM,o,i)(t):(e.consume(t),r)}function i(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function o(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},eM={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,eT.N)(e,i,"linePrefix")};function i(i){if(null===i||(0,e_.HP)(i))return n(i);let o=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&o&&"linePrefix"===o[1].type&&o[2].sliceSerialize(o[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}},eO={tokenize:function(e){let t=this,n=e.attempt(eP.B,function(r){return null===r?void e.consume(r):(e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n)},e.attempt(this.parser.constructs.flowInitial,r,(0,eT.N)(e,e.attempt(this.parser.constructs.flow,r,e.attempt(eR,r)),"linePrefix")));return n;function r(r){return null===r?void e.consume(r):(e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n)}}},eD={resolveAll:eN()},eL=eB("string"),ej=eB("text");function eB(e){return{resolveAll:eN("text"===e?eF:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,o,s);return o;function o(e){return l(e)?i(e):s(e)}function s(e){return null===e?void t.consume(e):(t.enter("data"),t.consume(e),a)}function a(e){return l(e)?(t.exit("data"),i(e)):(t.consume(e),a)}function l(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function eN(e){return function(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function eF(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r,i=e[n-1][1],o=t.sliceStream(i),s=o.length,a=-1,l=0;for(;s--;){let e=o[s];if("string"==typeof e){for(a=e.length;32===e.charCodeAt(a-1);)l++,a--;if(a)break;a=-1}else if(-2===e)r=!0,l++;else if(-1===e);else{s++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(l=0),l){let o={type:n===e.length||r||l<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:s?a:i.start._bufferIndex+a,_index:i.start._index+s,line:i.end.line,column:i.end.column-l,offset:i.end.offset-l},end:{...i.end}};i.end={...o.start},i.start.offset===i.end.offset?Object.assign(i,o):(e.splice(n,0,["enter",o,t],["exit",o,t]),n+=2)}n++}return e}let ez={name:"thematicBreak",tokenize:function(e,t,n){let r,i=0;return function(o){var s;return e.enter("thematicBreak"),r=s=o,function o(s){return s===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),i++,t):(e.exit("thematicBreakSequence"),(0,e_.On)(n)?(0,eT.N)(e,o,"whitespace")(n):o(n))}(s)):i>=3&&(null===s||(0,e_.HP)(s))?(e.exit("thematicBreak"),t(s)):n(s)}(s)}}},eU={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(eP.B,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,(0,eT.N)(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!(0,e_.On)(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(eH,t,i)(n))});function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,(0,eT.N)(e,e.attempt(eU,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,i=r.events[r.events.length-1],o=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,s=0;return function(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:(0,e_.BM)(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(ez,n,a)(t):a(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(i){return(0,e_.BM)(i)&&++s<10?(e.consume(i),t):(!r.interrupt||s<2)&&(r.containerState.marker?i===r.containerState.marker:41===i||46===i)?(e.exit("listItemValue"),a(i)):n(i)}(t)}return n(t)};function a(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(eP.B,r.interrupt?n:l,e.attempt(eV,c,u))}function l(e){return r.containerState.initialBlankLine=!0,o++,c(e)}function u(t){return(0,e_.On)(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),c):n(t)}function c(n){return r.containerState.size=o+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},eV={partial:!0,tokenize:function(e,t,n){let r=this;return(0,eT.N)(e,function(e){let i=r.events[r.events.length-1];return!(0,e_.On)(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},eH={partial:!0,tokenize:function(e,t,n){let r=this;return(0,eT.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},eW={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return(0,e_.On)(t)?(0,eT.N)(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):i(t)};function i(r){return e.attempt(eW,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),i}return n(t)};function i(n){return(0,e_.On)(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function e$(e,t,n,r,i,o,s,a,l){let u=l||1/0,c=0;return function(t){return 60===t?(e.enter(r),e.enter(i),e.enter(o),e.consume(t),e.exit(o),h):null===t||32===t||41===t||(0,e_.JQ)(t)?n(t):(e.enter(r),e.enter(s),e.enter(a),e.enter("chunkString",{contentType:"string"}),p(t))};function h(n){return 62===n?(e.enter(o),e.consume(n),e.exit(o),e.exit(i),e.exit(r),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),d(n))}function d(t){return 62===t?(e.exit("chunkString"),e.exit(a),h(t)):null===t||60===t||(0,e_.HP)(t)?n(t):(e.consume(t),92===t?f:d)}function f(t){return 60===t||62===t||92===t?(e.consume(t),d):d(t)}function p(i){return!c&&(null===i||41===i||(0,e_.Ee)(i))?(e.exit("chunkString"),e.exit(a),e.exit(s),e.exit(r),t(i)):c<u&&40===i?(e.consume(i),c++,p):41===i?(e.consume(i),c--,p):null===i||32===i||40===i||(0,e_.JQ)(i)?n(i):(e.consume(i),92===i?m:p)}function m(t){return 40===t||41===t||92===t?(e.consume(t),p):p(t)}}function eq(e,t,n,r,i,o){let s,a=this,l=0;return function(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(o),u};function u(h){return l>999||null===h||91===h||93===h&&!s||94===h&&!l&&"_hiddenFootnoteSupport"in a.parser.constructs?n(h):93===h?(e.exit(o),e.enter(i),e.consume(h),e.exit(i),e.exit(r),t):(0,e_.HP)(h)?(e.enter("lineEnding"),e.consume(h),e.exit("lineEnding"),u):(e.enter("chunkString",{contentType:"string"}),c(h))}function c(t){return null===t||91===t||93===t||(0,e_.HP)(t)||l++>999?(e.exit("chunkString"),u(t)):(e.consume(t),s||(s=!(0,e_.On)(t)),92===t?h:c)}function h(t){return 91===t||92===t||93===t?(e.consume(t),l++,c):c(t)}}function eK(e,t,n,r,i,o){let s;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),s=40===t?41:t,a):n(t)};function a(n){return n===s?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(o),l(n))}function l(t){return t===s?(e.exit(o),a(s)):null===t?n(t):(0,e_.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,eT.N)(e,l,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),u(t))}function u(t){return t===s||null===t||(0,e_.HP)(t)?(e.exit("chunkString"),l(t)):(e.consume(t),92===t?c:u)}function c(t){return t===s||92===t?(e.consume(t),u):u(t)}}function eY(e,t){let n;return function r(i){return(0,e_.HP)(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):(0,e_.On)(i)?(0,eT.N)(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}var eX=n(3386);let eG={partial:!0,tokenize:function(e,t,n){return function(t){return(0,e_.Ee)(t)?eY(e,r)(t):n(t)};function r(t){return eK(e,i,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function i(t){return(0,e_.On)(t)?(0,eT.N)(e,o,"whitespace")(t):o(t)}function o(e){return null===e||(0,e_.HP)(e)?t(e):n(e)}}},eJ={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),(0,eT.N)(e,i,"linePrefix",5)(t)};function i(t){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function t(n){return null===n?o(n):(0,e_.HP)(n)?e.attempt(eZ,t,o)(n):(e.enter("codeFlowValue"),function n(r){return null===r||(0,e_.HP)(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function o(n){return e.exit("codeIndented"),t(n)}}},eZ={partial:!0,tokenize:function(e,t,n){let r=this;return i;function i(t){return r.parser.lazy[r.now().line]?n(t):(0,e_.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):(0,eT.N)(e,o,"linePrefix",5)(t)}function o(e){let o=r.events[r.events.length-1];return o&&"linePrefix"===o[1].type&&o[2].sliceSerialize(o[1],!0).length>=4?t(e):(0,e_.HP)(e)?i(e):n(e)}}},eQ={name:"setextUnderline",resolveTo:function(e,t){let n,r,i,o=e.length;for(;o--;)if("enter"===e[o][0]){if("content"===e[o][1].type){n=o;break}"paragraph"===e[o][1].type&&(r=o)}else"content"===e[o][1].type&&e.splice(o,1),i||"definition"!==e[o][1].type||(i=o);let s={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",s,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end={...e[i][1].end}):e[n][1]=s,e.push(["exit",s,t]),e},tokenize:function(e,t,n){let r,i=this;return function(t){var s;let a,l=i.events.length;for(;l--;)if("lineEnding"!==i.events[l][1].type&&"linePrefix"!==i.events[l][1].type&&"content"!==i.events[l][1].type){a="paragraph"===i.events[l][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||a)?(e.enter("setextHeadingLine"),r=t,s=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),(0,e_.On)(n)?(0,eT.N)(e,o,"lineSuffix")(n):o(n))}(s)):n(t)};function o(r){return null===r||(0,e_.HP)(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},e0=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],e1=["pre","script","style","textarea"],e2={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(eP.B,t,n)}}},e5={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return(0,e_.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):n(t)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},e3={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},e6={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r,i=this,o={partial:!0,tokenize:function(e,t,n){let o=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),s};function s(t){return e.enter("codeFencedFence"),(0,e_.On)(t)?(0,eT.N)(e,l,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):l(t)}function l(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(i){return i===r?(o++,e.consume(i),t):o>=a?(e.exit("codeFencedFenceSequence"),(0,e_.On)(i)?(0,eT.N)(e,u,"whitespace")(i):u(i)):n(i)}(t)):n(t)}function u(r){return null===r||(0,e_.HP)(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},s=0,a=0;return function(t){var o=t;let u=i.events[i.events.length-1];return s=u&&"linePrefix"===u[1].type?u[2].sliceSerialize(u[1],!0).length:0,r=o,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(i){return i===r?(a++,e.consume(i),t):a<3?n(i):(e.exit("codeFencedFenceSequence"),(0,e_.On)(i)?(0,eT.N)(e,l,"whitespace")(i):l(i))}(o)};function l(o){return null===o||(0,e_.HP)(o)?(e.exit("codeFencedFence"),i.interrupt?t(o):e.check(e3,c,p)(o)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,e_.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),l(i)):(0,e_.On)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),(0,eT.N)(e,u,"whitespace")(i)):96===i&&i===r?n(i):(e.consume(i),t)}(o))}function u(t){return null===t||(0,e_.HP)(t)?l(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,e_.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),l(i)):96===i&&i===r?n(i):(e.consume(i),t)}(t))}function c(t){return e.attempt(o,p,h)(t)}function h(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),d}function d(t){return s>0&&(0,e_.On)(t)?(0,eT.N)(e,f,"linePrefix",s+1)(t):f(t)}function f(t){return null===t||(0,e_.HP)(t)?e.check(e3,c,p)(t):(e.enter("codeFlowValue"),function t(n){return null===n||(0,e_.HP)(n)?(e.exit("codeFlowValue"),f(n)):(e.consume(n),t)}(t))}function p(n){return e.exit("codeFenced"),t(n)}}},e4=document.createElement("i");function e9(e){let t="&"+e+";";e4.innerHTML=t;let n=e4.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&n!==t&&n}let e8={name:"characterReference",tokenize:function(e,t,n){let r,i,o=this,s=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),a};function a(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),l):(e.enter("characterReferenceValue"),r=31,i=e_.lV,u(t))}function l(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=e_.ok,u):(e.enter("characterReferenceValue"),r=7,i=e_.BM,u(t))}function u(a){if(59===a&&s){let r=e.exit("characterReferenceValue");return i!==e_.lV||e9(o.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(a),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(a)}return i(a)&&s++<r?(e.consume(a),u):n(a)}}},e7={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return(0,e_.ol)(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},te={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,eT.N)(e,t,"linePrefix")}}};var tt=n(1877);let tn={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&(0,ex.m)(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,i,o,s=e.length,a=0;for(;s--;)if(n=e[s][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[s][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[s][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=s,"labelLink"!==n.type)){a=2;break}}else"labelEnd"===n.type&&(i=s);let l={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},u={type:"label",start:{...e[r][1].start},end:{...e[i][1].end}},c={type:"labelText",start:{...e[r+a+2][1].end},end:{...e[i-2][1].start}};return o=[["enter",l,t],["enter",u,t]],o=(0,ex.V)(o,e.slice(r+1,r+a+3)),o=(0,ex.V)(o,[["enter",c,t]]),o=(0,ex.V)(o,(0,tt.W)(t.parser.constructs.insideSpan.null,e.slice(r+a+4,i-3),t)),o=(0,ex.V)(o,[["exit",c,t],e[i-2],e[i-1],["exit",u,t]]),o=(0,ex.V)(o,e.slice(i+1)),o=(0,ex.V)(o,[["exit",l,t]]),(0,ex.m)(e,r,e.length,o),e},tokenize:function(e,t,n){let r,i,o=this,s=o.events.length;for(;s--;)if(("labelImage"===o.events[s][1].type||"labelLink"===o.events[s][1].type)&&!o.events[s][1]._balanced){r=o.events[s][1];break}return function(t){return r?r._inactive?c(t):(i=o.parser.defined.includes((0,eX.B)(o.sliceSerialize({start:r.end,end:o.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),a):n(t)};function a(t){return 40===t?e.attempt(tr,u,i?u:c)(t):91===t?e.attempt(ti,u,i?l:c)(t):i?u(t):c(t)}function l(t){return e.attempt(to,u,c)(t)}function u(e){return t(e)}function c(e){return r._balanced=!0,n(e)}}},tr={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return(0,e_.Ee)(t)?eY(e,i)(t):i(t)}function i(t){return 41===t?u(t):e$(e,o,s,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function o(t){return(0,e_.Ee)(t)?eY(e,a)(t):u(t)}function s(e){return n(e)}function a(t){return 34===t||39===t||40===t?eK(e,l,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):u(t)}function l(t){return(0,e_.Ee)(t)?eY(e,u)(t):u(t)}function u(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},ti={tokenize:function(e,t,n){let r=this;return function(t){return eq.call(r,e,i,o,"reference","referenceMarker","referenceString")(t)};function i(e){return r.parser.defined.includes((0,eX.B)(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function o(e){return n(e)}}},to={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},ts={name:"labelStartImage",resolveAll:tn.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),i};function i(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),o):n(t)}function o(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};var ta=n(9535);let tl={name:"attention",resolveAll:function(e,t){let n,r,i,o,s,a,l,u,c=-1;for(;++c<e.length;)if("enter"===e[c][0]&&"attentionSequence"===e[c][1].type&&e[c][1]._close){for(n=c;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[c][1]).charCodeAt(0)){if((e[n][1]._close||e[c][1]._open)&&(e[c][1].end.offset-e[c][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[c][1].end.offset-e[c][1].start.offset)%3))continue;a=e[n][1].end.offset-e[n][1].start.offset>1&&e[c][1].end.offset-e[c][1].start.offset>1?2:1;let h={...e[n][1].end},d={...e[c][1].start};tu(h,-a),tu(d,a),o={type:a>1?"strongSequence":"emphasisSequence",start:h,end:{...e[n][1].end}},s={type:a>1?"strongSequence":"emphasisSequence",start:{...e[c][1].start},end:d},i={type:a>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[c][1].start}},r={type:a>1?"strong":"emphasis",start:{...o.start},end:{...s.end}},e[n][1].end={...o.start},e[c][1].start={...s.end},l=[],e[n][1].end.offset-e[n][1].start.offset&&(l=(0,ex.V)(l,[["enter",e[n][1],t],["exit",e[n][1],t]])),l=(0,ex.V)(l,[["enter",r,t],["enter",o,t],["exit",o,t],["enter",i,t]]),l=(0,ex.V)(l,(0,tt.W)(t.parser.constructs.insideSpan.null,e.slice(n+1,c),t)),l=(0,ex.V)(l,[["exit",i,t],["enter",s,t],["exit",s,t],["exit",r,t]]),e[c][1].end.offset-e[c][1].start.offset?(u=2,l=(0,ex.V)(l,[["enter",e[c][1],t],["exit",e[c][1],t]])):u=0,(0,ex.m)(e,n-1,c-n+3,l),c=n+l.length-u-2;break}}for(c=-1;++c<e.length;)"attentionSequence"===e[c][1].type&&(e[c][1].type="data");return e},tokenize:function(e,t){let n,r=this.parser.constructs.attentionMarkers.null,i=this.previous,o=(0,ta.S)(i);return function(s){return n=s,e.enter("attentionSequence"),function s(a){if(a===n)return e.consume(a),s;let l=e.exit("attentionSequence"),u=(0,ta.S)(a),c=!u||2===u&&o||r.includes(a),h=!o||2===o&&u||r.includes(i);return l._open=!!(42===n?c:c&&(o||!h)),l._close=!!(42===n?h:h&&(u||!c)),t(a)}(s)}}};function tu(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let tc={name:"labelStartLink",resolveAll:tn.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},th={42:eU,43:eU,45:eU,48:eU,49:eU,50:eU,51:eU,52:eU,53:eU,54:eU,55:eU,56:eU,57:eU,62:eW},td={91:{name:"definition",tokenize:function(e,t,n){let r,i=this;return function(t){var r;return e.enter("definition"),r=t,eq.call(i,e,o,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(r)};function o(t){return(r=(0,eX.B)(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),s):n(t)}function s(t){return(0,e_.Ee)(t)?eY(e,a)(t):a(t)}function a(t){return e$(e,l,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function l(t){return e.attempt(eG,u,u)(t)}function u(t){return(0,e_.On)(t)?(0,eT.N)(e,c,"whitespace")(t):c(t)}function c(o){return null===o||(0,e_.HP)(o)?(e.exit("definition"),i.parser.defined.push(r),t(o)):n(o)}}}},tf={[-2]:eJ,[-1]:eJ,32:eJ},tp={35:{name:"headingAtx",resolve:function(e,t){let n,r,i=e.length-2,o=3;return"whitespace"===e[3][1].type&&(o+=2),i-2>o&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(o===i-1||i-4>o&&"whitespace"===e[i-2][1].type)&&(i-=o+1===i?2:4),i>o&&(n={type:"atxHeadingText",start:e[o][1].start,end:e[i][1].end},r={type:"chunkText",start:e[o][1].start,end:e[i][1].end,contentType:"text"},(0,ex.m)(e,o,i-o+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(i){var o;return e.enter("atxHeading"),o=i,e.enter("atxHeadingSequence"),function i(o){return 35===o&&r++<6?(e.consume(o),i):null===o||(0,e_.Ee)(o)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||(0,e_.HP)(r)?(e.exit("atxHeading"),t(r)):(0,e_.On)(r)?(0,eT.N)(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||(0,e_.Ee)(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(o)):n(o)}(o)}}},42:ez,45:[eQ,ez],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,i,o,s,a,l=this;return function(t){var n;return n=t,e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(n),u};function u(s){return 33===s?(e.consume(s),c):47===s?(e.consume(s),i=!0,f):63===s?(e.consume(s),r=3,l.interrupt?t:M):(0,e_.CW)(s)?(e.consume(s),o=String.fromCharCode(s),p):n(s)}function c(i){return 45===i?(e.consume(i),r=2,h):91===i?(e.consume(i),r=5,s=0,d):(0,e_.CW)(i)?(e.consume(i),r=4,l.interrupt?t:M):n(i)}function h(r){return 45===r?(e.consume(r),l.interrupt?t:M):n(r)}function d(r){let i="CDATA[";return r===i.charCodeAt(s++)?(e.consume(r),s===i.length)?l.interrupt?t:E:d:n(r)}function f(t){return(0,e_.CW)(t)?(e.consume(t),o=String.fromCharCode(t),p):n(t)}function p(s){if(null===s||47===s||62===s||(0,e_.Ee)(s)){let a=47===s,u=o.toLowerCase();return!a&&!i&&e1.includes(u)?(r=1,l.interrupt?t(s):E(s)):e0.includes(o.toLowerCase())?(r=6,a)?(e.consume(s),m):l.interrupt?t(s):E(s):(r=7,l.interrupt&&!l.parser.lazy[l.now().line]?n(s):i?function t(n){return(0,e_.On)(n)?(e.consume(n),t):k(n)}(s):g(s))}return 45===s||(0,e_.lV)(s)?(e.consume(s),o+=String.fromCharCode(s),p):n(s)}function m(r){return 62===r?(e.consume(r),l.interrupt?t:E):n(r)}function g(t){return 47===t?(e.consume(t),k):58===t||95===t||(0,e_.CW)(t)?(e.consume(t),y):(0,e_.On)(t)?(e.consume(t),g):k(t)}function y(t){return 45===t||46===t||58===t||95===t||(0,e_.lV)(t)?(e.consume(t),y):v(t)}function v(t){return 61===t?(e.consume(t),b):(0,e_.On)(t)?(e.consume(t),v):g(t)}function b(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),a=t,x):(0,e_.On)(t)?(e.consume(t),b):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||(0,e_.Ee)(n)?v(n):(e.consume(n),t)}(t)}function x(t){return t===a?(e.consume(t),a=null,w):null===t||(0,e_.HP)(t)?n(t):(e.consume(t),x)}function w(e){return 47===e||62===e||(0,e_.On)(e)?g(e):n(e)}function k(t){return 62===t?(e.consume(t),S):n(t)}function S(t){return null===t||(0,e_.HP)(t)?E(t):(0,e_.On)(t)?(e.consume(t),S):n(t)}function E(t){return 45===t&&2===r?(e.consume(t),A):60===t&&1===r?(e.consume(t),I):62===t&&4===r?(e.consume(t),O):63===t&&3===r?(e.consume(t),M):93===t&&5===r?(e.consume(t),R):(0,e_.HP)(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(e2,D,T)(t)):null===t||(0,e_.HP)(t)?(e.exit("htmlFlowData"),T(t)):(e.consume(t),E)}function T(t){return e.check(e5,_,D)(t)}function _(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),C}function C(t){return null===t||(0,e_.HP)(t)?T(t):(e.enter("htmlFlowData"),E(t))}function A(t){return 45===t?(e.consume(t),M):E(t)}function I(t){return 47===t?(e.consume(t),o="",P):E(t)}function P(t){if(62===t){let n=o.toLowerCase();return e1.includes(n)?(e.consume(t),O):E(t)}return(0,e_.CW)(t)&&o.length<8?(e.consume(t),o+=String.fromCharCode(t),P):E(t)}function R(t){return 93===t?(e.consume(t),M):E(t)}function M(t){return 62===t?(e.consume(t),O):45===t&&2===r?(e.consume(t),M):E(t)}function O(t){return null===t||(0,e_.HP)(t)?(e.exit("htmlFlowData"),D(t)):(e.consume(t),O)}function D(n){return e.exit("htmlFlow"),t(n)}}},61:eQ,95:ez,96:e6,126:e6},tm={38:e8,92:e7},tg={[-5]:te,[-4]:te,[-3]:te,33:ts,38:e8,42:tl,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(t){return(0,e_.CW)(t)?(e.consume(t),o):64===t?n(t):a(t)}function o(t){return 43===t||45===t||46===t||(0,e_.lV)(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,s):(43===n||45===n||46===n||(0,e_.lV)(n))&&r++<32?(e.consume(n),t):(r=0,a(n))}(t)):a(t)}function s(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||(0,e_.JQ)(r)?n(r):(e.consume(r),s)}function a(t){return 64===t?(e.consume(t),l):(0,e_.cx)(t)?(e.consume(t),a):n(t)}function l(i){return(0,e_.lV)(i)?function i(o){return 46===o?(e.consume(o),r=0,l):62===o?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(o),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(o){if((45===o||(0,e_.lV)(o))&&r++<63){let n=45===o?t:i;return e.consume(o),n}return n(o)}(o)}(i):n(i)}}},{name:"htmlText",tokenize:function(e,t,n){let r,i,o,s=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){return 33===t?(e.consume(t),l):47===t?(e.consume(t),x):63===t?(e.consume(t),v):(0,e_.CW)(t)?(e.consume(t),k):n(t)}function l(t){return 45===t?(e.consume(t),u):91===t?(e.consume(t),i=0,f):(0,e_.CW)(t)?(e.consume(t),y):n(t)}function u(t){return 45===t?(e.consume(t),d):n(t)}function c(t){return null===t?n(t):45===t?(e.consume(t),h):(0,e_.HP)(t)?(o=c,P(t)):(e.consume(t),c)}function h(t){return 45===t?(e.consume(t),d):c(t)}function d(e){return 62===e?I(e):45===e?h(e):c(e)}function f(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?p:f):n(t)}function p(t){return null===t?n(t):93===t?(e.consume(t),m):(0,e_.HP)(t)?(o=p,P(t)):(e.consume(t),p)}function m(t){return 93===t?(e.consume(t),g):p(t)}function g(t){return 62===t?I(t):93===t?(e.consume(t),g):p(t)}function y(t){return null===t||62===t?I(t):(0,e_.HP)(t)?(o=y,P(t)):(e.consume(t),y)}function v(t){return null===t?n(t):63===t?(e.consume(t),b):(0,e_.HP)(t)?(o=v,P(t)):(e.consume(t),v)}function b(e){return 62===e?I(e):v(e)}function x(t){return(0,e_.CW)(t)?(e.consume(t),w):n(t)}function w(t){return 45===t||(0,e_.lV)(t)?(e.consume(t),w):function t(n){return(0,e_.HP)(n)?(o=t,P(n)):(0,e_.On)(n)?(e.consume(n),t):I(n)}(t)}function k(t){return 45===t||(0,e_.lV)(t)?(e.consume(t),k):47===t||62===t||(0,e_.Ee)(t)?S(t):n(t)}function S(t){return 47===t?(e.consume(t),I):58===t||95===t||(0,e_.CW)(t)?(e.consume(t),E):(0,e_.HP)(t)?(o=S,P(t)):(0,e_.On)(t)?(e.consume(t),S):I(t)}function E(t){return 45===t||46===t||58===t||95===t||(0,e_.lV)(t)?(e.consume(t),E):function t(n){return 61===n?(e.consume(n),T):(0,e_.HP)(n)?(o=t,P(n)):(0,e_.On)(n)?(e.consume(n),t):S(n)}(t)}function T(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,_):(0,e_.HP)(t)?(o=T,P(t)):(0,e_.On)(t)?(e.consume(t),T):(e.consume(t),C)}function _(t){return t===r?(e.consume(t),r=void 0,A):null===t?n(t):(0,e_.HP)(t)?(o=_,P(t)):(e.consume(t),_)}function C(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||(0,e_.Ee)(t)?S(t):(e.consume(t),C)}function A(e){return 47===e||62===e||(0,e_.Ee)(e)?S(e):n(e)}function I(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function P(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),R}function R(t){return(0,e_.On)(t)?(0,eT.N)(e,M,"linePrefix",s.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):M(t)}function M(t){return e.enter("htmlTextData"),o(t)}}}],91:tc,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return(0,e_.HP)(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},e7],93:tn,95:tl,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,i,o=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),o++,t):(e.exit("codeTextSequence"),s(n))}(t)};function s(l){return null===l?n(l):32===l?(e.enter("space"),e.consume(l),e.exit("space"),s):96===l?(i=e.enter("codeTextSequence"),r=0,function n(s){return 96===s?(e.consume(s),r++,n):r===o?(e.exit("codeTextSequence"),e.exit("codeText"),t(s)):(i.type="codeTextData",a(s))}(l)):(0,e_.HP)(l)?(e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),s):(e.enter("codeTextData"),a(l))}function a(t){return null===t||32===t||96===t||(0,e_.HP)(t)?(e.exit("codeTextData"),s(t)):(e.consume(t),a)}}}},ty={null:[tl,eD]},tv={null:[42,95]},tb={null:[]},tx=/[\0\t\n\r]/g;function tw(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let tk=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function tS(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return tw(n.slice(t?2:1),t?16:10)}return e9(n)||e}let tE={}.hasOwnProperty;function tT(e){return{line:e.line,column:e.column,offset:e.offset}}function t_(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+Q({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+Q({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+Q({start:t.start,end:t.end})+") is still open")}function tC(e){let t=this;t.parser=function(n){var r,i;let s,a,l,u;return"string"!=typeof(r={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(i=r,r=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(y),autolinkProtocol:u,autolinkEmail:u,atxHeading:r(p),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:u,characterReference:u,codeFenced:r(f),codeFencedFenceInfo:i,codeFencedFenceMeta:i,codeIndented:r(f,i),codeText:r(function(){return{type:"inlineCode",value:""}},i),codeTextData:u,data:u,codeFlowValue:u,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:i,definitionLabelString:i,definitionTitleString:i,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(m),hardBreakTrailing:r(m),htmlFlow:r(g,i),htmlFlowData:u,htmlText:r(g,i),htmlTextData:u,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:i,link:r(y),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(v,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(v),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:i,resourceDestinationString:i,resourceTitleString:i,setextHeading:r(p),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:s(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];t.depth||(t.depth=this.sliceSerialize(e).length)},autolink:s(),autolinkEmail:function(e){c.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){c.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:s(),characterEscapeValue:c,characterReferenceMarkerHexadecimal:d,characterReferenceMarkerNumeric:d,characterReferenceValue:function(e){let t,n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=tw(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=e9(n);let i=this.stack[this.stack.length-1];i.value+=t},characterReference:function(e){this.stack.pop().position.end=tT(e.end)},codeFenced:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:c,codeIndented:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:c,data:c,definition:s(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,eX.B)(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:s(),hardBreakEscape:s(h),hardBreakTrailing:s(h),htmlFlow:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:c,htmlText:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:c,image:s(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];this.data.inReference=!0,"link"===n.type?n.children=e.children:n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=t.replace(tk,tS),n.identifier=(0,eX.B)(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=tT(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(u.call(this,e),c.call(this,e))},link:s(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:s(),listOrdered:s(),listUnordered:s(),paragraph:s(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,eX.B)(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:s(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:s(),thematicBreak:s()}};!function e(t,n){let r=-1;for(;++r<n.length;){let i=n[r];Array.isArray(i)?e(t,i):function(e,t){let n;for(n in t)if(tE.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,i)}}(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},s={stack:[r],tokenStack:[],config:t,enter:o,exit:a,buffer:i,resume:l,data:n},u=[],c=-1;for(;++c<e.length;)("listOrdered"===e[c][1].type||"listUnordered"===e[c][1].type)&&("enter"===e[c][0]?u.push(c):c=function(e,t,n){let r,i,o,s,a=t-1,l=-1,u=!1;for(;++a<=n;){let t=e[a];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?l++:l--,s=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||s||l||o||(o=a),s=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:s=void 0}if(!l&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===l&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let s=a;for(i=void 0;s--;){let t=e[s];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",u=!0),t[1].type="lineEnding",i=s}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}o&&(!i||o<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||a,0,["exit",r,t[2]]),a++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(a,0,["enter",i,t[2]]),a++,n++,o=void 0,s=!0}}}return e[t][1]._spread=u,n}(e,u.pop(),c));for(c=-1;++c<e.length;){let n=t[e[c][0]];tE.call(n,e[c][1].type)&&n[e[c][1].type].call(Object.assign({sliceSerialize:e[c][2].sliceSerialize},s),e[c][1])}if(s.tokenStack.length>0){let e=s.tokenStack[s.tokenStack.length-1];(e[1]||t_).call(s,void 0,e[0])}for(r.position={start:tT(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:tT(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},c=-1;++c<t.transforms.length;)r=t.transforms[c](r)||r;return r};function r(e,t){return function(n){o.call(this,e(n),n),t&&t.call(this,n)}}function i(){this.stack.push({type:"fragment",children:[]})}function o(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:tT(t.start),end:void 0}}function s(e){return function(t){e&&e.call(this,t),a.call(this,t)}}function a(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||t_).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+Q({start:e.start,end:e.end})+"): it’s not open");n.position.end=tT(e.end)}function l(){return(0,eb.d)(this.stack.pop())}function u(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:tT(e.start),end:void 0},t.push(n)),this.stack.push(n)}function c(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=tT(e.end)}function h(){this.data.atHardBreak=!0}function d(e){this.data.characterReferenceType=e.type}function f(){return{type:"code",lang:null,meta:null,value:""}}function p(){return{type:"heading",depth:0,children:[]}}function m(){return{type:"break"}}function g(){return{type:"html",value:""}}function y(){return{type:"link",title:null,url:"",children:[]}}function v(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(i)(function(e){for(;!eS(e););return e}((function(e){let t={constructs:(0,eE.y)([o,...(e||{}).extensions||[]]),content:n(eC),defined:[],document:n(eA),flow:n(eO),lazy:{},string:n(eL),text:n(ej)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},o=[],s=[],a=[],l={attempt:p(function(e,t){m(e,t.from)}),check:p(f),consume:function(e){(0,e_.HP)(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,g()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===s[r._index].length&&(r._bufferIndex=-1,r._index++)),u.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=d(),u.events.push(["enter",n,u]),a.push(n),n},exit:function(e){let t=a.pop();return t.end=d(),u.events.push(["exit",t,u]),t},interrupt:p(f,{interrupt:!0})},u={code:null,containerState:{},defineSkip:function(e){i[e.line]=e.column,g()},events:[],now:d,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,i=[];for(;++r<e.length;){let o,s=e[r];if("string"==typeof s)o=s;else switch(s){case -5:o="\r";break;case -4:o="\n";break;case -3:o="\r\n";break;case -2:o=t?" ":"	";break;case -1:if(!t&&n)continue;o=" ";break;default:o=String.fromCharCode(s)}n=-2===s,i.push(o)}return i.join("")}(h(e),t)},sliceStream:h,write:function(e){return(s=(0,ex.V)(s,e),function(){let e;for(;r._index<s.length;){let n=s[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),c=c(t)}else c=c(n)}}(),null!==s[s.length-1])?[]:(m(t,0),u.events=(0,tt.W)(o,u.events,u),u.events)}},c=t.tokenize.call(u,l);return t.resolveAll&&o.push(t),u;function h(e){return function(e,t){let n,r=t.start._index,i=t.start._bufferIndex,o=t.end._index,s=t.end._bufferIndex;if(r===o)n=[e[r].slice(i,s)];else{if(n=e.slice(r,o),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}s>0&&n.push(e[o].slice(0,s))}return n}(s,e)}function d(){let{_bufferIndex:e,_index:t,line:n,column:i,offset:o}=r;return{_bufferIndex:e,_index:t,line:n,column:i,offset:o}}function f(e,t){t.restore()}function p(e,t){return function(n,i,o){var s;let c,h,f,p;return Array.isArray(n)?m(n):"tokenize"in n?m([n]):(s=n,function(e){let t=null!==e&&s[e],n=null!==e&&s.null;return m([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(n)?n:n?[n]:[]])(e)});function m(e){return(c=e,h=0,0===e.length)?o:y(e[h])}function y(e){return function(n){return(p=function(){let e=d(),t=u.previous,n=u.currentConstruct,i=u.events.length,o=Array.from(a);return{from:i,restore:function(){r=e,u.previous=t,u.currentConstruct=n,u.events.length=i,a=o,g()}}}(),f=e,e.partial||(u.currentConstruct=e),e.name&&u.parser.constructs.disable.null.includes(e.name))?b(n):e.tokenize.call(t?Object.assign(Object.create(u),t):u,l,v,b)(n)}}function v(t){return e(f,p),i}function b(e){return(p.restore(),++h<c.length)?y(c[h]):o}}}function m(e,t){e.resolveAll&&!o.includes(e)&&o.push(e),e.resolve&&(0,ex.m)(u.events,t,u.events.length-t,e.resolve(u.events.slice(t),u)),e.resolveTo&&(u.events=e.resolveTo(u.events,u))}function g(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}(t,e,n)}}})(i).document().write((a=1,l="",u=!0,function(e,t,n){let r,i,o,c,h,d=[];for(e=l+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),o=0,l="",u&&(65279===e.charCodeAt(0)&&o++,u=void 0);o<e.length;){if(tx.lastIndex=o,c=(r=tx.exec(e))&&void 0!==r.index?r.index:e.length,h=e.charCodeAt(c),!r){l=e.slice(o);break}if(10===h&&o===c&&s)d.push(-3),s=void 0;else switch(s&&(d.push(-5),s=void 0),o<c&&(d.push(e.slice(o,c)),a+=c-o),h){case 0:d.push(65533),a++;break;case 9:for(i=4*Math.ceil(a/4),d.push(-2);a++<i;)d.push(-1);break;case 10:d.push(-4),a=1;break;default:s=!0,a=1}o=c+1}return n&&(s&&d.push(-5),l&&d.push(l),d.push(null)),d})(n,r,!0))))}}let tA="object"==typeof self?self:globalThis,tI=e=>((e,t)=>{let n=(t,n)=>(e.set(n,t),t),r=i=>{if(e.has(i))return e.get(i);let[o,s]=t[i];switch(o){case 0:case -1:return n(s,i);case 1:{let e=n([],i);for(let t of s)e.push(r(t));return e}case 2:{let e=n({},i);for(let[t,n]of s)e[r(t)]=r(n);return e}case 3:return n(new Date(s),i);case 4:{let{source:e,flags:t}=s;return n(new RegExp(e,t),i)}case 5:{let e=n(new Map,i);for(let[t,n]of s)e.set(r(t),r(n));return e}case 6:{let e=n(new Set,i);for(let t of s)e.add(r(t));return e}case 7:{let{name:e,message:t}=s;return n(new tA[e](t),i)}case 8:return n(BigInt(s),i);case"BigInt":return n(Object(BigInt(s)),i);case"ArrayBuffer":return n(new Uint8Array(s).buffer,s);case"DataView":{let{buffer:e}=new Uint8Array(s);return n(new DataView(e),s)}}return n(new tA[o](s),i)};return r})(new Map,e)(0),{toString:tP}={},{keys:tR}=Object,tM=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=tP.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},tO=([e,t])=>0===e&&("function"===t||"symbol"===t),tD=(e,{json:t,lossy:n}={})=>{let r=[];return((e,t,n,r)=>{let i=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},o=r=>{if(n.has(r))return n.get(r);let[s,a]=tM(r);switch(s){case 0:{let t=r;switch(a){case"bigint":s=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+a);t=null;break;case"undefined":return i([-1],r)}return i([s,t],r)}case 1:{if(a){let e=r;return"DataView"===a?e=new Uint8Array(r.buffer):"ArrayBuffer"===a&&(e=new Uint8Array(r)),i([a,[...e]],r)}let e=[],t=i([s,e],r);for(let t of r)e.push(o(t));return t}case 2:{if(a)switch(a){case"BigInt":return i([a,r.toString()],r);case"Boolean":case"Number":case"String":return i([a,r.valueOf()],r)}if(t&&"toJSON"in r)return o(r.toJSON());let n=[],l=i([s,n],r);for(let t of tR(r))(e||!tO(tM(r[t])))&&n.push([o(t),o(r[t])]);return l}case 3:return i([s,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return i([s,{source:e,flags:t}],r)}case 5:{let t=[],n=i([s,t],r);for(let[n,i]of r)(e||!(tO(tM(n))||tO(tM(i))))&&t.push([o(n),o(i)]);return n}case 6:{let t=[],n=i([s,t],r);for(let n of r)(e||!tO(tM(n)))&&t.push(o(n));return n}}let{message:l}=r;return i([s,{name:a,message:l}],r)};return o})(!(t||n),!!t,new Map,r)(e),r},tL="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?tI(tD(e,t)):structuredClone(e):(e,t)=>tI(tD(e,t));function tj(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let o=e.charCodeAt(n),s="";if(37===o&&(0,e_.lV)(e.charCodeAt(n+1))&&(0,e_.lV)(e.charCodeAt(n+2)))i=2;else if(o<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(o))||(s=String.fromCharCode(o));else if(o>55295&&o<57344){let t=e.charCodeAt(n+1);o<56320&&t>56319&&t<57344?(s=String.fromCharCode(o,t),i=1):s="�"}else s=String.fromCharCode(o);s&&(t.push(e.slice(r,n),encodeURIComponent(s)),r=n+i+1,s=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function tB(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function tN(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}var tF=n(8428);function tz(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),o=i[0];o&&"text"===o.type?o.value="["+o.value:i.unshift({type:"text",value:"["});let s=i[i.length-1];return s&&"text"===s.type?s.value+=r:i.push({type:"text",value:r}),i}function tU(e){let t=e.spread;return null==t?e.children.length>1:t}function tV(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}let tH={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n,r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),o=tj(i.toLowerCase()),s=e.footnoteOrder.indexOf(i),a=e.footnoteCounts.get(i);void 0===a?(a=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=s+1,a+=1,e.footnoteCounts.set(i,a);let l={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+o,id:r+"fnref-"+o+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,l);let u={type:"element",tagName:"sup",properties:{},children:[l]};return e.patch(t,u),e.applyData(t,u)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return tz(e,t);let i={src:tj(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let o={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,o),e.applyData(t,o)},image:function(e,t){let n={src:tj(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return tz(e,t);let i={href:tj(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let o={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,o),e.applyData(t,o)},link:function(e,t){let n={href:tj(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),i=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=tU(n[r])}return t}(n):tU(t),o={},s=[];if("boolean"==typeof t.checked){let e,n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),o.className=["task-list-item"]}let a=-1;for(;++a<r.length;){let e=r[a];(i||0!==a||"element"!==e.type||"p"!==e.tagName)&&s.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?s.push(e):s.push(...e.children)}let l=r[r.length-1];l&&(i||"element"!==l.type||"p"!==l.tagName)&&s.push({type:"text",value:"\n"});let u={type:"element",tagName:"li",properties:o,children:s};return e.patch(t,u),e.applyData(t,u)},list:function(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let o={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,o),e.applyData(t,o)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},o=J(t.children[1]),s=G(t.children[t.children.length-1]);o&&s&&(r.position={start:o,end:s}),i.push(r)}let o={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,o),e.applyData(t,o)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,i=0===(r?r.indexOf(t):1)?"th":"td",o=n&&"table"===n.type?n.align:void 0,s=o?o.length:t.children.length,a=-1,l=[];for(;++a<s;){let n=t.children[a],r={},s=o?o[a]:void 0;s&&(r.align=s);let u={type:"element",tagName:i,properties:r,children:[]};n&&(u.children=e.all(n),e.patch(n,u),u=e.applyData(n,u)),l.push(u)}let u={type:"element",tagName:"tr",properties:{},children:e.wrap(l,!0)};return e.patch(t,u),e.applyData(t,u)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,o=[];for(;r;)o.push(tV(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return o.push(tV(t.slice(i),i>0,!1)),o.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:tW,yaml:tW,definition:tW,footnoteDefinition:tW};function tW(){}let t$={}.hasOwnProperty,tq={};function tK(e,t){e.position&&(t.position=function(e){let t=J(e),n=G(e);if(t&&n)return{start:t,end:n}}(e))}function tY(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,i=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&i&&Object.assign(n.properties,tL(i)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function tX(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function tG(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function tJ(e,t){let n=function(e,t){let n=t||tq,r=new Map,i=new Map,o={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=o.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=tG(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=tG(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t},applyData:tY,definitionById:r,footnoteById:i,footnoteCounts:new Map,footnoteOrder:[],handlers:{...tH,...n.handlers},one:function(e,t){let n=e.type,r=o.handlers[n];if(t$.call(o.handlers,n)&&r)return r(o,e,t);if(o.options.passThrough&&o.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=tL(n);return r.children=o.all(e),r}return tL(e)}return(o.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(t$.call(n,"hProperties")||t$.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(o,e,t)},options:n,patch:tK,wrap:tX};return(0,tF.YR)(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:i,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),o}(e,t),r=n.one(e,void 0),i=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||tB,r=e.options.footnoteBackLabel||tN,i=e.options.footnoteLabel||"Footnotes",o=e.options.footnoteLabelTagName||"h2",s=e.options.footnoteLabelProperties||{className:["sr-only"]},a=[],l=-1;for(;++l<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[l]);if(!i)continue;let o=e.all(i),s=String(i.identifier).toUpperCase(),u=tj(s.toLowerCase()),c=0,h=[],d=e.footnoteCounts.get(s);for(;void 0!==d&&++c<=d;){h.length>0&&h.push({type:"text",value:" "});let e="string"==typeof n?n:n(l,c);"string"==typeof e&&(e={type:"text",value:e}),h.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+u+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(l,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let f=o[o.length-1];if(f&&"element"===f.type&&"p"===f.tagName){let e=f.children[f.children.length-1];e&&"text"===e.type?e.value+=" ":f.children.push({type:"text",value:" "}),f.children.push(...h)}else o.push(...h);let p={type:"element",tagName:"li",properties:{id:t+"fn-"+u},children:e.wrap(o,!0)};e.patch(i,p),a.push(p)}if(0!==a.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:o,properties:{...tL(s),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(a,!0)},{type:"text",value:"\n"}]}}(n),o=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return i&&((0,c.ok)("children"in o),o.children.push({type:"text",value:"\n"},i)),o}function tZ(e,t){return e&&"run"in e?async function(n,r){let i=tJ(n,{file:r,...t});await e.run(i,r)}:function(n,r){return tJ(n,{file:r,...e||t})}}function tQ(e){if(e)throw e}var t0=n(3360);function t1(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}let t2={basename:function(e,t){let n;if(void 0!==t&&"string"!=typeof t)throw TypeError('"ext" argument must be a string');t5(e);let r=0,i=-1,o=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;o--;)if(47===e.codePointAt(o)){if(n){r=o+1;break}}else i<0&&(n=!0,i=o+1);return i<0?"":e.slice(r,i)}if(t===e)return"";let s=-1,a=t.length-1;for(;o--;)if(47===e.codePointAt(o)){if(n){r=o+1;break}}else s<0&&(n=!0,s=o+1),a>-1&&(e.codePointAt(o)===t.codePointAt(a--)?a<0&&(i=o):(a=-1,i=s));return r===i?i=s:i<0&&(i=e.length),e.slice(r,i)},dirname:function(e){let t;if(t5(e),0===e.length)return".";let n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){let t;t5(e);let n=e.length,r=-1,i=0,o=-1,s=0;for(;n--;){let a=e.codePointAt(n);if(47===a){if(t){i=n+1;break}continue}r<0&&(t=!0,r=n+1),46===a?o<0?o=n:1!==s&&(s=1):o>-1&&(s=-1)}return o<0||r<0||0===s||1===s&&o===r-1&&o===i+1?"":e.slice(o,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)t5(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){t5(e);let t=47===e.codePointAt(0),n=function(e,t){let n,r,i="",o=0,s=-1,a=0,l=-1;for(;++l<=e.length;){if(l<e.length)n=e.codePointAt(l);else if(47===n)break;else n=47;if(47===n){if(s===l-1||1===a);else if(s!==l-1&&2===a){if(i.length<2||2!==o||46!==i.codePointAt(i.length-1)||46!==i.codePointAt(i.length-2)){if(i.length>2){if((r=i.lastIndexOf("/"))!==i.length-1){r<0?(i="",o=0):o=(i=i.slice(0,r)).length-1-i.lastIndexOf("/"),s=l,a=0;continue}}else if(i.length>0){i="",o=0,s=l,a=0;continue}}t&&(i=i.length>0?i+"/..":"..",o=2)}else i.length>0?i+="/"+e.slice(s+1,l):i=e.slice(s+1,l),o=l-s-1;s=l,a=0}else 46===n&&a>-1?a++:a=-1}return i}(e,!t);return 0!==n.length||t||(n="."),n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/"),t?"/"+n:n}(t)},sep:"/"};function t5(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}let t3={cwd:function(){return"/"}};function t6(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let t4=["history","path","basename","stem","extname","dirname"];class t9{constructor(e){let t,n;t=e?t6(e)?{path:e}:"string"==typeof e||function(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":t3.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<t4.length;){let e=t4[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)t4.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?t2.basename(this.path):void 0}set basename(e){t7(e,"basename"),t8(e,"basename"),this.path=t2.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?t2.dirname(this.path):void 0}set dirname(e){ne(this.basename,"dirname"),this.path=t2.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?t2.extname(this.path):void 0}set extname(e){if(t8(e,"extname"),ne(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=t2.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){t6(e)&&(e=function(e){if("string"==typeof e)e=new URL(e);else if(!t6(e)){let t=TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){let e=TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){let e=TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}let t=e.pathname,n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){let e=t.codePointAt(n+2);if(70===e||102===e){let e=TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}(e)),t7(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?t2.basename(this.path,this.extname):void 0}set stem(e){t7(e,"stem"),t8(e,"stem"),this.path=t2.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new er(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function t8(e,t){if(e&&e.includes(t2.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+t2.sep+"`")}function t7(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function ne(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let nt=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},nn={}.hasOwnProperty;class nr extends nt{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);!function i(o,...s){let a=e[++n],l=-1;if(o)return void r(o);for(;++l<t.length;)(null===s[l]||void 0===s[l])&&(s[l]=t[l]);t=s,a?(function(e,t){let n;return function(...t){let o,s=e.length>t.length;s&&t.push(r);try{o=e.apply(this,t)}catch(e){if(s&&n)throw e;return r(e)}s||(o&&o.then&&"function"==typeof o.then?o.then(i,r):o instanceof Error?r(o):i(o))};function r(e,...i){n||(n=!0,t(e,...i))}function i(e){r(null,e)}})(a,i)(...s):r(null,...s)}(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new nr,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(t0(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(na("data",this.frozen),this.namespace[e]=t,this):nn.call(this.namespace,e)&&this.namespace[e]||void 0:e?(na("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=1/0,this}parse(e){this.freeze();let t=nc(e),n=this.parser||this.Parser;return no("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),no("process",this.parser||this.Parser),ns("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,i){let o=nc(e),s=n.parse(o);function a(e,n){e||!n?i(e):r?r(n):((0,c.ok)(t,"`done` is defined if `resolve` is not"),t(void 0,n))}n.run(s,o,function(e,t,r){var i,o;if(e||!t||!r)return a(e);let s=n.stringify(t,r);"string"==typeof(i=s)||(o=i)&&"object"==typeof o&&"byteLength"in o&&"byteOffset"in o?r.value=s:r.result=s,a(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),no("processSync",this.parser||this.Parser),ns("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,tQ(e),t=r}),nu("processSync","process",n),(0,c.ok)(t,"we either bailed on an error or have a tree"),t}run(e,t,n){nl(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?i(void 0,n):new Promise(i);function i(i,o){(0,c.ok)("function"!=typeof t,"`file` can’t be a `done` anymore, we checked");let s=nc(t);r.run(e,s,function(t,r,s){let a=r||e;t?o(t):i?i(a):((0,c.ok)(n,"`done` is defined if `resolve` is not"),n(void 0,a,s))})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){tQ(e),n=t,r=!0}),nu("runSync","run",r),(0,c.ok)(n,"we either bailed on an error or have a tree"),n}stringify(e,t){this.freeze();let n=nc(t),r=this.compiler||this.Compiler;return ns("stringify",r),nl(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(na("use",this.frozen),null==e);else if("function"==typeof e)s(e,t);else if("object"==typeof e)Array.isArray(e)?o(e):i(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function i(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");o(e.plugins),e.settings&&(r.settings=t0(!0,r.settings,e.settings))}function o(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;){var n=e[t];if("function"==typeof n)s(n,[]);else if("object"==typeof n)if(Array.isArray(n)){let[e,...t]=n;s(e,t)}else i(n);else throw TypeError("Expected usable value, not `"+n+"`")}else throw TypeError("Expected a list of plugins, not `"+e+"`")}function s(e,t){let r=-1,i=-1;for(;++r<n.length;)if(n[r][0]===e){i=r;break}if(-1===i)n.push([e,...t]);else if(t.length>0){let[r,...o]=t,s=n[i][1];t1(s)&&t1(r)&&(r=t0(!0,s,r)),n[i]=[e,r,...o]}}}}let ni=new nr().freeze();function no(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function ns(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function na(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function nl(e){if(!t1(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function nu(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function nc(e){var t;return(t=e)&&"object"==typeof t&&"message"in t&&"messages"in t?e:new t9(e)}let nh=[],nd={allowDangerousHtml:!0},nf=/^(https?|ircs?|mailto|xmpp)$/i,np=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function nm(e){let t=function(e){let t=e.rehypePlugins||nh,n=e.remarkPlugins||nh,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...nd}:nd;return ni().use(tC).use(n).use(tZ,r).use(t)}(e),n=function(e){let t=e.children||"",n=new t9;return"string"==typeof t?n.value=t:(0,c.HB)("Unexpected value `"+t+"` for `children` prop, expected `string`"),n}(e);return function(e,t){let n=t.allowedElements,r=t.allowElement,i=t.components,o=t.disallowedElements,s=t.skipHtml,a=t.unwrapDisallowed,l=t.urlTransform||ng;for(let e of np)Object.hasOwn(t,e.from)&&(0,c.HB)("Unexpected `"+e.from+"` prop, "+(e.to?"use `"+e.to+"` instead":"remove it")+" (see <https://github.com/remarkjs/react-markdown/blob/main/changelog.md#"+e.id+"> for more info)");return n&&o&&(0,c.HB)("Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other"),(0,tF.YR)(e,function(e,t,i){if("raw"===e.type&&i&&"number"==typeof t)return s?i.children.splice(t,1):i.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in ey)if(Object.hasOwn(ey,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=ey[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=l(String(n||""),t,e))}}if("element"===e.type){let s=n?!n.includes(e.tagName):!!o&&o.includes(e.tagName);if(!s&&r&&"number"==typeof t&&(s=!r(e,t,i)),s&&i&&"number"==typeof t)return a&&e.children?i.children.splice(t,1,...e.children):i.children.splice(t,1),t}}),function(e,t){var n,r,i,o;let s;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let a=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=a,r=t.jsxDEV,s=function(e,t,i,o){let s=Array.isArray(i.children),a=J(e);return r(t,i,o,s,{columnNumber:a?a.column-1:void 0,fileName:n,lineNumber:a?a.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");i=t.jsx,o=t.jsxs,s=function(e,t,n,r){let s=Array.isArray(n.children)?o:i;return r?s(t,n,r):s(t,n)}}let l={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:s,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:a,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?V:U,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},u=ec(l,e,void 0);return u&&"string"!=typeof u?u:l.create(e,l.Fragment,{children:u||void 0},void 0)}(e,{Fragment:ev.Fragment,components:i,ignoreInvalidStyle:!0,jsx:ev.jsx,jsxs:ev.jsxs,passKeys:!0,passNode:!0})}(t.runSync(t.parse(n),n),e)}function ng(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return -1===t||-1!==i&&t>i||-1!==n&&t>n||-1!==r&&t>r||nf.test(e.slice(0,t))?e:""}var ny=n(3829),nv=({node:e,...t})=>(0,ev.jsx)("pre",{...t}),nb=({node:e,...t})=>(0,ev.jsx)("code",{...t}),nx=({node:e,components:{Pre:t,Code:n},code:r})=>(0,ev.jsx)(t,{children:(0,ev.jsx)(n,{node:e,children:r})}),nw=()=>null,nk=n(9033),nS=({node:e,components:{Pre:t,Code:n,SyntaxHighlighter:r,CodeHeader:i},language:o,code:s})=>{let a=(0,u.useMemo)(()=>({Pre:t,Code:n}),[t,n]),l=o?r:nx;return(0,ev.jsxs)(ev.Fragment,{children:[(0,ev.jsx)(i,{node:e,language:o,code:s}),(0,ev.jsx)(l,{node:e,components:a,language:o??"unknown",code:s})]})},nE=n(9300),nT=({className:e,...t})=>({className:n,...r})=>({className:nE(e,n),...t,...r}),n_=n(9912),nC=({node:e,components:{Pre:t,Code:n,SyntaxHighlighter:r,CodeHeader:i},componentsByLanguage:o={},children:s,...a})=>{let l=nT((0,u.useContext)(ny.fv)),c=(0,nk.c)(e=>(0,ev.jsx)(t,{...l(e)})),h=nT(a),d=(0,nk.c)(e=>(0,ev.jsx)(n,{...h(e)})),f=/language-(\w+)/.exec(a.className||"")?.[1]??"";if("string"!=typeof s)return(0,ev.jsx)(nx,{node:e,components:{Pre:c,Code:d},code:s});let p=o[f]?.SyntaxHighlighter??r,m=o[f]?.CodeHeader??i;return(0,ev.jsx)(nS,{node:e,components:{Pre:c,Code:d,SyntaxHighlighter:p,CodeHeader:m},language:f||"unknown",code:s})},nA=(0,u.memo)(({node:e,components:t,componentsByLanguage:n,...r})=>(0,ny.$7)()?(0,ev.jsx)(nC,{node:e,components:t,componentsByLanguage:n,...r}):(0,ev.jsx)(t.Code,{...r}),(e,t)=>e.components===t.components&&e.componentsByLanguage===t.componentsByLanguage&&(0,n_.x1)(e,t)),{cg:nI,a2:nP,D0:nR}=r,nM=e=>{let{components:t,componentsByLanguage:n,smooth:r=!0,preprocess:i,...o}=e,s=(0,l.d)(),{text:a}=nI((0,u.useMemo)(()=>i?{...s,text:i(s.text)}:s,[s,i]),r),{pre:c=nv,code:h=nb,SyntaxHighlighter:d=nx,CodeHeader:f=nw}=null!=t?t:{},p=(0,u.useMemo)(()=>({Pre:c,Code:h,SyntaxHighlighter:d,CodeHeader:f}),[c,h,d,f]),m=(0,nk.c)(e=>(0,ev.jsx)(nA,{components:p,componentsByLanguage:n,...e})),g=(0,u.useMemo)(()=>{let{pre:e,code:n,SyntaxHighlighter:r,CodeHeader:i,...o}=null!=t?t:{};return{...o,pre:ny.U_,code:m}},[m,t]);return(0,ev.jsx)(nm,{components:g,...o,children:a})},nO=(0,u.forwardRef)((e,t)=>{let{className:n,containerProps:r,containerComponent:i="div",...o}=e,s=nP();return(0,ev.jsx)(i,{"data-status":s.type,...r,className:nE(n,null==r?void 0:r.className),ref:t,children:(0,ev.jsx)(nM,{...o})})});nO.displayName="MarkdownTextPrimitive";var nD=nR(nO)},811:(e,t,n)=>{"use strict";n.d(t,{P:()=>s});var r=n(8716),i=n(2115),o=n(7589),s=(0,r.x)("ThreadPrimitive.ScrollToBottom",()=>{let e=(0,o.qZ)(e=>e.isAtBottom),t=(0,o.D0)(),n=(0,i.useCallback)(()=>{t.getState().scrollToBottom()},[t]);return e?null:n})},949:(e,t,n)=>{"use strict";n.d(t,{ME:()=>u,iW:()=>s,oM:()=>l});var r=n(2115),i=n(4354),o=n(2288),s=(0,r.createContext)(null),a=(0,i.h)(s,"a component passed to <MessagePrimitive.Parts components={...}>");function l(e){let t=a(e);return t?t.useMessagePartRuntime():null}var u=(0,o.k)(l)},1174:(e,t,n)=>{"use strict";n.d(t,{D:()=>o});var r=Symbol.for("aui.tool-response"),i=class e{get[r](){return!0}artifact;result;isError;constructor(e){void 0!==e.artifact&&(this.artifact=e.artifact),this.result=e.result,this.isError=e.isError??!1}static[Symbol.hasInstance](e){return"object"==typeof e&&null!==e&&r in e}static toResponse(t){return t instanceof e?t:new e({result:void 0===t?"<no result>":t})}},o=class{constructor(e,t,n){this.contentBinding=e,this.messageApi=t,this.threadApi=n}get path(){return this.contentBinding.path}__internal_bindMethods(){this.addToolResult=this.addToolResult.bind(this),this.getState=this.getState.bind(this),this.subscribe=this.subscribe.bind(this)}getState(){return this.contentBinding.getState()}addToolResult(e){let t=this.contentBinding.getState();if(!t)throw Error("Message part is not available");if("tool-call"!==t.type)throw Error("Tried to add tool result to non-tool message part");if(!this.messageApi)throw Error("Message API is not available. This is likely a bug in assistant-ui.");if(!this.threadApi)throw Error("Thread API is not available");let n=this.messageApi.getState();if(!n)throw Error("Message is not available");let r=t.toolName,o=t.toolCallId,s=i.toResponse(e);this.threadApi.getState().addToolResult({messageId:n.id,toolName:r,toolCallId:o,result:s.result,artifact:s.artifact,isError:s.isError})}subscribe(e){return this.contentBinding.subscribe(e)}}},1246:(e,t,n)=>{"use strict";n.d(t,{c:()=>c});var r=n(2115),i=n(6882),o=n(9033),s=n(6906),a=n(9935),l=class{start(){null===this.animationFrameId&&(this.lastUpdateTime=Date.now(),this.animate())}stop(){null!==this.animationFrameId&&(cancelAnimationFrame(this.animationFrameId),this.animationFrameId=null)}constructor(e,t){this.animationFrameId=null,this.lastUpdateTime=Date.now(),this.targetText="",this.animate=()=>{let e=Date.now(),t=e-this.lastUpdateTime,n=this.targetText.length-this.currentText.length,r=Math.min(5,250/n),i=0;for(;t>=r&&i<n;)i++,t-=r;i!==n?this.animationFrameId=requestAnimationFrame(this.animate):this.animationFrameId=null,0!==i&&(this.currentText=this.targetText.slice(0,this.currentText.length+i),this.lastUpdateTime=e-t,this.setText(this.currentText))},this.currentText=e,this.setText=t}},u=Object.freeze({type:"running"}),c=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],{text:n}=e,c=(0,i.JX)({optional:!0,selector:e=>e.id}),h=(0,r.useRef)(c),[d,f]=(0,r.useState)(n),p=(0,s.Bs)({optional:!0}),m=(0,o.c)(t=>{if(f(t),p){let n=d!==t||"running"===e.status.type?u:e.status;(0,a.s)(p).setState(n,!0)}});(0,r.useEffect)(()=>{if(p){let t=d!==n||"running"===e.status.type?u:e.status;(0,a.s)(p).setState(t,!0)}},[p,n,d,e.status]);let[g]=(0,r.useState)(new l(n,m));return(0,r.useEffect)(()=>{if(!t)return void g.stop();if(h.current!==c||!n.startsWith(g.targetText)){h.current=c,m(n),g.currentText=n,g.targetText=n,g.stop();return}g.targetText=n,g.start()},[m,g,c,t,n]),(0,r.useEffect)(()=>()=>{g.stop()},[g]),(0,r.useMemo)(()=>t?{type:"text",text:d,status:n===d?e.status:u}:e,[t,d,e,n])}},1285:(e,t,n)=>{"use strict";n.d(t,{B:()=>l});var r,i=n(2115),o=n(2712),s=(r||(r=n.t(i,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function l(e){let[t,n]=i.useState(s());return(0,o.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},1300:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,i=/^[^-]+$/,o=/^-(webkit|moz|ms|o|khtml)-/,s=/^-(ms)-/,a=function(e,t){return t.toUpperCase()},l=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var u;return(void 0===t&&(t={}),!(u=e)||i.test(u)||n.test(u))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(s,l):e.replace(o,l)).replace(r,a))}},1386:(e,t,n)=>{"use strict";n.d(t,{Xi:()=>u,pK:()=>l,vz:()=>s});var r=n(2115),i=n(4354),o=n(2288),s=(0,r.createContext)(null),a=(0,i.h)(s,"a component passed to <ThreadListPrimitive.Items components={...}>");function l(e){let t=a(e);return t?t.useThreadListItemRuntime():null}var u=(0,o.k)(l)},1557:(e,t,n)=>{"use strict";n.d(t,{H:()=>s});var r=n(8716),i=n(2115),o=n(6882),s=(0,r.x)("BranchPickerPrimitive.Next",()=>{let e=(0,o.LN)(),t=(0,o.JX)(e=>e.branchNumber>=e.branchCount),n=(0,i.useCallback)(()=>{e.switchToBranch({position:"next"})},[e]);return t?null:n})},1595:(e,t,n)=>{"use strict";n.d(t,{U:()=>o});var r=n(2115),i=n(9033);function o(e,t=globalThis?.document){let n=(0,i.c)(e);r.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}},1603:(e,t,n)=>{"use strict";function r(e,t,n,r){let i,o=e.length,s=0;if(t=t<0?-t>o?0:o+t:t>o?o:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);s<r.length;)(i=r.slice(s,s+1e4)).unshift(t,0),e.splice(...i),s+=1e4,t+=1e4}function i(e,t){return e.length>0?(r(e,e.length,0,t),e):t}n.d(t,{V:()=>i,m:()=>r})},1604:(e,t,n)=>{"use strict";n.d(t,{C:()=>s});var r=n(3655),i=n(2115),o=n(5155),s=(0,i.forwardRef)((e,t)=>(0,o.jsx)(r.sG.div,{...e,ref:t}));s.displayName="ThreadListPrimitive.Root"},1741:(e,t)=>{"use strict";t.byteLength=function(e){var t=l(e),n=t[0],r=t[1];return(n+r)*3/4-r},t.toByteArray=function(e){var t,n,o=l(e),s=o[0],a=o[1],u=new i((s+a)*3/4-a),c=0,h=a>0?s-4:s;for(n=0;n<h;n+=4)t=r[e.charCodeAt(n)]<<18|r[e.charCodeAt(n+1)]<<12|r[e.charCodeAt(n+2)]<<6|r[e.charCodeAt(n+3)],u[c++]=t>>16&255,u[c++]=t>>8&255,u[c++]=255&t;return 2===a&&(t=r[e.charCodeAt(n)]<<2|r[e.charCodeAt(n+1)]>>4,u[c++]=255&t),1===a&&(t=r[e.charCodeAt(n)]<<10|r[e.charCodeAt(n+1)]<<4|r[e.charCodeAt(n+2)]>>2,u[c++]=t>>8&255,u[c++]=255&t),u},t.fromByteArray=function(e){for(var t,r=e.length,i=r%3,o=[],s=0,a=r-i;s<a;s+=16383)o.push(function(e,t,r){for(var i,o=[],s=t;s<r;s+=3)i=(e[s]<<16&0xff0000)+(e[s+1]<<8&65280)+(255&e[s+2]),o.push(n[i>>18&63]+n[i>>12&63]+n[i>>6&63]+n[63&i]);return o.join("")}(e,s,s+16383>a?a:s+16383));return 1===i?o.push(n[(t=e[r-1])>>2]+n[t<<4&63]+"=="):2===i&&o.push(n[(t=(e[r-2]<<8)+e[r-1])>>10]+n[t>>4&63]+n[t<<2&63]+"="),o.join("")};for(var n=[],r=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,a=o.length;s<a;++s)n[s]=o[s],r[o.charCodeAt(s)]=s;function l(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var n=e.indexOf("=");-1===n&&(n=t);var r=n===t?0:4-n%4;return[n,r]}r[45]=62,r[95]=63},1745:(e,t,n)=>{"use strict";n.d(t,{v:()=>e4});var r,i=n(2115),o=class{_providers=new Set;getModelContext(){return Array.from(this._providers).map(e=>e.getModelContext()).sort((e,t)=>(t.priority??0)-(e.priority??0)).reduce((e,t)=>{if(t.system&&(e.system?e.system+=`

${t.system}`:e.system=t.system),t.tools)for(let[n,r]of Object.entries(t.tools)){let t=e.tools?.[n];if(t&&t!==r)throw Error(`You tried to define a tool with the name ${n}, but it already exists.`);e.tools||(e.tools={}),e.tools[n]=r}return t.config&&(e.config={...e.config,...t.config}),t.callSettings&&(e.callSettings={...e.callSettings,...t.callSettings}),e},{})}registerModelContextProvider(e){this._providers.add(e);let t=e.subscribe?.(()=>{this.notifySubscribers()});return this.notifySubscribers(),()=>{this._providers.delete(e),t?.(),this.notifySubscribers()}}_subscribers=new Set;notifySubscribers(){for(let e of this._subscribers)e()}subscribe(e){return this._subscribers.add(e),()=>this._subscribers.delete(e)}},s=class{_contextProvider=new o;registerModelContextProvider(e){return this._contextProvider.registerModelContextProvider(e)}},a=n(4866),l=Symbol("aui.parse-partial-json-object.meta"),u=e=>{if(0===e.length)return{[l]:{state:"partial",partialPath:[]}};try{let t=a.parse(e);if("object"!=typeof t||null===t)throw Error("argsText is expected to be an object");return t[l]={state:"complete",partialPath:[]},t}catch{try{let[t,n]=function(e){let t,n=["ROOT"],r=-1,i=null,o=[];function s(){void 0!==t&&(o.push(JSON.parse('"'+t+'"')),t=void 0)}function a(e,t,o){switch(e){case'"':r=t,n.pop(),n.push(o),n.push("INSIDE_STRING"),s();break;case"f":case"t":case"n":r=t,i=t,n.pop(),n.push(o),n.push("INSIDE_LITERAL");break;case"-":n.pop(),n.push(o),n.push("INSIDE_NUMBER"),s();break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":r=t,n.pop(),n.push(o),n.push("INSIDE_NUMBER"),s();break;case"{":r=t,n.pop(),n.push(o),n.push("INSIDE_OBJECT_START"),s();break;case"[":r=t,n.pop(),n.push(o),n.push("INSIDE_ARRAY_START"),s()}}function l(e,i){switch(e){case",":n.pop(),n.push("INSIDE_OBJECT_AFTER_COMMA");break;case"}":r=i,n.pop(),t=o.pop()}}function u(e,i){switch(e){case",":n.pop(),n.push("INSIDE_ARRAY_AFTER_COMMA"),t=(Number(t)+1).toString();break;case"]":r=i,n.pop(),t=o.pop()}}for(let s=0;s<e.length;s++){let c=e[s];switch(n[n.length-1]){case"ROOT":a(c,s,"FINISH");break;case"INSIDE_OBJECT_START":switch(c){case'"':n.pop(),n.push("INSIDE_OBJECT_KEY"),t="";break;case"}":r=s,n.pop(),t=o.pop()}break;case"INSIDE_OBJECT_AFTER_COMMA":'"'===c&&(n.pop(),n.push("INSIDE_OBJECT_KEY"),t="");break;case"INSIDE_OBJECT_KEY":switch(c){case'"':n.pop(),n.push("INSIDE_OBJECT_AFTER_KEY");break;case"\\":n.push("INSIDE_STRING_ESCAPE"),t+=c;break;default:t+=c}break;case"INSIDE_OBJECT_AFTER_KEY":":"===c&&(n.pop(),n.push("INSIDE_OBJECT_BEFORE_VALUE"));break;case"INSIDE_OBJECT_BEFORE_VALUE":a(c,s,"INSIDE_OBJECT_AFTER_VALUE");break;case"INSIDE_OBJECT_AFTER_VALUE":l(c,s);break;case"INSIDE_STRING":switch(c){case'"':n.pop(),r=s,t=o.pop();break;case"\\":n.push("INSIDE_STRING_ESCAPE");break;default:r=s}break;case"INSIDE_ARRAY_START":"]"===c?(r=s,n.pop(),t=o.pop()):(r=s,t="0",a(c,s,"INSIDE_ARRAY_AFTER_VALUE"));break;case"INSIDE_ARRAY_AFTER_VALUE":switch(c){case",":n.pop(),n.push("INSIDE_ARRAY_AFTER_COMMA"),t=(Number(t)+1).toString();break;case"]":r=s,n.pop(),t=o.pop();break;default:r=s}break;case"INSIDE_ARRAY_AFTER_COMMA":a(c,s,"INSIDE_ARRAY_AFTER_VALUE");break;case"INSIDE_STRING_ESCAPE":n.pop(),"INSIDE_STRING"===n[n.length-1]?r=s:"INSIDE_OBJECT_KEY"===n[n.length-1]&&(t+=c);break;case"INSIDE_NUMBER":switch(c){case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":r=s;break;case"e":case"E":case"-":case".":break;case",":n.pop(),t=o.pop(),"INSIDE_ARRAY_AFTER_VALUE"===n[n.length-1]&&u(c,s),"INSIDE_OBJECT_AFTER_VALUE"===n[n.length-1]&&l(c,s);break;case"}":n.pop(),t=o.pop(),"INSIDE_OBJECT_AFTER_VALUE"===n[n.length-1]&&l(c,s);break;case"]":n.pop(),t=o.pop(),"INSIDE_ARRAY_AFTER_VALUE"===n[n.length-1]&&u(c,s);break;default:n.pop(),t=o.pop()}break;case"INSIDE_LITERAL":{let t=e.substring(i,s+1);"false".startsWith(t)||"true".startsWith(t)||"null".startsWith(t)?r=s:(n.pop(),"INSIDE_OBJECT_AFTER_VALUE"===n[n.length-1]?l(c,s):"INSIDE_ARRAY_AFTER_VALUE"===n[n.length-1]&&u(c,s))}}}let c=e.slice(0,r+1);for(let t=n.length-1;t>=0;t--)switch(n[t]){case"INSIDE_STRING":c+='"';break;case"INSIDE_OBJECT_KEY":case"INSIDE_OBJECT_AFTER_KEY":case"INSIDE_OBJECT_AFTER_COMMA":case"INSIDE_OBJECT_START":case"INSIDE_OBJECT_BEFORE_VALUE":case"INSIDE_OBJECT_AFTER_VALUE":c+="}";break;case"INSIDE_ARRAY_START":case"INSIDE_ARRAY_AFTER_COMMA":case"INSIDE_ARRAY_AFTER_VALUE":c+="]";break;case"INSIDE_LITERAL":{let t=e.substring(i,e.length);"true".startsWith(t)?c+="true".slice(t.length):"false".startsWith(t)?c+="false".slice(t.length):"null".startsWith(t)&&(c+="null".slice(t.length))}}return[c,o]}(e),r=a.parse(t);if("object"!=typeof r||null===r)throw Error("argsText is expected to be an object");return r[l]={state:"partial",partialPath:n},r}catch{return}}};let c=(e,t=21)=>(n=t)=>{let r="",i=0|n;for(;i--;)r+=e[Math.random()*e.length|0];return r};var h=c("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",7),d=()=>`__optimistic__${h()}`,f=(e,t,n)=>{let{role:r,id:i,createdAt:o,attachments:s,status:a,metadata:l}=e,c={id:i??t,createdAt:o??new Date},d="string"==typeof e.content?[{type:"text",text:e.content}]:e.content;if("user"!==r&&s?.length)throw Error("attachments are only supported for user messages");if("assistant"!==r&&a)throw Error("status is only supported for assistant messages");if("assistant"!==r&&l?.steps)throw Error("metadata.steps is only supported for assistant messages");switch(r){case"assistant":return{...c,role:r,content:d.map(e=>{let t=e.type;switch(t){case"text":case"reasoning":if(0===e.text.trim().length)return null;return e;case"file":case"source":return e;case"image":return(({image:e,...t})=>e.match(/^data:image\/(png|jpeg|jpg|gif|webp);base64,(.*)$/)?{...t,image:e}:(console.warn("Invalid image data format detected"),null))(e);case"tool-call":{let{parentId:t,...n}=e,r={...n,toolCallId:e.toolCallId??"tool-"+h(),...void 0!==t&&{parentId:t}};if(e.args)return{...r,args:e.args,argsText:JSON.stringify(e.args)};return{...r,args:e.args??u(e.argsText??"")??{},argsText:e.argsText??""}}default:throw Error(`Unsupported assistant message part type: ${t}`)}}).filter(e=>!!e),status:a??n,metadata:{unstable_state:l?.unstable_state??null,unstable_annotations:l?.unstable_annotations??[],unstable_data:l?.unstable_data??[],custom:l?.custom??{},steps:l?.steps??[]}};case"user":return{...c,role:r,content:d.map(e=>{let t=e.type;switch(t){case"text":case"image":case"audio":case"file":return e;default:throw Error(`Unsupported user message part type: ${t}`)}}),attachments:s??[],metadata:{custom:l?.custom??{}}};case"system":if(1!==d.length||"text"!==d[0].type)throw Error("System messages must have exactly one text message part.");return{...c,role:r,content:d,metadata:{custom:l?.custom??{}}};default:throw Error(`Unknown message role: ${r}`)}},p=(e,t)=>void 0===t?e.status?.type==="requires-action"&&"tool-calls"===e.status.reason&&e.content.every(e=>"tool-call"!==e.type||!!e.result):e.status?.type==="requires-action"&&"tool-calls"===e.status.reason&&e.content.every(e=>"tool-call"!==e.type||!!e.result||!t.includes(e.toolName)),m=(Object.freeze({type:"running"}),Object.freeze({type:"complete",reason:"unknown"})),g=Object.freeze({type:"requires-action",reason:"tool-calls"}),y={fromArray:e=>{let t=e.map(e=>f(e,h(),m));return{messages:t.map((e,n)=>({parentId:n>0?t[n-1].id:null,message:e}))}}},v=e=>e.next?v(e.next):"current"in e?e:null,b=class{constructor(e){this.func=e}_value=null;get value(){return null===this._value&&(this._value=this.func()),this._value}dirty(){this._value=null}},x=class{messages=new Map;head=null;root={children:[],next:null};performOp(e,t,n){let r=t.prev??this.root,i=e??this.root;if("relink"!==n||r!==i){if("link"!==n&&(r.children=r.children.filter(e=>e!==t.current.id),r.next===t)){let e=r.children.at(-1),t=e?this.messages.get(e):null;if(void 0===t)throw Error("MessageRepository(performOp/cut): Fallback sibling message not found. This is likely an internal bug in assistant-ui.");r.next=t}if("cut"!==n){for(let n=e;n;n=n.prev)if(n.current.id===t.current.id)throw Error("MessageRepository(performOp/link): A message with the same id already exists in the parent tree. This error occurs if the same message id is found multiple times. This is likely an internal bug in assistant-ui.");i.children=[...i.children,t.current.id],(v(t)===this.head||null===i.next)&&(i.next=t),t.prev=e}}}_messages=new b(()=>{let e=Array(this.head?.level??0);for(let t=this.head;t;t=t.prev)e[t.level]=t.current;return e});get headId(){return this.head?.current.id??null}getMessages(){return this._messages.value}addOrUpdateMessage(e,t){let n=this.messages.get(t.id),r=e?this.messages.get(e):null;if(void 0===r)throw Error("MessageRepository(addOrUpdateMessage): Parent message not found. This is likely an internal bug in assistant-ui.");if(n){n.current=t,this.performOp(r,n,"relink"),this._messages.dirty();return}let i={prev:r,current:t,next:null,children:[],level:r?r.level+1:0};this.messages.set(t.id,i),this.performOp(r,i,"link"),this.head===r&&(this.head=i),this._messages.dirty()}getMessage(e){let t=this.messages.get(e);if(!t)throw Error("MessageRepository(updateMessage): Message not found. This is likely an internal bug in assistant-ui.");return{parentId:t.prev?.current.id??null,message:t.current}}appendOptimisticMessage(e,t){let n;do n=d();while(this.messages.has(n));return this.addOrUpdateMessage(e,f(t,n,{type:"running"})),n}deleteMessage(e,t){let n=this.messages.get(e);if(!n)throw Error("MessageRepository(deleteMessage): Message not found. This is likely an internal bug in assistant-ui.");let r=void 0===t?n.prev:null===t?null:this.messages.get(t);if(void 0===r)throw Error("MessageRepository(deleteMessage): Replacement not found. This is likely an internal bug in assistant-ui.");for(let e of n.children){let t=this.messages.get(e);if(!t)throw Error("MessageRepository(deleteMessage): Child message not found. This is likely an internal bug in assistant-ui.");this.performOp(r,t,"relink")}this.performOp(null,n,"cut"),this.messages.delete(e),this.head===n&&(this.head=v(r??this.root)),this._messages.dirty()}getBranches(e){let t=this.messages.get(e);if(!t)throw Error("MessageRepository(getBranches): Message not found. This is likely an internal bug in assistant-ui.");let{children:n}=t.prev??this.root;return n}switchToBranch(e){let t=this.messages.get(e);if(!t)throw Error("MessageRepository(switchToBranch): Branch not found. This is likely an internal bug in assistant-ui.");(t.prev??this.root).next=t,this.head=v(t),this._messages.dirty()}resetHead(e){if(null===e){this.head=null,this._messages.dirty();return}let t=this.messages.get(e);if(!t)throw Error("MessageRepository(resetHead): Branch not found. This is likely an internal bug in assistant-ui.");this.head=t;for(let e=t;e;e=e.prev)e.prev&&(e.prev.next=e);this._messages.dirty()}clear(){this.messages.clear(),this.head=null,this.root={children:[],next:null},this._messages.dirty()}export(){let e=[];for(let[,t]of this.messages)e.push({message:t.current,parentId:t.prev?.current.id??null});return{headId:this.head?.current.id??null,messages:e}}import({headId:e,messages:t}){for(let{message:e,parentId:n}of t)this.addOrUpdateMessage(n,e);this.resetHead(e??t.at(-1)?.message.id??null)}},w=class{_subscribers=new Set;subscribe(e){return this._subscribers.add(e),()=>this._subscribers.delete(e)}waitForUpdate(){return new Promise(e=>{let t=this.subscribe(()=>{t(),e()})})}_notifySubscribers(){let e=[];for(let t of this._subscribers)try{t()}catch(t){e.push(t)}if(e.length>0)if(1===e.length)throw e[0];else throw AggregateError(e)}},k=class extends w{isEditing=!0;getAttachmentAccept(){return this.getAttachmentAdapter()?.accept??"*"}_attachments=[];get attachments(){return this._attachments}setAttachments(e){this._attachments=e,this._notifySubscribers()}get isEmpty(){return!this.text.trim()&&!this.attachments.length}_text="";get text(){return this._text}_role="user";get role(){return this._role}_runConfig={};get runConfig(){return this._runConfig}setText(e){this._text!==e&&(this._text=e,this._notifySubscribers())}setRole(e){this._role!==e&&(this._role=e,this._notifySubscribers())}setRunConfig(e){this._runConfig!==e&&(this._runConfig=e,this._notifySubscribers())}_emptyTextAndAttachments(){this._attachments=[],this._text="",this._notifySubscribers()}async _onClearAttachments(){let e=this.getAttachmentAdapter();e&&await Promise.all(this._attachments.map(t=>e.remove(t)))}async reset(){if(0===this._attachments.length&&""===this._text&&"user"===this._role&&0===Object.keys(this._runConfig).length)return;this._role="user",this._runConfig={};let e=this._onClearAttachments();this._emptyTextAndAttachments(),await e}async clearAttachments(){let e=this._onClearAttachments();this.setAttachments([]),await e}async send(){let e=this.getAttachmentAdapter(),t=e&&this.attachments.length>0?await Promise.all(this.attachments.map(async t=>"complete"===t.status.type?t:await e.send(t))):[],n={createdAt:new Date,role:this.role,content:this.text?[{type:"text",text:this.text}]:[],attachments:t,runConfig:this.runConfig,metadata:{custom:{}}};this._emptyTextAndAttachments(),this.handleSend(n),this._notifyEventSubscribers("send")}cancel(){this.handleCancel()}async addAttachment(e){let t=this.getAttachmentAdapter();if(!t)throw Error("Attachments are not supported");let n=e=>{let t=this._attachments.findIndex(t=>t.id===e.id);-1!==t?this._attachments=[...this._attachments.slice(0,t),e,...this._attachments.slice(t+1)]:(this._attachments=[...this._attachments,e],this._notifyEventSubscribers("attachment_add")),this._notifySubscribers()},r=t.add({file:e});if(Symbol.asyncIterator in r)for await(let e of r)n(e);else n(await r);this._notifyEventSubscribers("attachment_add"),this._notifySubscribers()}async removeAttachment(e){let t=this.getAttachmentAdapter();if(!t)throw Error("Attachments are not supported");let n=this._attachments.findIndex(t=>t.id===e);if(-1===n)throw Error("Attachment not found");let r=this._attachments[n];await t.remove(r),this._attachments=[...this._attachments.slice(0,n),...this._attachments.slice(n+1)],this._notifySubscribers()}_eventSubscribers=new Map;_notifyEventSubscribers(e){let t=this._eventSubscribers.get(e);if(t)for(let e of t)e()}unstable_on(e,t){let n=this._eventSubscribers.get(e);return n?n.add(t):this._eventSubscribers.set(e,new Set([t])),()=>{let n=this._eventSubscribers.get(e);n&&n.delete(t)}}},S=class extends k{constructor(e){super(),this.runtime=e,this.connect()}_canCancel=!1;get canCancel(){return this._canCancel}get attachments(){return super.attachments}getAttachmentAdapter(){return this.runtime.adapters?.attachments}connect(){return this.runtime.subscribe(()=>{this.canCancel!==this.runtime.capabilities.cancel&&(this._canCancel=this.runtime.capabilities.cancel,this._notifySubscribers())})}async handleSend(e){this.runtime.append({...e,parentId:this.runtime.messages.at(-1)?.id??null,sourceId:null})}async handleCancel(){this.runtime.cancelRun()}},E=e=>e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n\n"),T=class extends k{constructor(e,t,{parentId:n,message:r}){super(),this.runtime=e,this.endEditCallback=t,this._parentId=n,this._sourceId=r.id,this._previousText=E(r),this.setText(this._previousText),this.setRole(r.role),this.setAttachments(r.attachments??[]),this._nonTextParts=r.content.filter(e=>"text"!==e.type),this.setRunConfig({...e.composer.runConfig})}get canCancel(){return!0}getAttachmentAdapter(){return this.runtime.adapters?.attachments}_nonTextParts;_previousText;_parentId;_sourceId;async handleSend(e){E(e)!==this._previousText&&this.runtime.append({...e,content:[...e.content,...this._nonTextParts],parentId:this._parentId,sourceId:this._sourceId}),this.handleCancel()}handleCancel(){this.endEditCallback(),this._notifySubscribers()}},_=class{constructor(e){this._contextProvider=e}_subscriptions=new Set;_isInitialized=!1;repository=new x;get messages(){return this.repository.getMessages()}get state(){let e;for(let t of this.messages)if("assistant"===t.role){e=t;break}return e?.metadata.unstable_state??null}composer=new S(this);getModelContext(){return this._contextProvider.getModelContext()}_editComposers=new Map;getEditComposer(e){return this._editComposers.get(e)}beginEdit(e){if(this._editComposers.has(e))throw Error("Edit already in progress");this._editComposers.set(e,new T(this,()=>this._editComposers.delete(e),this.repository.getMessage(e))),this._notifySubscribers()}getMessageById(e){return this.repository.getMessage(e)}getBranches(e){return this.repository.getBranches(e)}switchToBranch(e){this.repository.switchToBranch(e),this._notifySubscribers()}_notifySubscribers(){for(let e of this._subscriptions)e()}_notifyEventSubscribers(e){let t=this._eventSubscribers.get(e);if(t)for(let e of t)e()}subscribe(e){return this._subscriptions.add(e),()=>this._subscriptions.delete(e)}_submittedFeedback={};getSubmittedFeedback(e){return this._submittedFeedback[e]}submitFeedback({messageId:e,type:t}){let n=this.adapters?.feedback;if(!n)throw Error("Feedback adapter not configured");let{message:r}=this.repository.getMessage(e);n.submit({message:r,type:t}),this._submittedFeedback[e]={type:t},this._notifySubscribers()}_stopSpeaking;speech;speak(e){let t=this.adapters?.speech;if(!t)throw Error("Speech adapter not configured");let{message:n}=this.repository.getMessage(e);this._stopSpeaking?.();let r=t.speak(E(n)),i=r.subscribe(()=>{"ended"===r.status.type?(this._stopSpeaking=void 0,this.speech=void 0):this.speech={messageId:e,status:r.status},this._notifySubscribers()});this.speech={messageId:e,status:r.status},this._notifySubscribers(),this._stopSpeaking=()=>{r.cancel(),i(),this.speech=void 0,this._stopSpeaking=void 0}}stopSpeaking(){if(!this._stopSpeaking)throw Error("No message is being spoken");this._stopSpeaking(),this._notifySubscribers()}ensureInitialized(){this._isInitialized||(this._isInitialized=!0,this._notifyEventSubscribers("initialize"))}export(){return this.repository.export()}import(e){this.ensureInitialized(),this.repository.clear(),this.repository.import(e),this._notifySubscribers()}reset(e){this.import(y.fromArray(e??[]))}_eventSubscribers=new Map;unstable_on(e,t){if("model-context-update"===e)return this._contextProvider.subscribe?.(t)??(()=>{});let n=this._eventSubscribers.get(e);return n?n.add(t):this._eventSubscribers.set(e,new Set([t])),()=>{this._eventSubscribers.get(e).delete(t)}}},C=class extends Error{name="AbortError";detach;constructor(e,t){super(t),this.detach=e}},A=class extends _{capabilities={switchToBranch:!0,edit:!0,reload:!0,cancel:!0,unstable_copy:!0,speech:!1,attachments:!1,feedback:!1};abortController=null;isDisabled=!1;_isLoading=!1;get isLoading(){return this._isLoading}_suggestions=[];_suggestionsController=null;get suggestions(){return this._suggestions}get adapters(){return this._options.adapters}constructor(e,t){super(e),this.__internal_setOptions(t)}_options;_lastRunConfig={};get extras(){}__internal_setOptions(e){if(this._options===e)return;this._options=e;let t=!1,n=e.adapters?.speech!==void 0;this.capabilities.speech!==n&&(this.capabilities.speech=n,t=!0);let r=e.adapters?.attachments!==void 0;this.capabilities.attachments!==r&&(this.capabilities.attachments=r,t=!0);let i=e.adapters?.feedback!==void 0;this.capabilities.feedback!==i&&(this.capabilities.feedback=i,t=!0),t&&this._notifySubscribers()}_loadPromise;__internal_load(){if(this._loadPromise)return this._loadPromise;let e=this.adapters.history?.load()??Promise.resolve(null);return this._isLoading=!0,this._notifySubscribers(),this._loadPromise=e.then(e=>{if(!e)return;this.repository.import(e),this._notifySubscribers();let t=this.adapters.history?.resume?.bind(this.adapters.history);e.unstable_resume&&t&&this.startRun({parentId:this.repository.headId,sourceId:this.repository.headId,runConfig:this._lastRunConfig},t)}).finally(()=>{this._isLoading=!1,this._notifySubscribers()}),this._loadPromise}async append(e){this.ensureInitialized();let t=f(e,h(),{type:"complete",reason:"unknown"});this.repository.addOrUpdateMessage(e.parentId,t),this._options.adapters.history?.append({parentId:e.parentId,message:t}),e.startRun??"user"===e.role?await this.startRun({parentId:t.id,sourceId:e.sourceId,runConfig:e.runConfig??{}}):(this.repository.resetHead(t.id),this._notifySubscribers())}resumeRun({stream:e,...t}){return this.startRun(t,e)}async startRun({parentId:e,runConfig:t},n){this.ensureInitialized(),this.repository.resetHead(e);let r={id:h(),role:"assistant",status:{type:"running"},content:[],metadata:{unstable_state:this.state,unstable_annotations:[],unstable_data:[],steps:[],custom:{}},createdAt:new Date};this._notifyEventSubscribers("run-start");try{this._suggestions=[],this._suggestionsController?.abort(),this._suggestionsController=null;do r=await this.performRoundtrip(e,r,t,n),n=void 0;while(p(r,this._options.unstable_humanToolNames))}finally{this._notifyEventSubscribers("run-end")}this._suggestionsController=new AbortController;let i=this._suggestionsController.signal;if(this.adapters.suggestion&&r.status?.type!=="requires-action"){let e=this.adapters.suggestion?.generate({messages:this.messages});if(Symbol.asyncIterator in e)for await(let t of e){if(i.aborted)break;this._suggestions=t}else{let t=await e;if(i.aborted)return;this._suggestions=t}}}async performRoundtrip(e,t,n,r){let i=this.repository.getMessages();this.abortController?.abort(),this.abortController=new AbortController;let o=t.content,s=t.metadata?.unstable_annotations,a=t.metadata?.unstable_data,l=t.metadata?.steps,u=t.metadata?.custom,c=n=>{let r=n.metadata?.steps,i=r?[...l??[],...r]:void 0,c=n.metadata?.unstable_annotations,h=n.metadata?.unstable_data,d=c?[...s??[],...c]:void 0,f=h?[...a??[],...h]:void 0;t={...t,...n.content?{content:[...o,...n.content??[]]}:void 0,status:n.status??t.status,...n.metadata?{metadata:{...t.metadata,...n.metadata.unstable_state?{unstable_state:n.metadata.unstable_state}:void 0,...d?{unstable_annotations:d}:void 0,...f?{unstable_data:f}:void 0,...i?{steps:i}:void 0,...n.metadata?.custom?{custom:{...u??{},...n.metadata.custom}}:void 0}}:void 0},this.repository.addOrUpdateMessage(e,t),this._notifySubscribers()},h=this._options.maxSteps??2;if((t.metadata?.steps?.length??0)>=h)return c({status:{type:"incomplete",reason:"tool-calls"}}),t;c({status:{type:"running"}});try{this._lastRunConfig=n??{};let e=this.getModelContext();r=r??this.adapters.chatModel.run.bind(this.adapters.chatModel);let o=this.abortController.signal,s=r({messages:i,runConfig:this._lastRunConfig,abortSignal:o,context:e,config:e,unstable_assistantMessageId:t.id,unstable_getMessage:()=>t});if(Symbol.asyncIterator in s)for await(let e of s){if(o.aborted){c({status:{type:"incomplete",reason:"cancelled"}});break}c(e)}else c(await s);"running"===t.status.type&&c({status:{type:"complete",reason:"unknown"}})}catch(e){if(e instanceof C)c({status:{type:"incomplete",reason:"cancelled"}});else throw c({status:{type:"incomplete",reason:"error",error:e instanceof Error?e.message:`[${typeof e}] ${new String(e).toString()}`}}),e}finally{this.abortController=null,("complete"===t.status.type||"incomplete"===t.status.type)&&await this._options.adapters.history?.append({parentId:e,message:t})}return t}detach(){let e=new C(!0);this.abortController?.abort(e),this.abortController=null}cancelRun(){let e=new C(!1);this.abortController?.abort(e),this.abortController=null}addToolResult({messageId:e,toolCallId:t,result:n,isError:r,artifact:i}){let o=this.repository.getMessage(e),{parentId:s}=o,{message:a}=o;if("assistant"!==a.role)throw Error("Tried to add tool result to non-assistant message");let l=!1,u=!1,c=a.content.map(e=>"tool-call"!==e.type||e.toolCallId!==t?e:(u=!0,e.result||(l=!0),{...e,result:n,artifact:i,isError:r}));if(!u)throw Error("Tried to add tool result to non-existing tool call");a={...a,content:c},this.repository.addOrUpdateMessage(s,a),l&&p(a,this._options.unstable_humanToolNames)&&this.performRoundtrip(s,a,this._lastRunConfig)}},I=Object.freeze([]),P=class extends w{_mainThread;constructor(e){super(),this._mainThread=e()}get isLoading(){return!1}getMainThreadRuntimeCore(){return this._mainThread}get newThreadId(){throw Error("Method not implemented.")}get threadIds(){throw I}get archivedThreadIds(){throw I}get mainThreadId(){return"__DEFAULT_ID__"}getThreadRuntimeCore(){throw Error("Method not implemented.")}getLoadThreadsPromise(){throw Error("Method not implemented.")}getItemById(e){if(e===this.mainThreadId)return{status:"regular",threadId:this.mainThreadId,remoteId:this.mainThreadId,externalId:void 0,title:void 0,isMain:!0};throw Error("Method not implemented")}async switchToThread(){throw Error("Method not implemented.")}switchToNewThread(){throw Error("Method not implemented.")}rename(){throw Error("Method not implemented.")}archive(){throw Error("Method not implemented.")}detach(){throw Error("Method not implemented.")}unarchive(){throw Error("Method not implemented.")}delete(){throw Error("Method not implemented.")}initialize(){throw Error("Method not implemented.")}generateTitle(){throw Error("Method not implemented.")}},R=class extends s{threads;Provider=void 0;_options;constructor(e,t){super(),this._options=e,this.threads=new P(()=>new A(this._contextProvider,this._options)),t&&this.threads.getMainThreadRuntimeCore().import(y.fromArray(t))}},M=n(5155),O=(0,i.createContext)(null),D=e=>{let{adapters:t,children:n}=e,r=(0,i.useContext)(O);return(0,M.jsx)(O.Provider,{value:{...r,...t},children:n})},L=n(5453),j=n(7119),B=n(3357),N=n(1386),F=class extends w{startThreadRuntime(e){return this.instances.has(e)||(this.instances.set(e,{}),this.useAliveThreadsKeysChanged.setState({},!0)),new Promise((t,n)=>{let r=()=>{let r=this.instances.get(e);if(r){if(!r.runtime)return;i(),t(r.runtime)}else i(),n(Error("Thread was deleted before runtime was started"))},i=this.subscribe(r);r()})}getThreadRuntimeCore(e){let t=this.instances.get(e);if(t)return t.runtime}stopThreadRuntime(e){this.instances.delete(e),this.useAliveThreadsKeysChanged.setState({},!0)}setRuntimeHook(e){this.useRuntimeHook.getState().useRuntime!==e&&this.useRuntimeHook.setState({useRuntime:e},!0)}constructor(e){super(),this.instances=new Map,this.useAliveThreadsKeysChanged=(0,L.v)(()=>({})),this._InnerActiveThreadProvider=()=>{let{id:e}=(0,N.Xi)(),{useRuntime:t}=this.useRuntimeHook(),n=t(),r=n.thread.__internal_threadBinding,o=(0,i.useCallback)(()=>{let t=this.instances.get(e);if(!t)throw Error("Thread not found. This is a bug in assistant-ui.");t.runtime=r.getState(),s&&this._notifySubscribers()},[e,r]),s=(0,i.useRef)(!1);s.current||o(),(0,i.useEffect)(()=>(s.current=!0,o(),r.outerSubscribe(o)),[r,o]);let a=(0,N.pK)();return(0,i.useEffect)(()=>n.threads.main.unstable_on("initialize",()=>{if("new"===a.getState().status){a.initialize();let e=n.thread.unstable_on("run-end",()=>{e(),a.generateTitle()})}}),[n,a]),null},this._OuterActiveThreadProvider=(0,i.memo)(e=>{let{threadId:t,provider:n}=e,r=(0,j.Ij)(),o=(0,i.useMemo)(()=>r.threads.getItemById(t),[r,t]);return(0,M.jsx)(B.X,{runtime:o,children:(0,M.jsx)(n,{children:(0,M.jsx)(this._InnerActiveThreadProvider,{})})})}),this.__internal_RenderThreadRuntimes=e=>{let{provider:t}=e;return this.useAliveThreadsKeysChanged(),Array.from(this.instances.keys()).map(e=>(0,M.jsx)(this._OuterActiveThreadProvider,{threadId:e,provider:t},e))},this.useRuntimeHook=(0,L.v)(()=>({useRuntime:e}))}},z=Error("This is the empty thread, a placeholder for the main thread. You cannot perform any actions on this thread instance. This error is probably because you tried to call a thread method in your render function. Call the method inside a `useEffect` hook instead."),U={getMessageById(){},getBranches:()=>[],switchToBranch(){throw z},append(){throw z},startRun(){throw z},resumeRun(){throw z},cancelRun(){throw z},addToolResult(){throw z},speak(){throw z},stopSpeaking(){throw z},getSubmittedFeedback(){},submitFeedback(){throw z},getModelContext:()=>({}),composer:{attachments:[],getAttachmentAccept:()=>"*",async addAttachment(){throw z},async removeAttachment(){throw z},isEditing:!1,canCancel:!1,isEmpty:!0,text:"",setText(){throw z},role:"user",setRole(){throw z},runConfig:{},setRunConfig(){throw z},async reset(){},async clearAttachments(){},send(){throw z},cancel(){},subscribe:()=>()=>{},unstable_on:()=>()=>{}},getEditComposer(){},beginEdit(){throw z},speech:void 0,capabilities:{switchToBranch:!1,edit:!1,reload:!1,cancel:!1,unstable_copy:!1,speech:!1,attachments:!1,feedback:!1},isDisabled:!0,isLoading:!1,messages:[],state:null,suggestions:[],extras:void 0,subscribe:()=>()=>{},import(){throw z},export:()=>({messages:[]}),reset(){throw z},unstable_on:()=>()=>{}},V=(e,t,n)=>n.reduce((e,n)=>n?.(e,t)??e,e),H=class extends w{_pendingTransforms=[];_baseValue;_cachedValue;constructor(e){super(),this._baseValue=e,this._cachedValue=e}_updateState(){this._cachedValue=this._pendingTransforms.reduce((e,t)=>V(e,t.task,[t.loading,t.optimistic]),this._baseValue),this._notifySubscribers()}get baseValue(){return this._baseValue}get value(){return this._cachedValue}update(e){this._baseValue=e,this._updateState()}async optimisticUpdate(e){let t=e.execute(),n={...e,task:t};try{this._pendingTransforms.push(n),this._updateState();let r=await t;return this._baseValue=V(this._baseValue,r,[e.optimistic,e.then]),r}finally{let e=this._pendingTransforms.indexOf(n);e>-1&&this._pendingTransforms.splice(e,1),this._updateState()}}},W=c("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",7),$=class e{_state;constructor(e=null){this._state=e}get state(){return this._state}append(t){this._state=t.reduce((t,n)=>e.apply(t,n),this._state)}static apply(t,n){let r=n.type;switch(r){case"set":return e.updatePath(t,n.path,()=>n.value);case"append-text":return e.updatePath(t,n.path,e=>{if("string"!=typeof e)throw Error(`Expected string at path [${n.path.join(", ")}]`);return e+n.value});default:throw Error(`Invalid operation type: ${r}`)}}static updatePath(e,t,n){if(0===t.length)return n(e);if("object"!=typeof(e??={}))throw Error(`Invalid path: [${t.join(", ")}]`);let[r,...i]=t;if(Array.isArray(e)){let o=Number(r);if(isNaN(o))throw Error(`Expected array index at [${t.join(", ")}]`);if(o>e.length||o<0)throw Error("Insert array index out of bounds");let s=[...e];return s[o]=this.updatePath(s[o],i,n),s}let o={...e};return o[r]=this.updatePath(o[r],i,n),o}},q=(e,t,n)=>{if(0===e.parts.length)throw Error("No parts available to update.");if(1!==t.path.length)throw Error("Nested paths are not supported yet.");let r=t.path[0],i=n(e.parts[r]);return{...e,parts:[...e.parts.slice(0,r),i,...e.parts.slice(r+1)],get content(){return this.parts}}},K=(e,t)=>{if(e.status?.type==="incomplete"&&e.status?.reason==="error")return e;let n=Y(t);return{...e,status:n}},Y=e=>"tool-calls"===e.finishReason?{type:"requires-action",reason:"tool-calls"}:"stop"===e.finishReason||"unknown"===e.finishReason?{type:"complete",reason:e.finishReason}:{type:"incomplete",reason:e.finishReason},X=class extends TransformStream{constructor({initialMessage:e}={}){let t=e??{role:"assistant",status:{type:"running"},parts:[],get content(){return this.parts},metadata:{unstable_state:null,unstable_data:[],unstable_annotations:[],steps:[],custom:{}}};super({transform(e,n){let r=e.type;switch(r){case"part-start":t=((e,t)=>{let n=t.part;if("text"===n.type||"reasoning"===n.type){let t={type:n.type,text:"",status:{type:"running"},...n.parentId&&{parentId:n.parentId}};return{...e,parts:[...e.parts,t],get content(){return this.parts}}}if("tool-call"===n.type){let t={type:"tool-call",state:"partial-call",status:{type:"running",isArgsComplete:!1},toolCallId:n.toolCallId,toolName:n.toolName,argsText:"",args:{},...n.parentId&&{parentId:n.parentId}};return{...e,parts:[...e.parts,t],get content(){return this.parts}}}if("source"===n.type){let t={type:"source",sourceType:n.sourceType,id:n.id,url:n.url,...n.title?{title:n.title}:void 0,...n.parentId&&{parentId:n.parentId}};return{...e,parts:[...e.parts,t],get content(){return this.parts}}}if("file"===n.type){let t={type:"file",mimeType:n.mimeType,data:n.data};return{...e,parts:[...e.parts,t],get content(){return this.parts}}}throw Error(`Unsupported part type: ${n.type}`)})(t,e);break;case"tool-call-args-text-finish":t=q(t,e,e=>{if("tool-call"!==e.type)throw Error("Last is not a tool call");return"partial-call"!==e.state?e:{...e,state:"call"}});break;case"part-finish":t=q(t,e,e=>({...e,status:{type:"complete",reason:"unknown"}}));break;case"text-delta":t=q(t,e,t=>{if("text"===t.type||"reasoning"===t.type)return{...t,text:t.text+e.textDelta};if("tool-call"===t.type){let n=t.argsText+e.textDelta,r=u(n)??t.args;return{...t,argsText:n,args:r}}throw Error("text-delta received but part is neither text nor tool-call")});break;case"result":t=q(t,e,t=>{if("tool-call"===t.type)return{...t,state:"result",...void 0!==e.artifact?{artifact:e.artifact}:{},result:e.result,isError:e.isError??!1,status:{type:"complete",reason:"stop"}};throw Error("Result chunk received but part is not a tool-call")});break;case"message-finish":t=K(t,e);break;case"annotations":let i;t={...i=t,metadata:{...i.metadata,unstable_annotations:[...i.metadata.unstable_annotations,...e.annotations]}};break;case"data":let o;t={...o=t,metadata:{...o.metadata,unstable_data:[...o.metadata.unstable_data,...e.data]}};break;case"step-start":let s;t={...s=t,metadata:{...s.metadata,steps:[...s.metadata.steps,{state:"started",messageId:e.messageId}]}};break;case"step-finish":t=((e,t)=>{let n=e.metadata.steps.slice(),r=n.length-1;return n.length>0&&n[r]?.state==="started"?n[r]={...n[r],state:"finished",finishReason:t.finishReason,usage:t.usage,isContinued:t.isContinued}:n.push({state:"finished",messageId:W(),finishReason:t.finishReason,usage:t.usage,isContinued:t.isContinued}),{...e,metadata:{...e.metadata,steps:n}}})(t,e);break;case"error":t={...t,status:{type:"incomplete",reason:"error",error:e.error}};break;case"update-state":t=((e,t)=>{let n=new $(e.metadata.unstable_state);return n.append(t.operations),{...e,metadata:{...e.metadata,unstable_state:n.state}}})(t,e);break;default:throw Error(`Unsupported chunk type: ${r}`)}n.enqueue(t)},flush(e){if(t.status?.type==="running"){let n=t.parts?.at(-1)?.type==="tool-call";t=K(t,{type:"message-finish",path:[],finishReason:n?"tool-calls":"unknown",usage:{promptTokens:0,completionTokens:0}}),e.enqueue(t)}}})}},G=class e{constructor(e){this.readable=e,this.readable=e}static fromAssistantStream(t){return new e(t.pipeThrough(new X))}async unstable_result(){let e;for await(let t of this)e=t;return e||{role:"assistant",status:{type:"complete",reason:"unknown"},parts:[],content:[],metadata:{unstable_state:null,unstable_data:[],unstable_annotations:[],steps:[],custom:{}}}}[Symbol.asyncIterator](){let e=this.readable.getReader();return{async next(){let{done:t,value:n}=await e.read();return t?{done:!0,value:void 0}:{done:!1,value:n}}}}tee(){let[t,n]=this.readable.tee();return[new e(t),new e(n)]}},J=(e,t)=>{let n=e.threadIdMap[t];if(void 0!==n)return e.threadData[n]},Z=(e,t,n)=>{let r=J(e,t);if(!r)return e;let{threadId:i,remoteId:o,status:s}=r;if(s===n)return e;let a={...e};switch(s){case"new":a.newThreadId=void 0;break;case"regular":a.threadIds=a.threadIds.filter(e=>e!==i);break;case"archived":a.archivedThreadIds=a.archivedThreadIds.filter(e=>e!==i);break;default:throw Error("Unsupported state: ".concat(s))}switch(n){case"regular":a.threadIds=[i,...a.threadIds];break;case"archived":a.archivedThreadIds=[i,...a.archivedThreadIds];break;case"deleted":a.threadData=Object.fromEntries(Object.entries(a.threadData).filter(e=>{let[t]=e;return t!==i})),a.threadIdMap=Object.fromEntries(Object.entries(a.threadIdMap).filter(e=>{let[t]=e;return t!==i&&t!==o}));break;default:throw Error("Unsupported state: ".concat(n))}return"deleted"!==n&&(a.threadData={...a.threadData,[i]:{...r,status:n}}),a},Q=class extends w{getLoadThreadsPromise(){return this._loadThreadsPromise||(this._loadThreadsPromise=this._state.optimisticUpdate({execute:()=>this._options.adapter.list(),loading:e=>({...e,isLoading:!0}),then:(e,t)=>{let n=[],r=[],i={},o={};for(let e of t.threads){switch(e.status){case"regular":n.push(e.remoteId);break;case"archived":r.push(e.remoteId);break;default:{let t=e.status;throw Error("Unsupported state: ".concat(t))}}let t=e.remoteId;i[e.remoteId]=t,o[t]={threadId:e.remoteId,remoteId:e.remoteId,externalId:e.externalId,status:e.status,title:e.title,initializeTask:Promise.resolve({remoteId:e.remoteId,externalId:e.externalId})}}return{...e,threadIds:n,archivedThreadIds:r,threadIdMap:{...e.threadIdMap,...i},threadData:{...e.threadData,...o}}}}).then(()=>{})),this._loadThreadsPromise}__internal_setOptions(e){var t;if(this._options===e)return;this._options=e;let n=null!=(t=e.adapter.unstable_Provider)?t:i.Fragment;n!==this.useProvider.getState().Provider&&this.useProvider.setState({Provider:n},!0),this._hookManager.setRuntimeHook(e.runtimeHook)}__internal_load(){this.getLoadThreadsPromise()}get isLoading(){return this._state.value.isLoading}get threadIds(){return this._state.value.threadIds}get archivedThreadIds(){return this._state.value.archivedThreadIds}get newThreadId(){return this._state.value.newThreadId}get mainThreadId(){return this._mainThreadId}getMainThreadRuntimeCore(){let e=this._hookManager.getThreadRuntimeCore(this._mainThreadId);return e||U}getThreadRuntimeCore(e){let t=this.getItemById(e);if(!t)throw Error("Thread not found");let n=this._hookManager.getThreadRuntimeCore(t.threadId);if(!n)throw Error("Thread not found");return n}getItemById(e){return J(this._state.value,e)}async switchToThread(e){let t=this.getItemById(e);if(!t)throw Error("Thread not found");if(this._mainThreadId===t.threadId)return;let n=this._hookManager.startThreadRuntime(t.threadId);void 0!==this.mainThreadId?await n:n.then(()=>this._notifySubscribers()),"archived"===t.status&&await this.unarchive(t.threadId),this._mainThreadId=t.threadId,this._notifySubscribers()}async switchToNewThread(){for(;void 0!==this._state.baseValue.newThreadId&&void 0===this._state.value.newThreadId;)await this._state.waitForUpdate();let e=this._state.value,t=this._state.value.newThreadId;if(void 0===t){do t="__LOCALID_".concat(h());while(e.threadIdMap[t]);let n=t;this._state.update({...e,newThreadId:t,threadIdMap:{...e.threadIdMap,[t]:n},threadData:{...e.threadData,[t]:{status:"new",threadId:t}}})}return this.switchToThread(t)}rename(e,t){let n=this.getItemById(e);if(!n)throw Error("Thread not found");if("new"===n.status)throw Error("Thread is not yet initialized");return this._state.optimisticUpdate({execute:async()=>{let{remoteId:e}=await n.initializeTask;return this._options.adapter.rename(e,t)},optimistic:n=>{let r=J(n,e);return r?{...n,threadData:{...n.threadData,[r.threadId]:{...r,title:t}}}:n}})}async _ensureThreadIsNotMain(e){if(e===this.newThreadId)throw Error("Cannot ensure new thread is not main");e===this._mainThreadId&&await this.switchToNewThread()}async archive(e){let t=this.getItemById(e);if(!t)throw Error("Thread not found");if("regular"!==t.status)throw Error("Thread is not yet initialized or already archived");return this._state.optimisticUpdate({execute:async()=>{await this._ensureThreadIsNotMain(t.threadId);let{remoteId:e}=await t.initializeTask;return this._options.adapter.archive(e)},optimistic:e=>Z(e,t.threadId,"archived")})}unarchive(e){let t=this.getItemById(e);if(!t)throw Error("Thread not found");if("archived"!==t.status)throw Error("Thread is not archived");return this._state.optimisticUpdate({execute:async()=>{try{let{remoteId:e}=await t.initializeTask;return await this._options.adapter.unarchive(e)}catch(e){throw await this._ensureThreadIsNotMain(t.threadId),e}},optimistic:e=>Z(e,t.threadId,"regular")})}async delete(e){let t=this.getItemById(e);if(!t)throw Error("Thread not found");if("regular"!==t.status&&"archived"!==t.status)throw Error("Thread is not yet initialized");return this._state.optimisticUpdate({execute:async()=>{await this._ensureThreadIsNotMain(t.threadId);let{remoteId:e}=await t.initializeTask;return await this._options.adapter.delete(e)},optimistic:e=>Z(e,t.threadId,"deleted")})}async detach(e){let t=this.getItemById(e);if(!t)throw Error("Thread not found");if("regular"!==t.status&&"archived"!==t.status)throw Error("Thread is not yet initialized");await this._ensureThreadIsNotMain(t.threadId),this._hookManager.stopThreadRuntime(t.threadId)}constructor(e,t){super(),this._state=new H({isLoading:!1,newThreadId:void 0,threadIds:[],archivedThreadIds:[],threadIdMap:{},threadData:{}}),this.initialize=async e=>{if(this._state.value.newThreadId!==e){let t=this.getItemById(e);if(!t)throw Error("Thread not found");if("new"===t.status)throw Error("Unexpected new state");return t.initializeTask}return this._state.optimisticUpdate({execute:()=>this._options.adapter.initialize(e),optimistic:t=>Z(t,e,"regular"),loading:(t,n)=>({...t,threadData:{...t.threadData,[e]:{...t.threadData[e],initializeTask:n}}}),then:(t,n)=>{let{remoteId:r,externalId:i}=n,o=J(t,e);return o?{...t,threadIdMap:{...t.threadIdMap,[r]:e},threadData:{...t.threadData,[e]:{...o,initializeTask:Promise.resolve({remoteId:r,externalId:i}),remoteId:r,externalId:i}}}:t}})},this.generateTitle=async e=>{let t=this.getItemById(e);if(!t)throw Error("Thread not found");if("new"===t.status)throw Error("Thread is not yet initialized");let{remoteId:n}=await t.initializeTask,r=this._hookManager.getThreadRuntimeCore(t.threadId);if(!r)return;let i=r.messages,o=await this._options.adapter.generateTitle(n,i);for await(let e of G.fromAssistantStream(o)){var s;let n=null==(s=e.parts.filter(e=>"text"===e.type)[0])?void 0:s.text,r=this._state.baseValue;this._state.update({...r,threadData:{...r.threadData,[t.threadId]:{...t,title:n}}})}},this.useBoundIds=(0,L.v)(()=>[]),this.__internal_RenderComponent=()=>{let e=(0,i.useId)();(0,i.useEffect)(()=>(this.useBoundIds.setState(t=>[...t,e],!0),()=>{this.useBoundIds.setState(t=>t.filter(t=>t!==e),!0)}),[e]);let t=this.useBoundIds(),{Provider:n}=this.useProvider(),r={modelContext:this.contextProvider};return(0===t.length||t[0]===e)&&(0,M.jsx)(D,{adapters:r,children:(0,M.jsx)(this._hookManager.__internal_RenderThreadRuntimes,{provider:n})})},this.contextProvider=t,this._state.subscribe(()=>this._notifySubscribers()),this._hookManager=new F(e.runtimeHook),this.useProvider=(0,L.v)(()=>{var t;return{Provider:null!=(t=e.adapter.unstable_Provider)?t:i.Fragment}}),this.__internal_setOptions(e),this.switchToNewThread()}},ee=class{_subscriptions=new Set;_connection;get isConnected(){return!!this._connection}notifySubscribers(){for(let e of this._subscriptions)e()}_updateConnection(){this._subscriptions.size>0?this._connection||(this._connection=this._connect()):(this._connection?.(),this._connection=void 0)}subscribe(e){return this._subscriptions.add(e),this._updateConnection(),()=>{this._subscriptions.delete(e),this._updateConnection()}}},et=Symbol("skip-update"),en=class extends ee{constructor(e){super(),this.binding=e}get path(){return this.binding.path}_previousStateDirty=!0;_previousState;getState=()=>{if(!this.isConnected||this._previousStateDirty){let e=this.binding.getState();e!==et&&(this._previousState=e),this._previousStateDirty=!1}if(void 0===this._previousState)throw Error("Entry not available in the store");return this._previousState};_connect(){let e=()=>{this._previousStateDirty=!0,this.notifySubscribers()};return this.binding.subscribe(e)}},er=class{constructor(e,t){this._core=e,this._threadListBinding=t}get path(){return this._core.path}__internal_bindMethods(){this.switchTo=this.switchTo.bind(this),this.rename=this.rename.bind(this),this.archive=this.archive.bind(this),this.unarchive=this.unarchive.bind(this),this.delete=this.delete.bind(this),this.initialize=this.initialize.bind(this),this.generateTitle=this.generateTitle.bind(this),this.subscribe=this.subscribe.bind(this),this.unstable_on=this.unstable_on.bind(this),this.getState=this.getState.bind(this),this.detach=this.detach.bind(this)}getState(){return this._core.getState()}switchTo(){let e=this._core.getState();return this._threadListBinding.switchToThread(e.id)}rename(e){let t=this._core.getState();return this._threadListBinding.rename(t.id,e)}archive(){let e=this._core.getState();return this._threadListBinding.archive(e.id)}unarchive(){let e=this._core.getState();return this._threadListBinding.unarchive(e.id)}delete(){let e=this._core.getState();return this._threadListBinding.delete(e.id)}initialize(){let e=this._core.getState();return this._threadListBinding.initialize(e.id)}generateTitle(){let e=this._core.getState();return this._threadListBinding.generateTitle(e.id)}unstable_on(e,t){let n=this._core.getState().isMain;return this.subscribe(()=>{let r=this._core.getState().isMain;if(n!==r)n=r,("switched-to"!==e||r)&&("switched-away"===e&&r||t())})}subscribe(e){return this._core.subscribe(e)}detach(){let e=this._core.getState();this._threadListBinding.detach(e.id)}},ei=class extends ee{constructor(e){super(),this.binding=e;let t=e.getState();if(t===et)throw Error("Entry not available in the store");this._previousState=t}get path(){return this.binding.path}_previousState;getState=()=>(this.isConnected||this._syncState(),this._previousState);_syncState(){let e=this.binding.getState();return!(e===et||function(e,t){if(void 0===e&&void 0===t)return!0;if(void 0===e||void 0===t)return!1;for(let n of Object.keys(e))if(!Object.is(e[n],t[n]))return!1;return!0}(e,this._previousState))&&(this._previousState=e,!0)}_connect(){let e=()=>{this._syncState()&&this.notifySubscribers()};return this.binding.subscribe(e)}},eo=Symbol("innerMessage");Symbol("innerMessages");var es=class{constructor(e){this._core=e}get path(){return this._core.path}__internal_bindMethods(){this.getState=this.getState.bind(this),this.remove=this.remove.bind(this),this.subscribe=this.subscribe.bind(this)}getState(){return this._core.getState()}subscribe(e){return this._core.subscribe(e)}},ea=class extends es{constructor(e,t){super(e),this._composerApi=t}remove(){let e=this._composerApi.getState();if(!e)throw Error("Composer is not available");return e.removeAttachment(this.getState().id)}},el=class extends ea{get source(){return"thread-composer"}},eu=class extends ea{get source(){return"edit-composer"}},ec=class extends es{get source(){return"message"}constructor(e){super(e)}remove(){throw Error("Message attachments cannot be removed")}},eh=class extends ee{constructor(e){super(),this.config=e}getState(){return this.config.binding.getState()}outerSubscribe(e){return this.config.binding.subscribe(e)}_connect(){let e=()=>{this.notifySubscribers()},t=this.config.binding.getState(),n=t?.unstable_on(this.config.event,e),r=()=>{let r=this.config.binding.getState();r!==t&&(t=r,n?.(),n=this.config.binding.getState()?.unstable_on(this.config.event,e))},i=this.outerSubscribe(r);return()=>{i?.(),n?.()}}},ed=Object.freeze([]),ef=Object.freeze({}),ep=class{constructor(e){this._core=e}get path(){return this._core.path}__internal_bindMethods(){this.setText=this.setText.bind(this),this.setRunConfig=this.setRunConfig.bind(this),this.getState=this.getState.bind(this),this.subscribe=this.subscribe.bind(this),this.addAttachment=this.addAttachment.bind(this),this.reset=this.reset.bind(this),this.clearAttachments=this.clearAttachments.bind(this),this.send=this.send.bind(this),this.cancel=this.cancel.bind(this),this.setRole=this.setRole.bind(this),this.getAttachmentAccept=this.getAttachmentAccept.bind(this),this.getAttachmentByIndex=this.getAttachmentByIndex.bind(this),this.unstable_on=this.unstable_on.bind(this)}setText(e){let t=this._core.getState();if(!t)throw Error("Composer is not available");t.setText(e)}setRunConfig(e){let t=this._core.getState();if(!t)throw Error("Composer is not available");t.setRunConfig(e)}addAttachment(e){let t=this._core.getState();if(!t)throw Error("Composer is not available");return t.addAttachment(e)}reset(){let e=this._core.getState();if(!e)throw Error("Composer is not available");return e.reset()}clearAttachments(){let e=this._core.getState();if(!e)throw Error("Composer is not available");return e.clearAttachments()}send(){let e=this._core.getState();if(!e)throw Error("Composer is not available");e.send()}cancel(){let e=this._core.getState();if(!e)throw Error("Composer is not available");e.cancel()}setRole(e){let t=this._core.getState();if(!t)throw Error("Composer is not available");t.setRole(e)}subscribe(e){return this._core.subscribe(e)}_eventSubscriptionSubjects=new Map;unstable_on(e,t){let n=this._eventSubscriptionSubjects.get(e);return n||(n=new eh({event:e,binding:this._core}),this._eventSubscriptionSubjects.set(e,n)),n.subscribe(t)}getAttachmentAccept(){let e=this._core.getState();if(!e)throw Error("Composer is not available");return e.getAttachmentAccept()}},em=class extends ep{get path(){return this._core.path}get type(){return"thread"}_getState;constructor(e){let t=new en({path:e.path,getState:()=>(e=>Object.freeze({type:"thread",isEditing:e?.isEditing??!1,canCancel:e?.canCancel??!1,isEmpty:e?.isEmpty??!0,attachments:e?.attachments??ed,text:e?.text??"",role:e?.role??"user",runConfig:e?.runConfig??ef,value:e?.text??""}))(e.getState()),subscribe:t=>e.subscribe(t)});super({path:e.path,getState:()=>e.getState(),subscribe:e=>t.subscribe(e)}),this._getState=t.getState.bind(t)}getState(){return this._getState()}getAttachmentByIndex(e){return new el(new ei({path:{...this.path,attachmentSource:"thread-composer",attachmentSelector:{type:"index",index:e},ref:this.path.ref+`${this.path.ref}.attachments[${e}]`},getState:()=>{let t=this.getState().attachments[e];return t?{...t,source:"thread-composer"}:et},subscribe:e=>this._core.subscribe(e)}),this._core)}},eg=class extends ep{constructor(e,t){let n=new en({path:e.path,getState:()=>(e=>Object.freeze({type:"edit",isEditing:e?.isEditing??!1,canCancel:e?.canCancel??!1,isEmpty:e?.isEmpty??!0,text:e?.text??"",role:e?.role??"user",attachments:e?.attachments??ed,runConfig:e?.runConfig??ef,value:e?.text??""}))(e.getState()),subscribe:t=>e.subscribe(t)});super({path:e.path,getState:()=>e.getState(),subscribe:e=>n.subscribe(e)}),this._beginEdit=t,this._getState=n.getState.bind(n)}get path(){return this._core.path}get type(){return"edit"}_getState;__internal_bindMethods(){super.__internal_bindMethods(),this.beginEdit=this.beginEdit.bind(this)}getState(){return this._getState()}beginEdit(){this._beginEdit()}getAttachmentByIndex(e){return new eu(new ei({path:{...this.path,attachmentSource:"edit-composer",attachmentSelector:{type:"index",index:e},ref:this.path.ref+`${this.path.ref}.attachments[${e}]`},getState:()=>{let t=this.getState().attachments[e];return t?{...t,source:"edit-composer"}:et},subscribe:e=>this._core.subscribe(e)}),this._core)}},ey=n(1174),ev=class extends ee{constructor(e){super(),this.binding=e}get path(){return this.binding.path}getState(){return this.binding.getState()}outerSubscribe(e){return this.binding.subscribe(e)}_connect(){let e=()=>{this.notifySubscribers()},t=this.binding.getState(),n=t?.subscribe(e),r=()=>{let r=this.binding.getState();r!==t&&(t=r,n?.(),n=this.binding.getState()?.subscribe(e),e())},i=this.outerSubscribe(r);return()=>{i?.(),n?.()}}},eb=Object.freeze({type:"complete"}),ex=(e,t)=>{let n=e.content[t];if(!n)return et;let r=((e,t,n)=>{if("assistant"!==e.role)return eb;if("tool-call"===n.type)if(!n.result)return e.status;else return eb;let r=t===Math.max(0,e.content.length-1);return"requires-action"===e.status.type?eb:r?e.status:eb})(e,t,n);return Object.freeze({...n,...{[eo]:n[eo]},status:r})},ew=class{constructor(e,t){this._core=e,this._threadBinding=t,this.composer=new eg(new ev({path:{...this.path,ref:this.path.ref+`${this.path.ref}.composer`,composerSource:"edit"},getState:this._getEditComposerRuntimeCore,subscribe:e=>this._threadBinding.subscribe(e)}),()=>this._threadBinding.getState().beginEdit(this._core.getState().id))}get path(){return this._core.path}__internal_bindMethods(){this.reload=this.reload.bind(this),this.getState=this.getState.bind(this),this.subscribe=this.subscribe.bind(this),this.getMessagePartByIndex=this.getMessagePartByIndex.bind(this),this.getMessagePartByToolCallId=this.getMessagePartByToolCallId.bind(this),this.getAttachmentByIndex=this.getAttachmentByIndex.bind(this),this.unstable_getCopyText=this.unstable_getCopyText.bind(this),this.speak=this.speak.bind(this),this.stopSpeaking=this.stopSpeaking.bind(this),this.submitFeedback=this.submitFeedback.bind(this),this.switchToBranch=this.switchToBranch.bind(this)}composer;_getEditComposerRuntimeCore=()=>this._threadBinding.getState().getEditComposer(this._core.getState().id);getState(){return this._core.getState()}reload(e={}){let t=this._getEditComposerRuntimeCore(),n=t??this._threadBinding.getState().composer,{runConfig:r=(t??n).runConfig}=e,i=this._core.getState();if("assistant"!==i.role)throw Error("Can only reload assistant messages");this._threadBinding.getState().startRun({parentId:i.parentId,sourceId:i.id,runConfig:r})}speak(){let e=this._core.getState();return this._threadBinding.getState().speak(e.id)}stopSpeaking(){let e=this._core.getState(),t=this._threadBinding.getState();if(t.speech?.messageId===e.id)this._threadBinding.getState().stopSpeaking();else throw Error("Message is not being spoken")}submitFeedback({type:e}){let t=this._core.getState();this._threadBinding.getState().submitFeedback({messageId:t.id,type:e})}switchToBranch({position:e,branchId:t}){let n=this._core.getState();if(t&&e)throw Error("May not specify both branchId and position");if(!t&&!e)throw Error("Must specify either branchId or position");let r=this._threadBinding.getState().getBranches(n.id),i=t;if("previous"===e?i=r[n.branchNumber-2]:"next"===e&&(i=r[n.branchNumber]),!i)throw Error("Branch not found");this._threadBinding.getState().switchToBranch(i)}unstable_getCopyText(){return E(this.getState())}subscribe(e){return this._core.subscribe(e)}getMessagePartByIndex(e){if(e<0)throw Error("Message part index must be >= 0");return new ey.D(new ei({path:{...this.path,ref:this.path.ref+`${this.path.ref}.content[${e}]`,messagePartSelector:{type:"index",index:e}},getState:()=>ex(this.getState(),e),subscribe:e=>this._core.subscribe(e)}),this._core,this._threadBinding)}getMessagePartByToolCallId(e){return new ey.D(new ei({path:{...this.path,ref:this.path.ref+`${this.path.ref}.content[toolCallId=${JSON.stringify(e)}]`,messagePartSelector:{type:"toolCallId",toolCallId:e}},getState:()=>{let t=this._core.getState(),n=t.content.findIndex(t=>"tool-call"===t.type&&t.toolCallId===e);return -1===n?et:ex(t,n)},subscribe:e=>this._core.subscribe(e)}),this._core,this._threadBinding)}getAttachmentByIndex(e){return new ec(new ei({path:{...this.path,ref:this.path.ref+`${this.path.ref}.attachments[${e}]`,attachmentSource:"message",attachmentSelector:{type:"index",index:e}},getState:()=>{let t=this.getState().attachments,n=t?.[e];return n?{...n,source:"message"}:et},subscribe:e=>this._core.subscribe(e)}))}},ek=class{get path(){return this._threadBinding.path}get __internal_threadBinding(){return this._threadBinding}_threadBinding;constructor(e,t){let n=new ei({path:e.path,getState:()=>((e,t)=>{let n=e.messages.at(-1);return Object.freeze({threadId:t.id,metadata:t,capabilities:e.capabilities,isDisabled:e.isDisabled,isLoading:e.isLoading,isRunning:n?.role==="assistant"&&"running"===n.status.type,messages:e.messages,state:e.state,suggestions:e.suggestions,extras:e.extras,speech:e.speech})})(e.getState(),t.getState()),subscribe:n=>{let r=e.subscribe(n),i=t.subscribe(n);return()=>{r(),i()}}});this._threadBinding={path:e.path,getState:()=>e.getState(),getStateState:()=>n.getState(),outerSubscribe:t=>e.outerSubscribe(t),subscribe:t=>e.subscribe(t)},this.composer=new em(new ev({path:{...this.path,ref:this.path.ref+`${this.path.ref}.composer`,composerSource:"thread"},getState:()=>this._threadBinding.getState().composer,subscribe:e=>this._threadBinding.subscribe(e)}))}__internal_bindMethods(){this.append=this.append.bind(this),this.unstable_resumeRun=this.unstable_resumeRun.bind(this),this.startRun=this.startRun.bind(this),this.cancelRun=this.cancelRun.bind(this),this.stopSpeaking=this.stopSpeaking.bind(this),this.export=this.export.bind(this),this.import=this.import.bind(this),this.reset=this.reset.bind(this),this.getMesssageByIndex=this.getMesssageByIndex.bind(this),this.getMesssageById=this.getMesssageById.bind(this),this.subscribe=this.subscribe.bind(this),this.unstable_on=this.unstable_on.bind(this),this.getModelContext=this.getModelContext.bind(this),this.getModelConfig=this.getModelConfig.bind(this),this.getState=this.getState.bind(this)}composer;getState(){return this._threadBinding.getStateState()}append(e){var t;this._threadBinding.getState().append((t=this._threadBinding.getState().messages,"string"==typeof e?{createdAt:new Date,parentId:t.at(-1)?.id??null,sourceId:null,runConfig:{},role:"user",content:[{type:"text",text:e}],attachments:[],metadata:{custom:{}}}:{createdAt:e.createdAt??new Date,parentId:e.parentId??t.at(-1)?.id??null,sourceId:e.sourceId??null,role:e.role??"user",content:e.content,attachments:e.attachments??[],metadata:e.metadata??{custom:{}},runConfig:e.runConfig??{},startRun:e.startRun}))}subscribe(e){return this._threadBinding.subscribe(e)}getModelContext(){return this._threadBinding.getState().getModelContext()}getModelConfig(){return this.getModelContext()}startRun(e){let t;return this._threadBinding.getState().startRun({parentId:(t=null===e||"string"==typeof e?{parentId:e}:e).parentId??null,sourceId:t.sourceId??null,runConfig:t.runConfig??{}})}unstable_resumeRun(e){return this._threadBinding.getState().resumeRun({parentId:e.parentId??null,sourceId:e.sourceId??null,runConfig:e.runConfig??{},stream:e.stream})}cancelRun(){this._threadBinding.getState().cancelRun()}stopSpeaking(){return this._threadBinding.getState().stopSpeaking()}export(){return this._threadBinding.getState().export()}import(e){this._threadBinding.getState().import(e)}reset(e){this._threadBinding.getState().reset(e)}getMesssageByIndex(e){if(e<0)throw Error("Message index must be >= 0");return this._getMessageRuntime({...this.path,ref:this.path.ref+`${this.path.ref}.messages[${e}]`,messageSelector:{type:"index",index:e}},()=>{let t=this._threadBinding.getState().messages,n=t[e];if(n)return{message:n,parentId:t[e-1]?.id??null}})}getMesssageById(e){return this._getMessageRuntime({...this.path,ref:this.path.ref+`${this.path.ref}.messages[messageId=${JSON.stringify(e)}]`,messageSelector:{type:"messageId",messageId:e}},()=>this._threadBinding.getState().getMessageById(e))}_getMessageRuntime(e,t){return new ew(new ei({path:e,getState:()=>{let{message:e,parentId:n}=t()??{},{messages:r,speech:i}=this._threadBinding.getState();if(!e||void 0===n)return et;let o=this._threadBinding.getState(),s=o.getBranches(e.id),a=o.getSubmittedFeedback(e.id);return{...e,...{[eo]:e[eo]},isLast:r.at(-1)?.id===e.id,parentId:n,branchNumber:s.indexOf(e.id)+1,branchCount:s.length,speech:i?.messageId===e.id?i:void 0,submittedFeedback:a}},subscribe:e=>this._threadBinding.subscribe(e)}),this._threadBinding)}_eventSubscriptionSubjects=new Map;unstable_on(e,t){let n=this._eventSubscriptionSubjects.get(e);return n||(n=new eh({event:e,binding:this._threadBinding}),this._eventSubscriptionSubjects.set(e,n)),n.subscribe(t)}},eS=(e,t)=>{if(void 0===t)return et;let n=e.getItemById(t);return n?{id:n.threadId,threadId:n.threadId,remoteId:n.remoteId,externalId:n.externalId,title:n.title,status:n.status,isMain:n.threadId===e.mainThreadId}:et},eE=class{constructor(e,t=ek){this._core=e,this._runtimeFactory=t;let n=new en({path:{},getState:()=>(e=>({mainThreadId:e.mainThreadId,newThread:e.newThreadId,threads:e.threadIds,archivedThreads:e.archivedThreadIds,isLoading:e.isLoading}))(e),subscribe:t=>e.subscribe(t)});this._getState=n.getState.bind(n),this._mainThreadListItemRuntime=new er(new ei({path:{ref:"threadItems[main]",threadSelector:{type:"main"}},getState:()=>eS(this._core,this._core.mainThreadId),subscribe:e=>this._core.subscribe(e)}),this._core),this.main=new t(new ev({path:{ref:"threads.main",threadSelector:{type:"main"}},getState:()=>e.getMainThreadRuntimeCore(),subscribe:t=>e.subscribe(t)}),this._mainThreadListItemRuntime)}_getState;__internal_bindMethods(){this.switchToThread=this.switchToThread.bind(this),this.switchToNewThread=this.switchToNewThread.bind(this),this.getState=this.getState.bind(this),this.subscribe=this.subscribe.bind(this),this.getById=this.getById.bind(this),this.getItemById=this.getItemById.bind(this),this.getItemByIndex=this.getItemByIndex.bind(this),this.getArchivedItemByIndex=this.getArchivedItemByIndex.bind(this)}switchToThread(e){return this._core.switchToThread(e)}switchToNewThread(){return this._core.switchToNewThread()}getState(){return this._getState()}get isLoading(){return this._core.isLoading}subscribe(e){return this._core.subscribe(e)}_mainThreadListItemRuntime;main;get mainItem(){return this._mainThreadListItemRuntime}getById(e){return new this._runtimeFactory(new ev({path:{ref:"threads[threadId="+JSON.stringify(e)+"]",threadSelector:{type:"threadId",threadId:e}},getState:()=>this._core.getThreadRuntimeCore(e),subscribe:e=>this._core.subscribe(e)}),this.mainItem)}getItemByIndex(e){return new er(new ei({path:{ref:`threadItems[${e}]`,threadSelector:{type:"index",index:e}},getState:()=>eS(this._core,this._core.threadIds[e]),subscribe:e=>this._core.subscribe(e)}),this._core)}getArchivedItemByIndex(e){return new er(new ei({path:{ref:`archivedThreadItems[${e}]`,threadSelector:{type:"archiveIndex",index:e}},getState:()=>eS(this._core,this._core.archivedThreadIds[e]),subscribe:e=>this._core.subscribe(e)}),this._core)}getItemById(e){return new er(new ei({path:{ref:`threadItems[threadId=${e}]`,threadSelector:{type:"threadId",threadId:e}},getState:()=>eS(this._core,e),subscribe:e=>this._core.subscribe(e)}),this._core)}},eT=class{constructor(e){this._core=e,this.threads=new eE(e.threads),this._thread=this.threads.main}threads;get threadList(){return this.threads}_thread;__internal_bindMethods(){this.switchToNewThread=this.switchToNewThread.bind(this),this.switchToThread=this.switchToThread.bind(this),this.registerModelContextProvider=this.registerModelContextProvider.bind(this),this.registerModelConfigProvider=this.registerModelConfigProvider.bind(this),this.reset=this.reset.bind(this)}get thread(){return this._thread}switchToNewThread(){return this._core.threads.switchToNewThread()}switchToThread(e){return this._core.threads.switchToThread(e)}registerModelContextProvider(e){return this._core.registerModelContextProvider(e)}registerModelConfigProvider(e){return this.registerModelContextProvider(e)}reset({initialMessages:e}={}){return this._core.threads.getMainThreadRuntimeCore().import(y.fromArray(e??[]))}},e_=class extends s{get RenderComponent(){return this.threads.__internal_RenderComponent}constructor(e){super(),this.threads=new Q(e,this._contextProvider)}},eC=e=>{try{let t=e.split(".")[1];if(!t)throw Error("Invalid JWT format");let n=t.replace(/-/g,"+").replace(/_/g,"/");for(;n.length%4!=0;)n+="=";let r=atob(n),i=JSON.parse(r).exp;if(!i||"number"!=typeof i)throw Error('JWT does not contain a valid "exp" field');return 1e3*i}catch(e){throw Error("Unable to determine the token expiry: "+e)}},eA=class{strategy="jwt";cachedToken=null;tokenExpiry=null;#e;constructor(e){this.#e=e}async getAuthHeaders(){let e=Date.now();if(this.cachedToken&&this.tokenExpiry&&this.tokenExpiry-e>3e4)return{Authorization:`Bearer ${this.cachedToken}`};let t=await this.#e();return!!t&&(this.cachedToken=t,this.tokenExpiry=eC(t),{Authorization:`Bearer ${t}`})}readAuthHeaders(e){let t=e.get("Authorization");if(!t)return;let[n,r]=t.split(" ");if("Bearer"!==n||!r)throw Error("Invalid auth header received");this.cachedToken=r,this.tokenExpiry=eC(r)}},eI=class{strategy="api-key";#t;#n;#r;constructor(e,t,n){this.#t=e,this.#n=t,this.#r=n}async getAuthHeaders(){return{Authorization:`Bearer ${this.#t}`,"Aui-User-Id":this.#n,"Aui-Workspace-Id":this.#r}}readAuthHeaders(){}},eP="aui:refresh_token",eR=class{strategy="anon";baseUrl;jwtStrategy;constructor(e){this.baseUrl=e,this.jwtStrategy=new eA(async()=>{let e=Date.now(),t=localStorage.getItem(eP),n=t?JSON.parse(t):void 0;if(n)if(new Date(n.expires_at).getTime()-e>3e4){let e=await fetch(`${this.baseUrl}/v1/auth/tokens/refresh`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refresh_token:n.token})});if(e.ok){let{access_token:t,refresh_token:n}=await e.json();return n&&localStorage.setItem(eP,JSON.stringify(n)),t}}else localStorage.removeItem(eP);let r=await fetch(`${this.baseUrl}/v1/auth/tokens/anonymous`,{method:"POST"});if(!r.ok)return null;let{access_token:i,refresh_token:o}=await r.json();return i&&o?(localStorage.setItem(eP,JSON.stringify(o)),i):null})}async getAuthHeaders(){return this.jwtStrategy.getAuthHeaders()}readAuthHeaders(e){this.jwtStrategy.readAuthHeaders(e)}},eM=class extends Error{constructor(e){super(e),this.name="APIError"}},eO=class{_auth;_baseUrl;constructor(e){if("authToken"in e)this._baseUrl=e.baseUrl,this._auth=new eA(e.authToken);else if("apiKey"in e)this._baseUrl="https://backend.assistant-api.com",this._auth=new eI(e.apiKey,e.userId,e.workspaceId);else if("anonymous"in e)this._baseUrl=e.baseUrl,this._auth=new eR(e.baseUrl);else throw Error("Invalid configuration: Must provide authToken, apiKey, or anonymous configuration")}async initializeAuth(){return!!this._auth.getAuthHeaders()}async makeRawRequest(e,t={}){let n=await this._auth.getAuthHeaders();if(!n)throw Error("Authorization failed");let r={...n,...t.headers,"Content-Type":"application/json"},i=new URLSearchParams;if(t.query)for(let[e,n]of Object.entries(t.query))!1!==n&&(!0===n?i.set(e,"true"):i.set(e,n.toString()));let o=new URL(`${this._baseUrl}/v1${e}`);o.search=i.toString();let s=await fetch(o,{method:t.method??"GET",headers:r,body:t.body?JSON.stringify(t.body):null});if(this._auth.readAuthHeaders(s.headers),!s.ok){let e=await s.text();try{let t=JSON.parse(e);throw new eM(t.message)}catch{throw Error(`Request failed with status ${s.status}, ${e}`)}}return s}async makeRequest(e,t={}){return(await this.makeRawRequest(e,t)).json()}},eD=class{constructor(e){this.cloud=e}async create(){return this.cloud.makeRequest("/auth/tokens",{method:"POST"})}},eL={toResponse:(e,t)=>new Response(eL.toByteStream(e,t),{headers:t.headers??{}}),fromResponse:(e,t)=>eL.fromByteStream(e.body,t),toByteStream:(e,t)=>e.pipeThrough(t),fromByteStream:(e,t)=>e.pipeThrough(t)},ej=function(){let e,t,n=new Promise((n,r)=>{e=n,t=r});if(!e||!t)throw Error("Failed to create promise");return{promise:n,resolve:e,reject:t}},eB=class{_controller;_isClosed=!1;constructor(e){this._controller=e}append(e){return this._controller.enqueue({type:"text-delta",path:[],textDelta:e}),this}close(){this._isClosed||(this._isClosed=!0,this._controller.enqueue({type:"part-finish",path:[]}),this._controller.close())}},eN=e=>new ReadableStream({start:t=>e.start?.(new eB(t)),pull:t=>e.pull?.(new eB(t)),cancel:t=>e.cancel?.(t)}),eF=()=>{let e;return[eN({start(t){e=t}}),e]},ez=class{constructor(e){this._controller=e;let t=eN({start:e=>{this._argsTextController=e}}),n=!1;this._mergeTask=t.pipeTo(new WritableStream({write:e=>{switch(e.type){case"text-delta":n=!0,this._controller.enqueue(e);break;case"part-finish":n||this._controller.enqueue({type:"text-delta",textDelta:"{}",path:[]}),this._controller.enqueue({type:"tool-call-args-text-finish",path:[]});break;default:throw Error(`Unexpected chunk type: ${e.type}`)}}}))}_isClosed=!1;_mergeTask;get argsText(){return this._argsTextController}_argsTextController;async setResponse(e){this._argsTextController.close(),await Promise.resolve(),this._controller.enqueue({type:"result",path:[],...void 0!==e.artifact?{artifact:e.artifact}:{},result:e.result,isError:e.isError??!1})}async close(){this._isClosed||(this._isClosed=!0,this._argsTextController.close(),await this._mergeTask,this._controller.enqueue({type:"part-finish",path:[]}),this._controller.close())}},eU=class{value=-1;up(){return++this.value}},eV=class extends TransformStream{constructor(e){super({transform(t,n){n.enqueue({...t,path:[e,...t.path]})}})}};TransformStream;var eH=class extends TransformStream{constructor(e){let t=new eU,n=new Map;super({transform(r,i){"part-start"===r.type&&0===r.path.length&&n.set(t.up(),e.up());let[o,...s]=r.path;if(void 0===o)return void i.enqueue(r);let a=n.get(o);if(void 0===a)throw Error("Path not found");i.enqueue({...r,path:[a,...s]})}})}},eW=class e{_state;_parentId;constructor(e){this._state=e||{merger:(()=>{let e,t,n=[],r=!1,i=i=>{i.promise||(i.promise=i.reader.read().then(({done:o,value:s})=>{i.promise=void 0,o?(n.splice(n.indexOf(i),1),r&&0===n.length&&e.close()):e.enqueue(s),t?.resolve(),t=void 0}).catch(r=>{console.error(r),n.forEach(e=>{e.reader.cancel()}),n.length=0,e.error(r),t?.reject(r),t=void 0}))};return{readable:new ReadableStream({start(t){e=t},pull:()=>(t=ej(),n.forEach(e=>{i(e)}),t.promise),cancel(){n.forEach(e=>{e.reader.cancel()}),n.length=0}}),isSealed:()=>r,seal(){r=!0,0===n.length&&e.close()},addStream(e){if(r)throw Error("Cannot add streams after the run callback has settled.");let t={reader:e.getReader()};n.push(t),i(t)},enqueue(e){this.addStream(new ReadableStream({start(t){t.enqueue(e),t.close()}}))}}})(),contentCounter:new eU}}get __internal_isClosed(){return this._state.merger.isSealed()}__internal_getReadable(){return this._state.merger.readable}__internal_subscribeToClose(e){this._state.closeSubscriber=e}_addPart(e,t){this._state.append&&(this._state.append.controller.close(),this._state.append=void 0),this.enqueue({type:"part-start",part:e,path:[]}),this._state.merger.addStream(t.pipeThrough(new eV(this._state.contentCounter.value)))}merge(e){this._state.merger.addStream(e.pipeThrough(new eH(this._state.contentCounter)))}appendText(e){this._state.append?.kind!=="text"&&(this._state.append={kind:"text",controller:this.addTextPart()}),this._state.append.controller.append(e)}appendReasoning(e){this._state.append?.kind!=="reasoning"&&(this._state.append={kind:"reasoning",controller:this.addReasoningPart()}),this._state.append.controller.append(e)}addTextPart(){let[e,t]=eF();return this._addPart({type:"text"},e),t}addReasoningPart(){let[e,t]=eF();return this._addPart({type:"reasoning"},e),t}addToolCallPart(e){let t,n,r="string"==typeof e?{toolName:e}:e,i=r.toolName,o=r.toolCallId??W(),[s,a]=[(n={start(e){t=e}},new ReadableStream({start:e=>n.start?.(new ez(e)),pull:e=>n.pull?.(new ez(e)),cancel:e=>n.cancel?.(e)})),t];return this._addPart({type:"tool-call",toolName:i,toolCallId:o,...this._parentId&&{parentId:this._parentId}},s),void 0!==r.argsText&&(a.argsText.append(r.argsText),a.argsText.close()),void 0!==r.args&&(a.argsText.append(JSON.stringify(r.args)),a.argsText.close()),void 0!==r.response&&a.setResponse(r.response),a}appendSource(e){this._addPart({...e,...this._parentId&&{parentId:this._parentId}},new ReadableStream({start(e){e.enqueue({type:"part-finish",path:[]}),e.close()}}))}appendFile(e){this._addPart(e,new ReadableStream({start(e){e.enqueue({type:"part-finish",path:[]}),e.close()}}))}enqueue(e){this._state.merger.enqueue(e),"part-start"===e.type&&0===e.path.length&&this._state.contentCounter.up()}withParentId(t){let n=new e(this._state);return n._parentId=t,n}close(){this._state.merger.seal(),this._state.append?.controller?.close(),this._state.closeSubscriber?.()}},e$=class extends TransformStream{constructor(e,t,n){let r,[i,o]=function(){let e,{resolve:t,promise:n}=ej();return[function(e){let t,n=new eW;try{t=e(n)}catch(e){throw n.__internal_isClosed||(n.enqueue({type:"error",path:[],error:String(e)}),n.close()),e}return t instanceof Promise?(async()=>{try{await t}catch(e){throw n.__internal_isClosed||n.enqueue({type:"error",path:[],error:String(e)}),e}finally{n.__internal_isClosed||n.close()}})():n.__internal_isClosed||n.close(),n.__internal_getReadable()}(r=>((e=r).__internal_subscribeToClose(t),n)),e]}();super({start:t=>(r=i.pipeTo(new WritableStream({write(e){t.enqueue(e)},abort(e){t.error(e)},close(){t.terminate()}})).catch(e=>{t.error(e)}),e.start?.(o)),transform:t=>e.transform?.(t,o),async flush(){await e.flush?.(o),o.close(),await r}},t,n)}},eq=class extends TransformStream{constructor(e){super(),Object.defineProperty(this,"readable",{value:e(super.readable),writable:!1})}},eK=class extends eq{constructor(){super(e=>{let t=new e$({transform(e,t){t.appendText(e)}});return e.pipeThrough(new TextDecoderStream).pipeThrough(t)})}},eY=class{constructor(e){this.cloud=e}__internal_getAssistantOptions(e){return{api:this.cloud._baseUrl+"/v1/runs/stream",headers:async()=>{let e=await this.cloud._auth.getAuthHeaders();if(!e)throw Error("Authorization failed");return{...e,Accept:"text/plain"}},body:{assistant_id:e,response_format:"vercel-ai-data-stream/v1",thread_id:"unstable_todo"}}}async stream(e){let t=await this.cloud.makeRawRequest("/runs/stream",{method:"POST",headers:{Accept:"text/plain"},body:e});return eL.fromResponse(t,new eK)}},eX=class{constructor(e){this.cloud=e}async list(e,t){return this.cloud.makeRequest(`/threads/${encodeURIComponent(e)}/messages`,{query:t})}async create(e,t){return this.cloud.makeRequest(`/threads/${encodeURIComponent(e)}/messages`,{method:"POST",body:t})}},eG=class{constructor(e){this.cloud=e,this.messages=new eX(e)}messages;async list(e){return this.cloud.makeRequest("/threads",{query:e})}async create(e){return this.cloud.makeRequest("/threads",{method:"POST",body:e})}async update(e,t){return this.cloud.makeRequest(`/threads/${encodeURIComponent(e)}`,{method:"PUT",body:t})}async delete(e){return this.cloud.makeRequest(`/threads/${encodeURIComponent(e)}`,{method:"DELETE"})}},eJ=class{constructor(e){this.cloud=e}async pdfToImages(e){return this.cloud.makeRequest("/files/pdf-to-images",{method:"POST",body:e})}async generatePresignedUploadUrl(e){return this.cloud.makeRequest("/files/attachments/generate-presigned-upload-url",{method:"POST",body:e})}},eZ=class{threads;auth;runs;files;constructor(e){let t=new eO(e);this.threads=new eG(t),this.auth={tokens:new eD(t)},this.runs=new eY(t),this.files=new eJ(t)}},eQ=e=>{let t=e.content,n=f({id:e.id,createdAt:e.created_at,...t},e.id,{type:"complete",reason:"unknown"});return{parentId:e.parent_id,message:n}},e0=class{constructor(e,t){this.parent=e,this.formatAdapter=t}async append(e){let t=this.formatAdapter.encode(e),n=this.formatAdapter.getId(e.message);return this.parent._appendWithFormat(e.parentId,n,this.formatAdapter.format,t)}async load(){return this.parent._loadWithFormat(this.formatAdapter.format,e=>this.formatAdapter.decode(e))}},e1=class{constructor(e,t){this.cloudRef=e,this.threadListItemRuntime=t}_getIdForLocalId={};withFormat(e){return new e0(this,e)}async append({parentId:e,message:t}){let{remoteId:n}=await this.threadListItemRuntime.initialize(),r=this.cloudRef.current.threads.messages.create(n,{parent_id:e?await this._getIdForLocalId[e]??e:null,format:"aui/v0",content:{role:t.role,content:t.content.map(e=>{let t=e.type;switch(t){case"text":return{type:"text",text:e.text};case"reasoning":return{type:"reasoning",text:e.text};case"source":return{type:"source",sourceType:e.sourceType,id:e.id,url:e.url,...e.title?{title:e.title}:void 0};case"tool-call":return!function e(t,n=0){return!(n>100)&&(null===t||"string"==typeof t||"boolean"==typeof t||("number"==typeof t?!Number.isNaN(t)&&Number.isFinite(t):Array.isArray(t)?t.every(t=>e(t,n+1)):"object"==typeof t&&Object.entries(t).every(([t,r])=>"string"==typeof t&&e(r,n+1))))}(e.result)&&console.warn("tool-call result is not JSON! "+JSON.stringify(e)),{type:"tool-call",toolCallId:e.toolCallId,toolName:e.toolName,...JSON.stringify(e.args)===e.argsText?{args:e.args}:{argsText:e.argsText},...e.result?{result:e.result}:{},...e.isError?{isError:!0}:{}};default:throw Error(`Message part type not supported by aui/v0: ${t}`)}}),metadata:t.metadata,...t.status?{status:t.status?.type==="running"?{type:"incomplete",reason:"cancelled"}:t.status}:void 0}}).then(({message_id:e})=>(this._getIdForLocalId[t.id]=e,e));return this._getIdForLocalId[t.id]=r,r.then(()=>{})}async load(){let e=this.threadListItemRuntime.getState().remoteId;if(!e)return{messages:[]};let{messages:t}=await this.cloudRef.current.threads.messages.list(e,{format:"aui/v0"});return{messages:t.filter(e=>"aui/v0"===e.format).map(eQ).reverse()}}async _appendWithFormat(e,t,n,r){let{remoteId:i}=await this.threadListItemRuntime.initialize(),o=this.cloudRef.current.threads.messages.create(i,{parent_id:e?await this._getIdForLocalId[e]??e:null,format:n,content:r}).then(({message_id:e})=>(this._getIdForLocalId[t]=e,e));return this._getIdForLocalId[t]=o,o.then(()=>{})}async _loadWithFormat(e,t){let n=this.threadListItemRuntime.getState().remoteId;if(!n)return{messages:[]};let{messages:r}=await this.cloudRef.current.threads.messages.list(n,{format:e});return{messages:r.filter(t=>t.format===e).map(e=>t({id:e.id,parent_id:e.parent_id,format:e.format,content:e.content})).reverse()}}},e2=class{list(){return Promise.resolve({threads:[]})}rename(){return Promise.resolve()}archive(){return Promise.resolve()}unarchive(){return Promise.resolve()}delete(){return Promise.resolve()}initialize(e){return Promise.resolve({remoteId:e,externalId:void 0})}generateTitle(){return Promise.resolve(new ReadableStream)}},e5=n(9509),e3=void 0!==e5&&(null==e5||null==(r=e5.env)?void 0:r.NEXT_PUBLIC_ASSISTANT_BASE_URL),e6=e3?new eZ({baseUrl:e3,anonymous:!0}):void 0,e4=function(e){let{cloud:t,...n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var r={runtimeHook:function(){return((e,t)=>{var n;let{initialMessages:r,...o}=t,{modelContext:s,...a}=null!=(n=(0,i.useContext)(O))?n:{},l=(0,i.useMemo)(()=>({...o,adapters:{...a,...o.adapters,chatModel:e}}),[e,o,a]),[u]=(0,i.useState)(()=>new R(l,r));return(0,i.useEffect)(()=>()=>{u.threads.getMainThreadRuntimeCore().detach()},[u]),(0,i.useEffect)(()=>{u.threads.getMainThreadRuntimeCore().__internal_setOptions(l),u.threads.getMainThreadRuntimeCore().__internal_load()},[u,l]),(0,i.useEffect)(()=>{if(s)return u.registerModelContextProvider(s)},[s,u]),(0,i.useMemo)(()=>new eT(u),[u])})(e,n)},adapter:(e=>{var t;let n=(0,i.useRef)(e);(0,i.useEffect)(()=>{n.current=e},[e]);let r=(0,i.useCallback)(function(e){let{children:t}=e,r=(e=>{let t=(0,N.pK)(),[n]=(0,i.useState)(()=>new e1(e,t));return n})({get current(){var o;return null!=(o=n.current.cloud)?o:e6}}),s=(0,i.useMemo)(()=>({history:r}),[r]);return(0,M.jsx)(D,{adapters:s,children:t})},[]),o=null!=(t=e.cloud)?t:e6;return o?{list:async()=>{let{threads:e}=await o.threads.list();return{threads:e.map(e=>{var t;return{status:e.is_archived?"archived":"regular",remoteId:e.id,title:e.title,externalId:null!=(t=e.external_id)?t:void 0}})}},initialize:async()=>{var t,n;let r=null!=(n=null==(t=e.create)?void 0:t.call(e))?n:Promise.resolve(),i=await r,s=i?i.externalId:void 0,{thread_id:a}=await o.threads.create({last_message_at:new Date,external_id:s});return{externalId:s,remoteId:a}},rename:async(e,t)=>o.threads.update(e,{title:t}),archive:async e=>o.threads.update(e,{is_archived:!0}),unarchive:async e=>o.threads.update(e,{is_archived:!1}),delete:async t=>{var n;return await (null==(n=e.delete)?void 0:n.call(e,t)),o.threads.delete(t)},generateTitle:async(e,t)=>o.runs.stream({thread_id:e,assistant_id:"system/thread_title",messages:t}),unstable_Provider:r}:new e2})({cloud:t})};let[o]=(0,i.useState)(()=>new e_(r));return(0,i.useEffect)(()=>{o.threads.__internal_setOptions(r),o.threads.__internal_load()},[o,r]),(0,i.useMemo)(()=>new eT(o),[o])}},1748:(e,t,n)=>{"use strict";n.d(t,{O:()=>s});var r=n(9033),i=n(2115),o=n(7589),s=e=>{let t=(0,r.c)(e),n=(0,o.qZ)(e=>e.onScrollToBottom);(0,i.useEffect)(()=>n(t),[n,t])}},1877:(e,t,n)=>{"use strict";function r(e,t,n){let r=[],i=-1;for(;++i<e.length;){let o=e[i].resolveAll;o&&!r.includes(o)&&(t=o(t,n),r.push(o))}return t}n.d(t,{W:()=>r})},1880:(e,t,n)=>{"use strict";n.d(t,{Y:()=>a});var r=n(3655),i=n(2115),o=n(4773),s=n(5155),a=(0,i.forwardRef)((e,t)=>{let{hideWhenSingleBranch:n,...i}=e;return(0,s.jsx)(o.s,{hasBranches:!!n||void 0,children:(0,s.jsx)(r.sG.div,{...i,ref:t})})});a.displayName="BranchPickerPrimitive.Root"},1887:(e,t,n)=>{"use strict";n.d(t,{I:()=>c});var r=n(2115),i=n(5185),o=n(3655),s=n(6882),a=n(2605),l=n(5155),u=function(){let{copiedDuration:e=3e3}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,s.LN)(),n=(0,a.I)(),i=(0,s.uW)(e=>e.setIsCopied),o=(0,s.JX)(e=>("assistant"!==e.role||"running"!==e.status.type)&&e.content.some(e=>"text"===e.type&&e.text.length>0)),l=(0,r.useCallback)(()=>{let{isEditing:r,text:o}=n.getState(),s=r?o:t.unstable_getCopyText();navigator.clipboard.writeText(s).then(()=>{i(!0),setTimeout(()=>i(!1),e)})},[t,i,n,e]);return o?l:null},c=(0,r.forwardRef)((e,t)=>{let{copiedDuration:n,onClick:r,disabled:a,...c}=e,h=(0,s.uW)(e=>e.isCopied),d=u({copiedDuration:n});return(0,l.jsx)(o.sG.button,{type:"button",...h?{"data-copied":"true"}:{},...c,ref:t,disabled:a||!d,onClick:(0,i.mK)(r,()=>{null==d||d()})})});c.displayName="ActionBarPrimitive.Copy"},1922:(e,t,n)=>{"use strict";n.d(t,{dc:()=>o,VG:()=>s});var r=n(7915);let i=[],o=!1;function s(e,t,n,s){let a;"function"==typeof t&&"function"!=typeof n?(s=n,n=t):a=t;let l=(0,r.C)(a),u=s?-1:1;(function e(r,a,c){let h=r&&"object"==typeof r?r:{};if("string"==typeof h.type){let e="string"==typeof h.tagName?h.tagName:"string"==typeof h.name?h.name:void 0;Object.defineProperty(d,"name",{value:"node ("+r.type+(e?"<"+e+">":"")+")"})}return d;function d(){var h;let d,f,p,m=i;if((!t||l(r,a,c[c.length-1]||void 0))&&(m=Array.isArray(h=n(r,c))?h:"number"==typeof h?[!0,h]:null==h?i:[h])[0]===o)return m;if("children"in r&&r.children&&r.children&&"skip"!==m[0])for(f=(s?r.children.length:-1)+u,p=c.concat(r);f>-1&&f<r.children.length;){if((d=e(r.children[f],f,p)())[0]===o)return d;f="number"==typeof d[1]?d[1]:f+u}return m}})(e,void 0,[])()}},2042:(e,t,n)=>{"use strict";n.d(t,{l:()=>s});var r=n(8716),i=n(2115),o=n(6882),s=(0,r.x)("ActionBarPrimitive.Edit",()=>{let e=(0,o.LN)(),t=(0,o.iP)(e=>e.isEditing),n=(0,i.useCallback)(()=>{e.composer.beginEdit()},[e]);return t?null:n})},2081:(e,t,n)=>{"use strict";n.d(t,{c:()=>m});var r=n(6101),i=n(3655),o=n(2115),s=n(5455),a=n(9033),l=n(2156),u=n(1748),c=n(9935),h=n(7589),d=n(7788),f=n(5155),p=(0,o.forwardRef)((e,t)=>{let{autoScroll:n,children:d,...p}=e,m=(e=>{let{autoScroll:t=!0}=e,n=(0,o.useRef)(null),i=(0,h.D0)(),d=(0,o.useRef)(0),f=(0,o.useRef)(!1),p=(0,o.useCallback)(e=>{let r=n.current;r&&t&&(f.current=!0,r.scrollTo({top:r.scrollHeight,behavior:e}))},[t]),m=()=>{let e=n.current;if(!e)return;let t=i.getState().isAtBottom,r=e.scrollHeight-e.scrollTop<=e.clientHeight+1;!r&&d.current<e.scrollTop||(r&&(f.current=!1),r!==t&&(0,c.s)(i).setState({isAtBottom:r})),d.current=e.scrollTop},g=(e=>{let t=(0,a.c)(e),n=(0,o.useCallback)(e=>{let n=new ResizeObserver(()=>{t()}),r=new MutationObserver(()=>{t()});return n.observe(e),r.observe(e,{childList:!0,subtree:!0,attributes:!0,characterData:!0}),()=>{n.disconnect(),r.disconnect()}},[t]);return(0,l.s)(n)})(()=>{(f.current||i.getState().isAtBottom)&&p("instant"),m()}),y=(0,l.s)(e=>(e.addEventListener("scroll",m),()=>{e.removeEventListener("scroll",m)}));(0,u.O)(()=>{p("auto")});let v=(0,s.Mp)();return(0,o.useEffect)(()=>v.unstable_on("run-start",()=>p("auto")),[p,v]),(0,r.s)(g,y,n)})({autoScroll:n}),g=(0,r.s)(t,m);return(0,f.jsx)(i.sG.div,{...p,ref:g,children:d})});p.displayName="ThreadPrimitive.ViewportScrollable";var m=(0,o.forwardRef)((e,t)=>(0,f.jsx)(d.U,{children:(0,f.jsx)(p,{...e,ref:t})}));m.displayName="ThreadPrimitive.Viewport"},2085:(e,t,n)=>{"use strict";n.d(t,{F:()=>s});var r=n(2596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.$,s=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:s,defaultVariants:a}=t,l=Object.keys(s).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let o=i(t)||i(r);return s[e][o]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,l,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...u}[t]):({...a,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},2156:(e,t,n)=>{"use strict";n.d(t,{s:()=>i});var r=n(2115),i=e=>{let t=(0,r.useRef)(void 0);return(0,r.useCallback)(n=>{t.current&&t.current(),n&&(t.current=e(n))},[e])}},2288:(e,t,n)=>{"use strict";n.d(t,{k:()=>o});var r=n(2115),i=n(4229);function o(e){return function(t){let n,o=!1;"function"==typeof t?n=t:t&&(o=!!t.optional,n=t.selector);let s=e({optional:o});return s?function(e,t=e=>e){(0,i.Q)(e);let n=(0,r.useSyncExternalStore)(e.subscribe,()=>t(e.getState()),()=>t(e.getState()));return(0,r.useDebugValue)(n),n}(s,n):null}}},2355:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},2432:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},2500:(e,t,n)=>{"use strict";n.d(t,{d:()=>i});var r=n(949),i=()=>(0,r.ME)(e=>{if("text"!==e.type&&"reasoning"!==e.type)throw Error("MessagePartText can only be used inside text or reasoning message parts.");return e})},2556:(e,t,n)=>{"use strict";n.d(t,{BM:()=>a,CW:()=>r,Ee:()=>h,HP:()=>c,JQ:()=>s,Ny:()=>p,On:()=>d,cx:()=>o,es:()=>f,lV:()=>i,ok:()=>l,ol:()=>u});let r=m(/[A-Za-z]/),i=m(/[\dA-Za-z]/),o=m(/[#-'*+\--9=?A-Z^-~]/);function s(e){return null!==e&&(e<32||127===e)}let a=m(/\d/),l=m(/[\dA-Fa-f]/),u=m(/[!-/:-@[-`{-~]/);function c(e){return null!==e&&e<-2}function h(e){return null!==e&&(e<0||32===e)}function d(e){return -2===e||-1===e||32===e}let f=m(/\p{P}|\p{S}/u),p=m(/\s/);function m(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}},2596:(e,t,n)=>{"use strict";function r(){for(var e,t,n=0,r="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=function e(t){var n,r,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(n=0;n<o;n++)t[n]&&(r=e(t[n]))&&(i&&(i+=" "),i+=r)}else for(r in t)t[r]&&(i&&(i+=" "),i+=r);return i}(e))&&(r&&(r+=" "),r+=t);return r}n.d(t,{$:()=>r})},2605:(e,t,n)=>{"use strict";n.d(t,{I:()=>o,s:()=>s});var r=n(6882),i=n(5455);function o(e){var t;let n=(0,r.LN)({optional:!0}),o=(0,i.Mp)(e);return n?n.composer:null!=(t=null==o?void 0:o.composer)?t:null}var s=(0,n(2288).k)(o)},2664:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return o}});let r=n(9991),i=n(7102);function o(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,i.hasBasePath)(n.pathname)}catch(e){return!1}}},2700:(e,t,n)=>{"use strict";let r=n(1741),i=n(7016),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function s(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');let t=new Uint8Array(e);return Object.setPrototypeOf(t,a.prototype),t}function a(e,t,n){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return c(e)}return l(e,t,n)}function l(e,t,n){if("string"==typeof e){var r=e,i=t;if(("string"!=typeof i||""===i)&&(i="utf8"),!a.isEncoding(i))throw TypeError("Unknown encoding: "+i);let n=0|p(r,i),o=s(n),l=o.write(r,i);return l!==n&&(o=o.slice(0,l)),o}if(ArrayBuffer.isView(e)){var o=e;if(B(o,Uint8Array)){let e=new Uint8Array(o);return d(e.buffer,e.byteOffset,e.byteLength)}return h(o)}if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(B(e,ArrayBuffer)||e&&B(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(B(e,SharedArrayBuffer)||e&&B(e.buffer,SharedArrayBuffer)))return d(e,t,n);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');let l=e.valueOf&&e.valueOf();if(null!=l&&l!==e)return a.from(l,t,n);let u=function(e){if(a.isBuffer(e)){let t=0|f(e.length),n=s(t);return 0===n.length||e.copy(n,0,0,t),n}return void 0!==e.length?"number"!=typeof e.length||function(e){return e!=e}(e.length)?s(0):h(e):"Buffer"===e.type&&Array.isArray(e.data)?h(e.data):void 0}(e);if(u)return u;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return a.from(e[Symbol.toPrimitive]("string"),t,n);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function u(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function c(e){return u(e),s(e<0?0:0|f(e))}function h(e){let t=e.length<0?0:0|f(e.length),n=s(t);for(let r=0;r<t;r+=1)n[r]=255&e[r];return n}function d(e,t,n){let r;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(n||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(r=void 0===t&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,t):new Uint8Array(e,t,n),a.prototype),r}function f(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function p(e,t){if(a.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||B(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);let n=e.length,r=arguments.length>2&&!0===arguments[2];if(!r&&0===n)return 0;let i=!1;for(;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return D(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return L(e).length;default:if(i)return r?-1:D(e).length;t=(""+t).toLowerCase(),i=!0}}function m(e,t,n){let i=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===n||n>this.length)&&(n=this.length),n<=0||(n>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,n){let r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);let i="";for(let r=t;r<n;++r)i+=N[e[r]];return i}(this,t,n);case"utf8":case"utf-8":return b(this,t,n);case"ascii":return function(e,t,n){let r="";n=Math.min(e.length,n);for(let i=t;i<n;++i)r+=String.fromCharCode(127&e[i]);return r}(this,t,n);case"latin1":case"binary":return function(e,t,n){let r="";n=Math.min(e.length,n);for(let i=t;i<n;++i)r+=String.fromCharCode(e[i]);return r}(this,t,n);case"base64":var o,s,a;return o=this,s=t,a=n,0===s&&a===o.length?r.fromByteArray(o):r.fromByteArray(o.slice(s,a));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,n){let r=e.slice(t,n),i="";for(let e=0;e<r.length-1;e+=2)i+=String.fromCharCode(r[e]+256*r[e+1]);return i}(this,t,n);default:if(i)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),i=!0}}function g(e,t,n){let r=e[t];e[t]=e[n],e[n]=r}function y(e,t,n,r,i){var o;if(0===e.length)return -1;if("string"==typeof n?(r=n,n=0):n>0x7fffffff?n=0x7fffffff:n<-0x80000000&&(n=-0x80000000),(o=n*=1)!=o&&(n=i?0:e.length-1),n<0&&(n=e.length+n),n>=e.length)if(i)return -1;else n=e.length-1;else if(n<0)if(!i)return -1;else n=0;if("string"==typeof t&&(t=a.from(t,r)),a.isBuffer(t))return 0===t.length?-1:v(e,t,n,r,i);if("number"==typeof t){if(t&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(e,t,n);else return Uint8Array.prototype.lastIndexOf.call(e,t,n);return v(e,[t],n,r,i)}throw TypeError("val must be string, number or Buffer")}function v(e,t,n,r,i){let o,s=1,a=e.length,l=t.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return -1;s=2,a/=2,l/=2,n/=2}function u(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(i){let r=-1;for(o=n;o<a;o++)if(u(e,o)===u(t,-1===r?0:o-r)){if(-1===r&&(r=o),o-r+1===l)return r*s}else -1!==r&&(o-=o-r),r=-1}else for(n+l>a&&(n=a-l),o=n;o>=0;o--){let n=!0;for(let r=0;r<l;r++)if(u(e,o+r)!==u(t,r)){n=!1;break}if(n)return o}return -1}function b(e,t,n){n=Math.min(e.length,n);let r=[],i=t;for(;i<n;){let t=e[i],o=null,s=t>239?4:t>223?3:t>191?2:1;if(i+s<=n){let n,r,a,l;switch(s){case 1:t<128&&(o=t);break;case 2:(192&(n=e[i+1]))==128&&(l=(31&t)<<6|63&n)>127&&(o=l);break;case 3:n=e[i+1],r=e[i+2],(192&n)==128&&(192&r)==128&&(l=(15&t)<<12|(63&n)<<6|63&r)>2047&&(l<55296||l>57343)&&(o=l);break;case 4:n=e[i+1],r=e[i+2],a=e[i+3],(192&n)==128&&(192&r)==128&&(192&a)==128&&(l=(15&t)<<18|(63&n)<<12|(63&r)<<6|63&a)>65535&&l<1114112&&(o=l)}}null===o?(o=65533,s=1):o>65535&&(o-=65536,r.push(o>>>10&1023|55296),o=56320|1023&o),r.push(o),i+=s}var o=r;let s=o.length;if(s<=4096)return String.fromCharCode.apply(String,o);let a="",l=0;for(;l<s;)a+=String.fromCharCode.apply(String,o.slice(l,l+=4096));return a}function x(e,t,n){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>n)throw RangeError("Trying to access beyond buffer length")}function w(e,t,n,r,i,o){if(!a.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw RangeError('"value" argument is out of bounds');if(n+r>e.length)throw RangeError("Index out of range")}function k(e,t,n,r,i){P(t,r,i,e,n,7);let o=Number(t&BigInt(0xffffffff));e[n++]=o,o>>=8,e[n++]=o,o>>=8,e[n++]=o,o>>=8,e[n++]=o;let s=Number(t>>BigInt(32)&BigInt(0xffffffff));return e[n++]=s,s>>=8,e[n++]=s,s>>=8,e[n++]=s,s>>=8,e[n++]=s,n}function S(e,t,n,r,i){P(t,r,i,e,n,7);let o=Number(t&BigInt(0xffffffff));e[n+7]=o,o>>=8,e[n+6]=o,o>>=8,e[n+5]=o,o>>=8,e[n+4]=o;let s=Number(t>>BigInt(32)&BigInt(0xffffffff));return e[n+3]=s,s>>=8,e[n+2]=s,s>>=8,e[n+1]=s,s>>=8,e[n]=s,n+8}function E(e,t,n,r,i,o){if(n+r>e.length||n<0)throw RangeError("Index out of range")}function T(e,t,n,r,o){return t*=1,n>>>=0,o||E(e,t,n,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,n,r,23,4),n+4}function _(e,t,n,r,o){return t*=1,n>>>=0,o||E(e,t,n,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,n,r,52,8),n+8}t.hp=a,t.IS=50,a.TYPED_ARRAY_SUPPORT=function(){try{let e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(e,t,n){return l(e,t,n)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(e,t,n){return(u(e),e<=0)?s(e):void 0!==t?"string"==typeof n?s(e).fill(t,n):s(e).fill(t):s(e)},a.allocUnsafe=function(e){return c(e)},a.allocUnsafeSlow=function(e){return c(e)},a.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==a.prototype},a.compare=function(e,t){if(B(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),B(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(e)||!a.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let n=e.length,r=t.length;for(let i=0,o=Math.min(n,r);i<o;++i)if(e[i]!==t[i]){n=e[i],r=t[i];break}return n<r?-1:+(r<n)},a.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(e,t){let n;if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return a.alloc(0);if(void 0===t)for(n=0,t=0;n<e.length;++n)t+=e[n].length;let r=a.allocUnsafe(t),i=0;for(n=0;n<e.length;++n){let t=e[n];if(B(t,Uint8Array))i+t.length>r.length?(a.isBuffer(t)||(t=a.from(t)),t.copy(r,i)):Uint8Array.prototype.set.call(r,t,i);else if(a.isBuffer(t))t.copy(r,i);else throw TypeError('"list" argument must be an Array of Buffers');i+=t.length}return r},a.byteLength=p,a.prototype._isBuffer=!0,a.prototype.swap16=function(){let e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(let t=0;t<e;t+=2)g(this,t,t+1);return this},a.prototype.swap32=function(){let e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(let t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},a.prototype.swap64=function(){let e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(let t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},a.prototype.toString=function(){let e=this.length;return 0===e?"":0==arguments.length?b(this,0,e):m.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(e){if(!a.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===a.compare(this,e)},a.prototype.inspect=function(){let e="",n=t.IS;return e=this.toString("hex",0,n).replace(/(.{2})/g,"$1 ").trim(),this.length>n&&(e+=" ... "),"<Buffer "+e+">"},o&&(a.prototype[o]=a.prototype.inspect),a.prototype.compare=function(e,t,n,r,i){if(B(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),t<0||n>e.length||r<0||i>this.length)throw RangeError("out of range index");if(r>=i&&t>=n)return 0;if(r>=i)return -1;if(t>=n)return 1;if(t>>>=0,n>>>=0,r>>>=0,i>>>=0,this===e)return 0;let o=i-r,s=n-t,l=Math.min(o,s),u=this.slice(r,i),c=e.slice(t,n);for(let e=0;e<l;++e)if(u[e]!==c[e]){o=u[e],s=c[e];break}return o<s?-1:+(s<o)},a.prototype.includes=function(e,t,n){return -1!==this.indexOf(e,t,n)},a.prototype.indexOf=function(e,t,n){return y(this,e,t,n,!0)},a.prototype.lastIndexOf=function(e,t,n){return y(this,e,t,n,!1)},a.prototype.write=function(e,t,n,r){var i,o,s,a,l,u,c,h;if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(n)?(n>>>=0,void 0===r&&(r="utf8")):(r=n,n=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let d=this.length-t;if((void 0===n||n>d)&&(n=d),e.length>0&&(n<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");r||(r="utf8");let f=!1;for(;;)switch(r){case"hex":return function(e,t,n,r){let i;n=Number(n)||0;let o=e.length-n;r?(r=Number(r))>o&&(r=o):r=o;let s=t.length;for(r>s/2&&(r=s/2),i=0;i<r;++i){var a;let r=parseInt(t.substr(2*i,2),16);if((a=r)!=a)break;e[n+i]=r}return i}(this,e,t,n);case"utf8":case"utf-8":return i=t,o=n,j(D(e,this.length-i),this,i,o);case"ascii":case"latin1":case"binary":return s=t,a=n,j(function(e){let t=[];for(let n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}(e),this,s,a);case"base64":return l=t,u=n,j(L(e),this,l,u);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return c=t,h=n,j(function(e,t){let n,r,i=[];for(let o=0;o<e.length&&!((t-=2)<0);++o)r=(n=e.charCodeAt(o))>>8,i.push(n%256),i.push(r);return i}(e,this.length-c),this,c,h);default:if(f)throw TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),f=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(e,t){let n=this.length;e=~~e,t=void 0===t?n:~~t,e<0?(e+=n)<0&&(e=0):e>n&&(e=n),t<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e);let r=this.subarray(e,t);return Object.setPrototypeOf(r,a.prototype),r},a.prototype.readUintLE=a.prototype.readUIntLE=function(e,t,n){e>>>=0,t>>>=0,n||x(e,t,this.length);let r=this[e],i=1,o=0;for(;++o<t&&(i*=256);)r+=this[e+o]*i;return r},a.prototype.readUintBE=a.prototype.readUIntBE=function(e,t,n){e>>>=0,t>>>=0,n||x(e,t,this.length);let r=this[e+--t],i=1;for(;t>0&&(i*=256);)r+=this[e+--t]*i;return r},a.prototype.readUint8=a.prototype.readUInt8=function(e,t){return e>>>=0,t||x(e,1,this.length),this[e]},a.prototype.readUint16LE=a.prototype.readUInt16LE=function(e,t){return e>>>=0,t||x(e,2,this.length),this[e]|this[e+1]<<8},a.prototype.readUint16BE=a.prototype.readUInt16BE=function(e,t){return e>>>=0,t||x(e,2,this.length),this[e]<<8|this[e+1]},a.prototype.readUint32LE=a.prototype.readUInt32LE=function(e,t){return e>>>=0,t||x(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},a.prototype.readUint32BE=a.prototype.readUInt32BE=function(e,t){return e>>>=0,t||x(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},a.prototype.readBigUInt64LE=F(function(e){R(e>>>=0,"offset");let t=this[e],n=this[e+7];(void 0===t||void 0===n)&&M(e,this.length-8);let r=t+256*this[++e]+65536*this[++e]+0x1000000*this[++e],i=this[++e]+256*this[++e]+65536*this[++e]+0x1000000*n;return BigInt(r)+(BigInt(i)<<BigInt(32))}),a.prototype.readBigUInt64BE=F(function(e){R(e>>>=0,"offset");let t=this[e],n=this[e+7];(void 0===t||void 0===n)&&M(e,this.length-8);let r=0x1000000*t+65536*this[++e]+256*this[++e]+this[++e],i=0x1000000*this[++e]+65536*this[++e]+256*this[++e]+n;return(BigInt(r)<<BigInt(32))+BigInt(i)}),a.prototype.readIntLE=function(e,t,n){e>>>=0,t>>>=0,n||x(e,t,this.length);let r=this[e],i=1,o=0;for(;++o<t&&(i*=256);)r+=this[e+o]*i;return r>=(i*=128)&&(r-=Math.pow(2,8*t)),r},a.prototype.readIntBE=function(e,t,n){e>>>=0,t>>>=0,n||x(e,t,this.length);let r=t,i=1,o=this[e+--r];for(;r>0&&(i*=256);)o+=this[e+--r]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},a.prototype.readInt8=function(e,t){return(e>>>=0,t||x(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},a.prototype.readInt16LE=function(e,t){e>>>=0,t||x(e,2,this.length);let n=this[e]|this[e+1]<<8;return 32768&n?0xffff0000|n:n},a.prototype.readInt16BE=function(e,t){e>>>=0,t||x(e,2,this.length);let n=this[e+1]|this[e]<<8;return 32768&n?0xffff0000|n:n},a.prototype.readInt32LE=function(e,t){return e>>>=0,t||x(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},a.prototype.readInt32BE=function(e,t){return e>>>=0,t||x(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},a.prototype.readBigInt64LE=F(function(e){R(e>>>=0,"offset");let t=this[e],n=this[e+7];return(void 0===t||void 0===n)&&M(e,this.length-8),(BigInt(this[e+4]+256*this[e+5]+65536*this[e+6]+(n<<24))<<BigInt(32))+BigInt(t+256*this[++e]+65536*this[++e]+0x1000000*this[++e])}),a.prototype.readBigInt64BE=F(function(e){R(e>>>=0,"offset");let t=this[e],n=this[e+7];return(void 0===t||void 0===n)&&M(e,this.length-8),(BigInt((t<<24)+65536*this[++e]+256*this[++e]+this[++e])<<BigInt(32))+BigInt(0x1000000*this[++e]+65536*this[++e]+256*this[++e]+n)}),a.prototype.readFloatLE=function(e,t){return e>>>=0,t||x(e,4,this.length),i.read(this,e,!0,23,4)},a.prototype.readFloatBE=function(e,t){return e>>>=0,t||x(e,4,this.length),i.read(this,e,!1,23,4)},a.prototype.readDoubleLE=function(e,t){return e>>>=0,t||x(e,8,this.length),i.read(this,e,!0,52,8)},a.prototype.readDoubleBE=function(e,t){return e>>>=0,t||x(e,8,this.length),i.read(this,e,!1,52,8)},a.prototype.writeUintLE=a.prototype.writeUIntLE=function(e,t,n,r){if(e*=1,t>>>=0,n>>>=0,!r){let r=Math.pow(2,8*n)-1;w(this,e,t,n,r,0)}let i=1,o=0;for(this[t]=255&e;++o<n&&(i*=256);)this[t+o]=e/i&255;return t+n},a.prototype.writeUintBE=a.prototype.writeUIntBE=function(e,t,n,r){if(e*=1,t>>>=0,n>>>=0,!r){let r=Math.pow(2,8*n)-1;w(this,e,t,n,r,0)}let i=n-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+n},a.prototype.writeUint8=a.prototype.writeUInt8=function(e,t,n){return e*=1,t>>>=0,n||w(this,e,t,1,255,0),this[t]=255&e,t+1},a.prototype.writeUint16LE=a.prototype.writeUInt16LE=function(e,t,n){return e*=1,t>>>=0,n||w(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeUint16BE=a.prototype.writeUInt16BE=function(e,t,n){return e*=1,t>>>=0,n||w(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeUint32LE=a.prototype.writeUInt32LE=function(e,t,n){return e*=1,t>>>=0,n||w(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},a.prototype.writeUint32BE=a.prototype.writeUInt32BE=function(e,t,n){return e*=1,t>>>=0,n||w(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeBigUInt64LE=F(function(e,t=0){return k(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))}),a.prototype.writeBigUInt64BE=F(function(e,t=0){return S(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))}),a.prototype.writeIntLE=function(e,t,n,r){if(e*=1,t>>>=0,!r){let r=Math.pow(2,8*n-1);w(this,e,t,n,r-1,-r)}let i=0,o=1,s=0;for(this[t]=255&e;++i<n&&(o*=256);)e<0&&0===s&&0!==this[t+i-1]&&(s=1),this[t+i]=(e/o|0)-s&255;return t+n},a.prototype.writeIntBE=function(e,t,n,r){if(e*=1,t>>>=0,!r){let r=Math.pow(2,8*n-1);w(this,e,t,n,r-1,-r)}let i=n-1,o=1,s=0;for(this[t+i]=255&e;--i>=0&&(o*=256);)e<0&&0===s&&0!==this[t+i+1]&&(s=1),this[t+i]=(e/o|0)-s&255;return t+n},a.prototype.writeInt8=function(e,t,n){return e*=1,t>>>=0,n||w(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},a.prototype.writeInt16LE=function(e,t,n){return e*=1,t>>>=0,n||w(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeInt16BE=function(e,t,n){return e*=1,t>>>=0,n||w(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeInt32LE=function(e,t,n){return e*=1,t>>>=0,n||w(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},a.prototype.writeInt32BE=function(e,t,n){return e*=1,t>>>=0,n||w(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeBigInt64LE=F(function(e,t=0){return k(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),a.prototype.writeBigInt64BE=F(function(e,t=0){return S(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),a.prototype.writeFloatLE=function(e,t,n){return T(this,e,t,!0,n)},a.prototype.writeFloatBE=function(e,t,n){return T(this,e,t,!1,n)},a.prototype.writeDoubleLE=function(e,t,n){return _(this,e,t,!0,n)},a.prototype.writeDoubleBE=function(e,t,n){return _(this,e,t,!1,n)},a.prototype.copy=function(e,t,n,r){if(!a.isBuffer(e))throw TypeError("argument should be a Buffer");if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw RangeError("Index out of range");if(r<0)throw RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);let i=r-n;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,n,r):Uint8Array.prototype.set.call(e,this.subarray(n,r),t),i},a.prototype.fill=function(e,t,n,r){let i;if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),void 0!==r&&"string"!=typeof r)throw TypeError("encoding must be a string");if("string"==typeof r&&!a.isEncoding(r))throw TypeError("Unknown encoding: "+r);if(1===e.length){let t=e.charCodeAt(0);("utf8"===r&&t<128||"latin1"===r)&&(e=t)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<n)throw RangeError("Out of range index");if(n<=t)return this;if(t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0),"number"==typeof e)for(i=t;i<n;++i)this[i]=e;else{let o=a.isBuffer(e)?e:a.from(e,r),s=o.length;if(0===s)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<n-t;++i)this[i+t]=o[i%s]}return this};let C={};function A(e,t,n){C[e]=class extends n{constructor(){super(),Object.defineProperty(this,"message",{value:t.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${e}]`,this.stack,delete this.name}get code(){return e}set code(e){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:e,writable:!0})}toString(){return`${this.name} [${e}]: ${this.message}`}}}function I(e){let t="",n=e.length,r=+("-"===e[0]);for(;n>=r+4;n-=3)t=`_${e.slice(n-3,n)}${t}`;return`${e.slice(0,n)}${t}`}function P(e,t,n,r,i,o){if(e>n||e<t){let r,i="bigint"==typeof t?"n":"";throw r=o>3?0===t||t===BigInt(0)?`>= 0${i} and < 2${i} ** ${(o+1)*8}${i}`:`>= -(2${i} ** ${(o+1)*8-1}${i}) and < 2 ** ${(o+1)*8-1}${i}`:`>= ${t}${i} and <= ${n}${i}`,new C.ERR_OUT_OF_RANGE("value",r,e)}R(i,"offset"),(void 0===r[i]||void 0===r[i+o])&&M(i,r.length-(o+1))}function R(e,t){if("number"!=typeof e)throw new C.ERR_INVALID_ARG_TYPE(t,"number",e)}function M(e,t,n){if(Math.floor(e)!==e)throw R(e,n),new C.ERR_OUT_OF_RANGE(n||"offset","an integer",e);if(t<0)throw new C.ERR_BUFFER_OUT_OF_BOUNDS;throw new C.ERR_OUT_OF_RANGE(n||"offset",`>= ${+!!n} and <= ${t}`,e)}A("ERR_BUFFER_OUT_OF_BOUNDS",function(e){return e?`${e} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),A("ERR_INVALID_ARG_TYPE",function(e,t){return`The "${e}" argument must be of type number. Received type ${typeof t}`},TypeError),A("ERR_OUT_OF_RANGE",function(e,t,n){let r=`The value of "${e}" is out of range.`,i=n;return Number.isInteger(n)&&Math.abs(n)>0x100000000?i=I(String(n)):"bigint"==typeof n&&(i=String(n),(n>BigInt(2)**BigInt(32)||n<-(BigInt(2)**BigInt(32)))&&(i=I(i)),i+="n"),r+=` It must be ${t}. Received ${i}`},RangeError);let O=/[^+/0-9A-Za-z-_]/g;function D(e,t){let n;t=t||1/0;let r=e.length,i=null,o=[];for(let s=0;s<r;++s){if((n=e.charCodeAt(s))>55295&&n<57344){if(!i){if(n>56319||s+1===r){(t-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(t-=3)>-1&&o.push(239,191,189),i=n;continue}n=(i-55296<<10|n-56320)+65536}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((t-=1)<0)break;o.push(n)}else if(n<2048){if((t-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else if(n<1114112){if((t-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}else throw Error("Invalid code point")}return o}function L(e){return r.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(O,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function j(e,t,n,r){let i;for(i=0;i<r&&!(i+n>=t.length)&&!(i>=e.length);++i)t[i+n]=e[i];return i}function B(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}let N=function(){let e="0123456789abcdef",t=Array(256);for(let n=0;n<16;++n){let r=16*n;for(let i=0;i<16;++i)t[r+i]=e[n]+e[i]}return t}();function F(e){return"undefined"==typeof BigInt?z:e}function z(){throw Error("BigInt not supported")}},2712:(e,t,n)=>{"use strict";n.d(t,{N:()=>i});var r=n(2115),i=globalThis?.document?r.useLayoutEffect:()=>{}},2724:(e,t,n)=>{"use strict";n.d(t,{L:()=>s});var r=n(8716),i=n(1386),o=n(2115),s=(0,r.x)("ThreadListItemPrimitive.Archive",()=>{let e=(0,i.pK)();return(0,o.useCallback)(()=>{e.archive()},[e])})},2757:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return a},urlObjectKeys:function(){return s}});let r=n(6966)._(n(8859)),i=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:n}=e,o=e.protocol||"",s=e.pathname||"",a=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:n&&(u=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||i.test(o))&&!1!==u?(u="//"+(u||""),s&&"/"!==s[0]&&(s="/"+s)):u||(u=""),a&&"#"!==a[0]&&(a="#"+a),c&&"?"!==c[0]&&(c="?"+c),""+o+u+(s=s.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+a}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(e){return o(e)}},2900:(e,t,n)=>{"use strict";let r;n.d(t,{P:()=>oh});var i=n(2115);let o=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],s=new Set(o),a=e=>180*e/Math.PI,l=e=>c(a(Math.atan2(e[1],e[0]))),u={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:l,rotateZ:l,skewX:e=>a(Math.atan(e[1])),skewY:e=>a(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},c=e=>((e%=360)<0&&(e+=360),e),h=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),d=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),f={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:h,scaleY:d,scale:e=>(h(e)+d(e))/2,rotateX:e=>c(a(Math.atan2(e[6],e[5]))),rotateY:e=>c(a(Math.atan2(-e[2],e[0]))),rotateZ:l,rotate:l,skewX:e=>a(Math.atan(e[4])),skewY:e=>a(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function p(e){return+!!e.includes("scale")}function m(e,t){let n,r;if(!e||"none"===e)return p(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=f,r=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=u,r=t}if(!r)return p(t);let o=n[t],s=r[1].split(",").map(g);return"function"==typeof o?o(s):s[o]}function g(e){return parseFloat(e.trim())}let y=e=>t=>"string"==typeof t&&t.startsWith(e),v=y("--"),b=y("var(--"),x=e=>!!b(e)&&w.test(e.split("/*")[0].trim()),w=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function k({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}let S=(e,t,n)=>e+(t-e)*n;function E(e){return void 0===e||1===e}function T({scale:e,scaleX:t,scaleY:n}){return!E(e)||!E(t)||!E(n)}function _(e){return T(e)||C(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function C(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function A(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function I(e,t=0,n=1,r,i){e.min=A(e.min,t,n,r,i),e.max=A(e.max,t,n,r,i)}function P(e,{x:t,y:n}){I(e.x,t.translate,t.scale,t.originPoint),I(e.y,n.translate,n.scale,n.originPoint)}function R(e,t){e.min=e.min+t,e.max=e.max+t}function M(e,t,n,r,i=.5){let o=S(e.min,e.max,i);I(e,t,n,o,r)}function O(e,t){M(e.x,t.x,t.scaleX,t.scale,t.originX),M(e.y,t.y,t.scaleY,t.scale,t.originY)}function D(e,t){return k(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let L=new Set(["width","height","top","left","right","bottom",...o]),j=(e,t,n)=>n>t?t:n<e?e:n,B={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},N={...B,transform:e=>j(0,1,e)},F={...B,default:1},z=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),U=z("deg"),V=z("%"),H=z("px"),W=z("vh"),$=z("vw"),q={...V,parse:e=>V.parse(e)/100,transform:e=>V.transform(100*e)},K=e=>t=>t.test(e),Y=[B,H,V,U,$,W,{test:e=>"auto"===e,parse:e=>e}],X=e=>Y.find(K(e)),G=()=>{},J=()=>{},Z=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),Q=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ee=e=>e===B||e===H,et=new Set(["x","y","z"]),en=o.filter(e=>!et.has(e)),er={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>m(t,"x"),y:(e,{transform:t})=>m(t,"y")};er.translateX=er.x,er.translateY=er.y;let ei=e=>e,eo={},es=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ea={value:null,addProjectionMetrics:null};function el(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,s=es.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,o=!1,s=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){s.has(t)&&(c.schedule(t),e()),l++,t(a)}let c={schedule:(e,t=!1,o=!1)=>{let a=o&&i?n:r;return t&&s.add(e),a.has(e)||a.add(e),e},cancel:e=>{r.delete(e),s.delete(e)},process:e=>{if(a=e,i){o=!0;return}i=!0,[n,r]=[r,n],n.forEach(u),t&&ea.value&&ea.value.frameloop[t].push(l),l=0,n.clear(),i=!1,o&&(o=!1,c.process(e))}};return c}(o,t?n:void 0),e),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:c,update:h,preRender:d,render:f,postRender:p}=s,m=()=>{let o=eo.useManualTiming?i.timestamp:performance.now();n=!1,eo.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(o-i.timestamp,40),1)),i.timestamp=o,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),c.process(i),h.process(i),d.process(i),f.process(i),p.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(m))};return{schedule:es.reduce((t,o)=>{let a=s[o];return t[o]=(t,o=!1,s=!1)=>(!n&&(n=!0,r=!0,i.isProcessing||e(m)),a.schedule(t,o,s)),t},{}),cancel:e=>{for(let t=0;t<es.length;t++)s[es[t]].cancel(e)},state:i,steps:s}}let{schedule:eu,cancel:ec,state:eh,steps:ed}=el("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:ei,!0),ef=new Set,ep=!1,em=!1,eg=!1;function ey(){if(em){let e=Array.from(ef).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return en.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}em=!1,ep=!1,ef.forEach(e=>e.complete(eg)),ef.clear()}function ev(){ef.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(em=!0)})}class eb{constructor(e,t,n,r,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(ef.add(this),ep||(ep=!0,eu.read(ev),eu.resolveKeyframes(ey))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){let i=r?.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,o);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=o),r&&void 0===i&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),ef.delete(this)}cancel(){"scheduled"===this.state&&(ef.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let ex=e=>/^0[^.\s]+$/u.test(e),ew=e=>Math.round(1e5*e)/1e5,ek=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,eS=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,eE=(e,t)=>n=>!!("string"==typeof n&&eS.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),eT=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,o,s,a]=r.match(ek);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},e_={...B,transform:e=>Math.round(j(0,255,e))},eC={test:eE("rgb","red"),parse:eT("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+e_.transform(e)+", "+e_.transform(t)+", "+e_.transform(n)+", "+ew(N.transform(r))+")"},eA={test:eE("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:eC.transform},eI={test:eE("hsl","hue"),parse:eT("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+V.transform(ew(t))+", "+V.transform(ew(n))+", "+ew(N.transform(r))+")"},eP={test:e=>eC.test(e)||eA.test(e)||eI.test(e),parse:e=>eC.test(e)?eC.parse(e):eI.test(e)?eI.parse(e):eA.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eC.transform(e):eI.transform(e),getAnimatableNone:e=>{let t=eP.parse(e);return t.alpha=0,eP.transform(t)}},eR=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eM="number",eO="color",eD=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eL(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,s=t.replace(eD,e=>(eP.test(e)?(r.color.push(o),i.push(eO),n.push(eP.parse(e))):e.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(e)):(r.number.push(o),i.push(eM),n.push(parseFloat(e))),++o,"${}")).split("${}");return{values:n,split:s,indexes:r,types:i}}function ej(e){return eL(e).values}function eB(e){let{split:t,types:n}=eL(e),r=t.length;return e=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],void 0!==e[o]){let t=n[o];t===eM?i+=ew(e[o]):t===eO?i+=eP.transform(e[o]):i+=e[o]}return i}}let eN=e=>"number"==typeof e?0:eP.test(e)?eP.getAnimatableNone(e):e,eF={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(ek)?.length||0)+(e.match(eR)?.length||0)>0},parse:ej,createTransformer:eB,getAnimatableNone:function(e){let t=ej(e);return eB(e)(t.map(eN))}},ez=new Set(["brightness","contrast","saturate","opacity"]);function eU(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(ek)||[];if(!r)return e;let i=n.replace(r,""),o=+!!ez.has(t);return r!==n&&(o*=100),t+"("+o+i+")"}let eV=/\b([a-z-]*)\(.*?\)/gu,eH={...eF,getAnimatableNone:e=>{let t=e.match(eV);return t?t.map(eU).join(" "):e}},eW={...B,transform:Math.round},e$={borderWidth:H,borderTopWidth:H,borderRightWidth:H,borderBottomWidth:H,borderLeftWidth:H,borderRadius:H,radius:H,borderTopLeftRadius:H,borderTopRightRadius:H,borderBottomRightRadius:H,borderBottomLeftRadius:H,width:H,maxWidth:H,height:H,maxHeight:H,top:H,right:H,bottom:H,left:H,padding:H,paddingTop:H,paddingRight:H,paddingBottom:H,paddingLeft:H,margin:H,marginTop:H,marginRight:H,marginBottom:H,marginLeft:H,backgroundPositionX:H,backgroundPositionY:H,rotate:U,rotateX:U,rotateY:U,rotateZ:U,scale:F,scaleX:F,scaleY:F,scaleZ:F,skew:U,skewX:U,skewY:U,distance:H,translateX:H,translateY:H,translateZ:H,x:H,y:H,z:H,perspective:H,transformPerspective:H,opacity:N,originX:q,originY:q,originZ:H,zIndex:eW,fillOpacity:N,strokeOpacity:N,numOctaves:eW},eq={...e$,color:eP,backgroundColor:eP,outlineColor:eP,fill:eP,stroke:eP,borderColor:eP,borderTopColor:eP,borderRightColor:eP,borderBottomColor:eP,borderLeftColor:eP,filter:eH,WebkitFilter:eH},eK=e=>eq[e];function eY(e,t){let n=eK(e);return n!==eH&&(n=eF),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let eX=new Set(["auto","none","0"]);class eG extends eb{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&x(r=r.trim())){let i=function e(t,n,r=1){J(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[i,o]=function(e){let t=Q.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}(t);if(!i)return;let s=window.getComputedStyle(n).getPropertyValue(i);if(s){let e=s.trim();return Z(e)?parseFloat(e):e}return x(o)?e(o,n,r+1):o}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!L.has(n)||2!==e.length)return;let[r,i]=e,o=X(r),s=X(i);if(o!==s)if(ee(o)&&ee(s))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else er[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||ex(r)))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!eX.has(t)&&eL(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=eY(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=er[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,o=n[i];n[i]=er[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}let eJ=e=>!!(e&&e.getVelocity);function eZ(){r=void 0}let eQ={now:()=>(void 0===r&&eQ.set(eh.isProcessing||eo.useManualTiming?eh.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(eZ)}};function e0(e,t){-1===e.indexOf(t)&&e.push(t)}function e1(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class e2{constructor(){this.subscriptions=[]}add(e){return e0(this.subscriptions,e),()=>e1(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let e5={current:void 0};class e3{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=e=>{let t=eQ.now();if(this.updatedAt!==t&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty()},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=eQ.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new e2);let n=this.events[e].add(t);return"change"===e?()=>{n(),eu.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e){this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return e5.current&&e5.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=eQ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function e6(e,t){return new e3(e,t)}let e4=[...Y,eP,eF],{schedule:e9}=el(queueMicrotask,!1),e8={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},e7={};for(let e in e8)e7[e]={isEnabled:t=>e8[e].some(e=>!!t[e])};let te=()=>({translate:0,scale:1,origin:0,originPoint:0}),tt=()=>({x:te(),y:te()}),tn=()=>({min:0,max:0}),tr=()=>({x:tn(),y:tn()}),ti="undefined"!=typeof window,to={current:null},ts={current:!1},ta=new WeakMap;function tl(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function tu(e){return"string"==typeof e||Array.isArray(e)}let tc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],th=["initial",...tc];function td(e){return tl(e.animate)||th.some(t=>tu(e[t]))}function tf(e){return!!(td(e)||e.variants)}function tp(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function tm(e,t,n,r){if("function"==typeof t){let[i,o]=tp(r);t=t(void 0!==n?n:e.custom,i,o)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,o]=tp(r);t=t(void 0!==n?n:e.custom,i,o)}return t}let tg=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ty{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eb,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=eQ.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,eu.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=td(t),this.isVariantNode=tf(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==a[e]&&eJ(t)&&t.set(a[e])}}mount(e){this.current=e,ta.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),ts.current||function(){if(ts.current=!0,ti)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>to.current=e.matches;e.addEventListener("change",t),t()}else to.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||to.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),ec(this.notifyUpdate),ec(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}addChild(e){this.children.add(e),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(e)}removeChild(e){this.children.delete(e),this.enteringChildren&&this.enteringChildren.delete(e)}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=s.has(e);r&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&eu.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in e7){let t=e7[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):tr()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<tg.length;t++){let n=tg[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],o=n[r];if(eJ(i))e.addValue(r,i);else if(eJ(o))e.addValue(r,e6(i,{owner:e}));else if(o!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,e6(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=e6(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];if(null!=n){if("string"==typeof n&&(Z(n)||ex(n)))n=parseFloat(n);else{let r;r=n,!e4.find(K(r))&&eF.test(t)&&(n=eY(e,t))}this.setBaseTarget(e,eJ(n)?n.get():n)}return eJ(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=tm(this.props,n,this.presenceContext?.custom);r&&(t=r[e])}if(n&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||eJ(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new e2),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}scheduleRenderMicrotask(){e9.render(this.render)}}class tv extends ty{constructor(){super(...arguments),this.KeyframeResolver=eG}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;eJ(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}let tb=(e,t)=>t&&"number"==typeof e?t.transform(e):e,tx={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},tw=o.length;function tk(e,t,n){let{style:r,vars:i,transformOrigin:a}=e,l=!1,u=!1;for(let e in t){let n=t[e];if(s.has(e)){l=!0;continue}if(v(e)){i[e]=n;continue}{let t=tb(n,e$[e]);e.startsWith("origin")?(u=!0,a[e]=t):r[e]=t}}if(!t.transform&&(l||n?r.transform=function(e,t,n){let r="",i=!0;for(let s=0;s<tw;s++){let a=o[s],l=e[a];if(void 0===l)continue;let u=!0;if(!(u="number"==typeof l?l===+!!a.startsWith("scale"):0===parseFloat(l))||n){let e=tb(l,e$[a]);if(!u){i=!1;let t=tx[a]||a;r+=`${t}(${e}) `}n&&(t[a]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),u){let{originX:e="50%",originY:t="50%",originZ:n=0}=a;r.transformOrigin=`${e} ${t} ${n}`}}function tS(e,{style:t,vars:n},r,i){let o,s=e.style;for(o in t)s[o]=t[o];for(o in i?.applyProjectionStyles(s,r),n)s.setProperty(o,n[o])}let tE={};function tT(e,{layout:t,layoutId:n}){return s.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!tE[e]||"opacity"===e)}function t_(e,t,n){let{style:r}=e,i={};for(let o in r)(eJ(r[o])||t.style&&eJ(t.style[o])||tT(o,e)||n?.getValue(o)?.liveStyle!==void 0)&&(i[o]=r[o]);return i}class tC extends tv{constructor(){super(...arguments),this.type="html",this.renderInstance=tS}readValueFromInstance(e,t){if(s.has(t))return this.projection?.isProjecting?p(t):((e,t)=>{let{transform:n="none"}=getComputedStyle(e);return m(n,t)})(e,t);{let n=window.getComputedStyle(e),r=(v(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return D(e,t)}build(e,t,n){tk(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return t_(e,t,n)}}let tA=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tI={offset:"stroke-dashoffset",array:"stroke-dasharray"},tP={offset:"strokeDashoffset",array:"strokeDasharray"};function tR(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:o=1,pathOffset:s=0,...a},l,u,c){if(tk(e,a,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:h,style:d}=e;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=c?.transformBox??"fill-box",delete h.transformBox),void 0!==t&&(h.x=t),void 0!==n&&(h.y=n),void 0!==r&&(h.scale=r),void 0!==i&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let o=i?tI:tP;e[o.offset]=H.transform(-r);let s=H.transform(t),a=H.transform(n);e[o.array]=`${s} ${a}`}(h,i,o,s,!1)}let tM=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),tO=e=>"string"==typeof e&&"svg"===e.toLowerCase();function tD(e,t,n){let r=t_(e,t,n);for(let n in e)(eJ(e[n])||eJ(t[n]))&&(r[-1!==o.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}class tL extends tv{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=tr}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(s.has(t)){let e=eK(t);return e&&e.default||0}return t=tM.has(t)?t:tA(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return tD(e,t,n)}build(e,t,n){tR(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){for(let n in tS(e,t,void 0,r),t.attrs)e.setAttribute(tM.has(n)?n:tA(n),t.attrs[n])}mount(e){this.isSVGTag=tO(e.tagName),super.mount(e)}}let tj=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function tB(e){if("string"!=typeof e||e.includes("-"));else if(tj.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var tN=n(5155);let tF=(0,i.createContext)({}),tz=(0,i.createContext)({strict:!1}),tU=(0,i.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),tV=(0,i.createContext)({});function tH(e){return Array.isArray(e)?e.join(" "):e}let tW=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function t$(e,t,n){for(let r in t)eJ(t[r])||tT(r,n)||(e[r]=t[r])}let tq=()=>({...tW(),attrs:{}}),tK=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function tY(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||tK.has(e)}let tX=e=>!tY(e);try{!function(e){"function"==typeof e&&(tX=t=>t.startsWith("on")?!tY(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let tG=(0,i.createContext)(null);function tJ(e){return eJ(e)?e.get():e}let tZ=e=>(t,n)=>{let r=(0,i.useContext)(tV),o=(0,i.useContext)(tG),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,i){return{latestValues:function(e,t,n,r){let i={},o=r(e,{});for(let e in o)i[e]=tJ(o[e]);let{initial:s,animate:a}=e,l=td(e),u=tf(e);t&&u&&!l&&!1!==e.inherit&&(void 0===s&&(s=t.initial),void 0===a&&(a=t.animate));let c=!!n&&!1===n.initial,h=(c=c||!1===s)?a:s;if(h&&"boolean"!=typeof h&&!tl(h)){let t=Array.isArray(h)?h:[h];for(let n=0;n<t.length;n++){let r=tm(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(i[e]=t)}for(let t in e)i[t]=e[t]}}}return i}(n,r,i,e),renderState:t()}})(e,t,r,o);return n?s():function(e){let t=(0,i.useRef)(null);return null===t.current&&(t.current=e()),t.current}(s)},tQ=tZ({scrapeMotionValuesFromProps:t_,createRenderState:tW}),t0=tZ({scrapeMotionValuesFromProps:tD,createRenderState:tq}),t1=Symbol.for("motionComponentSymbol");function t2(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let t5="data-"+tA("framerAppearId"),t3=(0,i.createContext)({}),t6=ti?i.useLayoutEffect:i.useEffect;function t4(e){var t,n;let{forwardMotionProps:r=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2?arguments[2]:void 0,s=arguments.length>3?arguments[3]:void 0;o&&function(e){for(let t in e)e7[t]={...e7[t],...e[t]}}(o);let a=tB(e)?t0:tQ;function l(t,n){var o;let l,u={...(0,i.useContext)(tU),...t,layoutId:function(e){let{layoutId:t}=e,n=(0,i.useContext)(tF).id;return n&&void 0!==t?n+"-"+t:t}(t)},{isStatic:c}=u,h=function(e){let{initial:t,animate:n}=function(e,t){if(td(e)){let{initial:t,animate:n}=e;return{initial:!1===t||tu(t)?t:void 0,animate:tu(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,i.useContext)(tV));return(0,i.useMemo)(()=>({initial:t,animate:n}),[tH(t),tH(n)])}(t),d=a(t,c);if(!c&&ti){(0,i.useContext)(tz).strict;let t=function(e){let{drag:t,layout:n}=e7;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==n?void 0:n.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(u);l=t.MeasureLayout,h.visualElement=function(e,t,n,r,o){let{visualElement:s}=(0,i.useContext)(tV),a=(0,i.useContext)(tz),l=(0,i.useContext)(tG),u=(0,i.useContext)(tU).reducedMotion,c=(0,i.useRef)(null);r=r||a.renderer,!c.current&&r&&(c.current=r(e,{visualState:t,parent:s,props:n,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let h=c.current,d=(0,i.useContext)(t3);h&&!h.projection&&o&&("html"===h.type||"svg"===h.type)&&function(e,t,n,r){let{layoutId:i,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!s||a&&t2(a),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(c.current,n,o,d);let f=(0,i.useRef)(!1);(0,i.useInsertionEffect)(()=>{h&&f.current&&h.update(n,l)});let p=n[t5],m=(0,i.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return t6(()=>{h&&(f.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),h.scheduleRenderMicrotask(),m.current&&h.animationState&&h.animationState.animateChanges())}),(0,i.useEffect)(()=>{h&&(!m.current&&h.animationState&&h.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1),h.enteringChildren=void 0)}),h}(e,d,u,s,t.ProjectionNode)}return(0,tN.jsxs)(tV.Provider,{value:h,children:[l&&h.visualElement?(0,tN.jsx)(l,{visualElement:h.visualElement,...u}):null,function(e,t,n,{latestValues:r},o,s=!1){let a=(tB(e)?function(e,t,n,r){let o=(0,i.useMemo)(()=>{let n=tq();return tR(n,t,tO(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};t$(t,e.style,e),o.style={...t,...o.style}}return o}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return t$(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,i.useMemo)(()=>{let n=tW();return tk(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(t,r,o,e),l=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(tX(i)||!0===n&&tY(i)||!t&&!tY(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(t,"string"==typeof e,s),u=e!==i.Fragment?{...l,...a,ref:n}:{},{children:c}=t,h=(0,i.useMemo)(()=>eJ(c)?c.get():c,[c]);return(0,i.createElement)(e,{...u,children:h})}(e,t,(o=h.visualElement,(0,i.useCallback)(e=>{e&&d.onMount&&d.onMount(e),o&&(e?o.mount(e):o.unmount()),n&&("function"==typeof n?n(e):t2(n)&&(n.current=e))},[o])),d,c,r)]})}l.displayName="motion.".concat("string"==typeof e?e:"create(".concat(null!=(n=null!=(t=e.displayName)?t:e.name)?n:"",")"));let u=(0,i.forwardRef)(l);return u[t1]=e,u}function t9(e,t,n){let r=e.getProps();return tm(r,t,void 0!==n?n:r.custom,e)}function t8(e,t){return e?.[t]??e?.default??e}let t7=e=>Array.isArray(e);function ne(e,t){let n=e.getValue("willChange");if(eJ(n)&&n.add)return n.add(t);if(!n&&eo.WillChange){let n=new eo.WillChange("auto");e.addValue("willChange",n),n.add(t)}}function nt(e){e.duration=0,e.type}let nn=(e,t)=>n=>t(e(n)),nr=(...e)=>e.reduce(nn),ni=e=>1e3*e,no={layout:0,mainThread:0,waapi:0};function ns(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function na(e,t){return n=>n>0?t:e}let nl=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},nu=[eA,eC,eI];function nc(e){let t=nu.find(t=>t.test(e));if(G(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!t)return!1;let n=t.parse(e);return t===eI&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,o=0,s=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,a=2*n-r;i=ns(a,r,e+1/3),o=ns(a,r,e),s=ns(a,r,e-1/3)}else i=o=s=n;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:r}}(n)),n}let nh=(e,t)=>{let n=nc(e),r=nc(t);if(!n||!r)return na(e,t);let i={...n};return e=>(i.red=nl(n.red,r.red,e),i.green=nl(n.green,r.green,e),i.blue=nl(n.blue,r.blue,e),i.alpha=S(n.alpha,r.alpha,e),eC.transform(i))},nd=new Set(["none","hidden"]);function nf(e,t){return n=>S(e,t,n)}function np(e){return"number"==typeof e?nf:"string"==typeof e?x(e)?na:eP.test(e)?nh:ny:Array.isArray(e)?nm:"object"==typeof e?eP.test(e)?nh:ng:na}function nm(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>np(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function ng(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=np(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let ny=(e,t)=>{let n=eF.createTransformer(t),r=eL(e),i=eL(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?nd.has(e)&&!i.values.length||nd.has(t)&&!r.values.length?function(e,t){return nd.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):nr(nm(function(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let o=t.types[i],s=e.indexes[o][r[o]],a=e.values[s]??0;n[i]=a,r[o]++}return n}(r,i),i.values),n):(G(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),na(e,t))};function nv(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?S(e,t,n):np(e)(e,t)}let nb=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>eu.update(t,e),stop:()=>ec(t),now:()=>eh.isProcessing?eh.timestamp:eQ.now()}},nx=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=Math.round(1e4*e(t/(i-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function nw(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function nk(e,t,n){var r,i;let o=Math.max(t-5,0);return r=n-e(o),(i=t-o)?1e3/i*r:0}let nS={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function nE(e,t){return e*Math.sqrt(1-t*t)}let nT=["duration","bounce"],n_=["stiffness","damping","mass"];function nC(e,t){return t.some(t=>void 0!==e[t])}function nA(e=nS.visualDuration,t=nS.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=r,s=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:h,duration:d,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:nS.velocity,stiffness:nS.stiffness,damping:nS.damping,mass:nS.mass,isResolvedFromDuration:!1,...e};if(!nC(e,n_)&&nC(e,nT))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*j(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:nS.mass,stiffness:r,damping:i}}else{let n=function({duration:e=nS.duration,bounce:t=nS.bounce,velocity:n=nS.velocity,mass:r=nS.mass}){let i,o;G(e<=ni(nS.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let s=1-t;s=j(nS.minDamping,nS.maxDamping,s),e=j(nS.minDuration,nS.maxDuration,e/1e3),s<1?(i=t=>{let r=t*s,i=r*e;return .001-(r-n)/nE(t,s)*Math.exp(-i)},o=t=>{let r=t*s*e,o=Math.pow(s,2)*Math.pow(t,2)*e,a=Math.exp(-r),l=nE(Math.pow(t,2),s);return(r*n+n-o)*a*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),o=t=>e*e*(n-t)*Math.exp(-t*e));let a=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,o,5/e);if(e=ni(e),isNaN(a))return{stiffness:nS.stiffness,damping:nS.damping,duration:e};{let t=Math.pow(a,2)*r;return{stiffness:t,damping:2*s*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:nS.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-((r.velocity||0)/1e3)}),m=f||0,g=c/(2*Math.sqrt(u*h)),y=a-s,v=Math.sqrt(u/h)/1e3,b=5>Math.abs(y);if(i||(i=b?nS.restSpeed.granular:nS.restSpeed.default),o||(o=b?nS.restDelta.granular:nS.restDelta.default),g<1){let e=nE(v,g);n=t=>a-Math.exp(-g*v*t)*((m+g*v*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)n=e=>a-Math.exp(-v*e)*(y+(m+v*y)*e);else{let e=v*Math.sqrt(g*g-1);n=t=>{let n=Math.exp(-g*v*t),r=Math.min(e*t,300);return a-n*((m+g*v*y)*Math.sinh(r)+e*y*Math.cosh(r))/e}}let x={calculatedDuration:p&&d||null,next:e=>{let t=n(e);if(p)l.done=e>=d;else{let r=0===e?m:0;g<1&&(r=0===e?ni(m):nk(n,e,t));let s=Math.abs(a-t)<=o;l.done=Math.abs(r)<=i&&s}return l.value=l.done?a:t,l},toString:()=>{let e=Math.min(nw(x),2e4),t=nx(t=>x.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return x}function nI({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){let h,d,f=e[0],p={done:!1,value:f},m=n*t,g=f+m,y=void 0===s?g:s(g);y!==g&&(m=y-f);let v=e=>-m*Math.exp(-e/r),b=e=>y+v(e),x=e=>{let t=v(e),n=b(e);p.done=Math.abs(t)<=u,p.value=p.done?y:n},w=e=>{let t;if(t=p.value,void 0!==a&&t<a||void 0!==l&&t>l){var n;h=e,d=nA({keyframes:[p.value,(n=p.value,void 0===a?l:void 0===l||Math.abs(a-n)<Math.abs(l-n)?a:l)],velocity:nk(b,e,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:c})}};return w(0),{calculatedDuration:null,next:e=>{let t=!1;return(d||void 0!==h||(t=!0,x(e),w(e)),void 0!==h&&e>=h)?d.next(e-h):(t||x(e),p)}}}nA.applyToOptions=e=>{let t=function(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(nw(r),2e4);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:i/1e3}}(e,100,nA);return e.ease=t.ease,e.duration=ni(t.duration),e.type="keyframes",e};let nP=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function nR(e,t,n,r){return e===t&&n===r?ei:i=>0===i||1===i?i:nP(function(e,t,n,r,i){let o,s,a=0;do(o=nP(s=t+(n-t)/2,r,i)-e)>0?n=s:t=s;while(Math.abs(o)>1e-7&&++a<12);return s}(i,0,1,e,n),t,r)}let nM=nR(.42,0,1,1),nO=nR(0,0,.58,1),nD=nR(.42,0,.58,1),nL=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,nj=e=>t=>1-e(1-t),nB=nR(.33,1.53,.69,.99),nN=nj(nB),nF=nL(nN),nz=e=>(e*=2)<1?.5*nN(e):.5*(2-Math.pow(2,-10*(e-1))),nU=e=>1-Math.sin(Math.acos(e)),nV=nj(nU),nH=nL(nU),nW=e=>Array.isArray(e)&&"number"==typeof e[0],n$={linear:ei,easeIn:nM,easeInOut:nD,easeOut:nO,circIn:nU,circInOut:nH,circOut:nV,backIn:nN,backInOut:nF,backOut:nB,anticipate:nz},nq=e=>{if(nW(e)){J(4===e.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[t,n,r,i]=e;return nR(t,n,r,i)}return"string"==typeof e?(J(void 0!==n$[e],`Invalid easing type '${e}'`,"invalid-easing-type"),n$[e]):e},nK=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r};function nY({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let o=Array.isArray(r)&&"number"!=typeof r[0]?r.map(nq):nq(r),s={done:!1,value:t[0]},a=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let o=e.length;if(J(o===t.length,"Both input and output ranges must be the same length","range-length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let s=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,n){let r=[],i=n||eo.mix||nv,o=e.length-1;for(let n=0;n<o;n++){let o=i(e[n],e[n+1]);t&&(o=nr(Array.isArray(t)?t[n]||ei:t,o)),r.push(o)}return r}(t,r,i),l=a.length,u=n=>{if(s&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=nK(e[r],e[r+1],n);return a[r](i)};return n?t=>u(j(e[0],e[o-1],t)):u}((i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=nK(0,t,r);e.push(S(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(o)?o:t.map(()=>o||nD).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=a(t),s.done=t>=e,s)}}let nX=e=>null!==e;function nG(e,{repeat:t,repeatType:n="loop"},r,i=1){let o=e.filter(nX),s=i<0||t&&"loop"!==n&&t%2==1?0:o.length-1;return s&&void 0!==r?r:o[s]}let nJ={decay:nI,inertia:nI,tween:nY,keyframes:nY,spring:nA};function nZ(e){"string"==typeof e.type&&(e.type=nJ[e.type])}class nQ{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let n0=e=>e/100;class n1 extends nQ{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==eQ.now()&&this.tick(eQ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},no.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;nZ(e);let{type:t=nY,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:o=0}=e,{keyframes:s}=e,a=t||nY;a!==nY&&"number"!=typeof s[0]&&(this.mixKeyframes=nr(n0,nv(s[0],s[1])),s=[0,100]);let l=a({...e,keyframes:s});"mirror"===i&&(this.mirroredGenerator=a({...e,keyframes:[...s].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=nw(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:o,resolvedDuration:s,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:h,repeatDelay:d,type:f,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,b=n;if(c){let e=Math.min(this.currentTime,r)/s,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,c+1))%2&&("reverse"===h?(n=1-n,d&&(n-=d/s)):"mirror"===h&&(b=o)),v=j(0,1,n)*s}let x=y?{done:!1,value:u[0]}:b.next(v);i&&(x.value=i(x.value));let{done:w}=x;y||null===a||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&f!==nI&&(x.value=nG(u,this.options,m,this.speed)),p&&p(x.value),k&&this.finish(),x}then(e,t){return this.finished.then(e,t)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(e){e=ni(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(eQ.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:e=nb,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=t??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(eQ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,no.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}function n2(e){let t;return()=>(void 0===t&&(t=e()),t)}let n5=n2(()=>void 0!==window.ScrollTimeline),n3={},n6=function(e,t){let n=n2(e);return()=>n3[t]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),n4=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,n9={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:n4([0,.65,.55,1]),circOut:n4([.55,0,1,.45]),backIn:n4([.31,.01,.66,-.59]),backOut:n4([.33,1.53,.69,.99])};function n8(e){return"function"==typeof e&&"applyToOptions"in e}class n7 extends nQ{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:s,onComplete:a}=e;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=e,J("string"!=typeof e.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:e,...t}){return n8(e)&&n6()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:s="loop",ease:a="easeOut",times:l}={},u){let c={[t]:n};l&&(c.offset=l);let h=function e(t,n){if(t)return"function"==typeof t?n6()?nx(t,n):"ease-out":nW(t)?n4(t):Array.isArray(t)?t.map(t=>e(t,n)||n9.easeOut):n9[t]}(a,i);Array.isArray(h)&&(c.easing=h),ea.value&&no.waapi++;let d={delay:r,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"};u&&(d.pseudoElement=u);let f=e.animate(c,d);return ea.value&&f.finished.finally(()=>{no.waapi--}),f}(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=nG(r,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){t.startsWith("--")?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(e){this.finishedTime=null,this.animation.currentTime=ni(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&n5())?(this.animation.timeline=e,ei):t(this)}}let re={anticipate:nz,backInOut:nF,circInOut:nH};class rt extends n7{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in re&&(e.ease=re[e.ease])}(e),nZ(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...o}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let s=new n1({...o,autoplay:!1}),a=ni(this.finishedTime??this.time);t.setWithVelocity(s.sample(a-10).value,s.sample(a).value,10),s.stop()}}let rn=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eF.test(e)||"0"===e)&&!e.startsWith("url(")),rr=new Set(["opacity","clipPath","filter","transform"]),ri=n2(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class ro extends nQ{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o="loop",keyframes:s,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=eQ.now();let h={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:o,name:a,motionValue:l,element:u,...c},d=u?.KeyframeResolver||eb;this.keyframeResolver=new d(s,(e,t,n)=>this.onKeyframesResolved(e,t,h,!n),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:o,velocity:s,delay:a,isHandoff:l,onUpdate:u}=n;this.resolvedAt=eQ.now(),!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],s=rn(i,t),a=rn(o,t);return G(s===a,`You are trying to animate ${t} from "${i}" to "${o}". "${s?o:i}" is not an animatable value.`,"value-not-animatable"),!!s&&!!a&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||n8(n))&&r)}(e,i,o,s)&&((eo.instantAnimations||!a)&&u?.(nG(e,n,t)),e[0]=e[e.length-1],nt(n),n.repeat=0);let c={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...n,keyframes:e},h=!l&&function(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:o,type:s}=e;if(!(t?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=t.owner.getProps();return ri()&&n&&rr.has(n)&&("transform"!==n||!l)&&!a&&!r&&"mirror"!==i&&0!==o&&"inertia"!==s}(c)?new rt({...c,element:c.motionValue.owner.current}):new n1(c);h.finished.then(()=>this.notifyFinished()).catch(ei),this.pendingTimeline&&(this.stopTimeline=h.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=h}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eg=!0,ev(),ey(),eg=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let rs=e=>null!==e,ra={type:"spring",stiffness:500,damping:25,restSpeed:10},rl={type:"keyframes",duration:.8},ru={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rc=(e,t,n,r={},i,o)=>a=>{let l=t8(r,e)||{},u=l.delay||r.delay||0,{elapsed:c=0}=r;c-=ni(u);let h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-c,onUpdate:e=>{t.set(e),l.onUpdate&&l.onUpdate(e)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(l)&&Object.assign(h,((e,{keyframes:t})=>t.length>2?rl:s.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===t[1]?2*Math.sqrt(550):30,restSpeed:10}:ra:ru)(e,h)),h.duration&&(h.duration=ni(h.duration)),h.repeatDelay&&(h.repeatDelay=ni(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(nt(h),0===h.delay&&(d=!0)),(eo.instantAnimations||eo.skipAnimations)&&(d=!0,nt(h),h.delay=0),h.allowFlatten=!l.type&&!l.ease,d&&!o&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(rs),o=t&&"loop"!==n&&t%2==1?0:i.length-1;return i[o]}(h.keyframes,l);if(void 0!==e)return void eu.update(()=>{h.onUpdate(e),h.onComplete()})}return l.isSync?new n1(h):new ro(h)};function rh(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...a}=t;r&&(o=r);let l=[],u=i&&e.animationState&&e.animationState.getState()[i];for(let t in a){let r=e.getValue(t,e.latestValues[t]??null),i=a[t];if(void 0===i||u&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(u,t))continue;let s={delay:n,...t8(o||{},t)},c=r.get();if(void 0!==c&&!r.isAnimating&&!Array.isArray(i)&&i===c&&!s.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let n=e.props[t5];if(n){let e=window.MotionHandoffAnimation(n,t,eu);null!==e&&(s.startTime=e,h=!0)}}ne(e,t),r.start(rc(t,r,i,e.shouldReduceMotion&&L.has(t)?{type:!1}:s,e,h));let d=r.animation;d&&l.push(d)}return s&&Promise.all(l).then(()=>{eu.update(()=>{s&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=t9(e,t)||{};for(let t in i={...i,...n}){var o;let n=t7(o=i[t])?o[o.length-1]||0:o;e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,e6(n))}}(e,s)})}),l}function rd(e,t,n,r=0,i=1){let o=Array.from(e).sort((e,t)=>e.sortNodePosition(t)).indexOf(t),s=e.size,a=(s-1)*r;return"function"==typeof n?n(o,s):1===i?o*r:a-o*r}function rf(e,t,n={}){let r=t9(e,t,"exit"===n.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let o=r?()=>Promise.all(rh(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:o=0,staggerChildren:s,staggerDirection:a}=i;return function(e,t,n=0,r=0,i=0,o=1,s){let a=[];for(let l of e.variantChildren)l.notify("AnimationStart",t),a.push(rf(l,t,{...s,delay:n+("function"==typeof r?0:r)+rd(e.variantChildren,l,r,i,o)}).then(()=>l.notify("AnimationComplete",t)));return Promise.all(a)}(e,t,r,o,s,a,n)}:()=>Promise.resolve(),{when:a}=i;if(!a)return Promise.all([o(),s(n.delay)]);{let[e,t]="beforeChildren"===a?[o,s]:[s,o];return e().then(()=>t())}}function rp(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}let rm=th.length,rg=[...tc].reverse(),ry=tc.length;function rv(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rb(){return{animate:rv(!0),whileInView:rv(),whileHover:rv(),whileTap:rv(),whileDrag:rv(),whileFocus:rv(),exit:rv()}}class rx{constructor(e){this.isMounted=!1,this.node=e}update(){}}class rw extends rx{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>rf(e,t,n)));else if("string"==typeof t)r=rf(e,t,n);else{let i="function"==typeof t?t9(e,t,n.custom):t;r=Promise.all(rh(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=rb(),r=!0,i=t=>(n,r)=>{let i=t9(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function o(o){let{props:s}=e,a=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<rm;e++){let r=th[e],i=t.props[r];(tu(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},l=[],u=new Set,c={},h=1/0;for(let t=0;t<ry;t++){var d,f;let p=rg[t],m=n[p],g=void 0!==s[p]?s[p]:a[p],y=tu(g),v=p===o?m.isActive:null;!1===v&&(h=t);let b=g===a[p]&&g!==s[p]&&y;if(b&&r&&e.manuallyAnimateOnMount&&(b=!1),m.protectedKeys={...c},!m.isActive&&null===v||!g&&!m.prevProp||tl(g)||"boolean"==typeof g)continue;let x=(d=m.prevProp,"string"==typeof(f=g)?f!==d:!!Array.isArray(f)&&!rp(f,d)),w=x||p===o&&m.isActive&&!b&&y||t>h&&y,k=!1,S=Array.isArray(g)?g:[g],E=S.reduce(i(p),{});!1===v&&(E={});let{prevResolvedValues:T={}}=m,_={...T,...E},C=t=>{w=!0,u.has(t)&&(k=!0,u.delete(t)),m.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in _){let t=E[e],n=T[e];if(!c.hasOwnProperty(e))(t7(t)&&t7(n)?rp(t,n):t===n)?void 0!==t&&u.has(e)?C(e):m.protectedKeys[e]=!0:null!=t?C(e):u.add(e)}m.prevProp=g,m.prevResolvedValues=E,m.isActive&&(c={...c,...E}),r&&e.blockInitialAnimation&&(w=!1);let A=b&&x,I=!A||k;w&&I&&l.push(...S.map(t=>{let n={type:p};if("string"==typeof t&&r&&!A&&e.manuallyAnimateOnMount&&e.parent){let{parent:r}=e,i=t9(r,t);if(r.enteringChildren&&i){let{delayChildren:t}=i.transition||{};n.delay=rd(r.enteringChildren,e,t)}}return{animation:t,options:n}}))}if(u.size){let t={};if("boolean"!=typeof s.initial){let n=t9(e,Array.isArray(s.initial)?s.initial[0]:s.initial);n&&n.transition&&(t.transition=n.transition)}u.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),l.push({animation:t})}let p=!!l.length;return r&&(!1===s.initial||s.initial===s.animate)&&!e.manuallyAnimateOnMount&&(p=!1),r=!1,p?t(l):Promise.resolve()}return{animateChanges:o,setActive:function(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=o(t);for(let e in n)n[e].protectedKeys={};return i},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=rb(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();tl(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let rk=0;class rS extends rx{constructor(){super(...arguments),this.id=rk++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let rE={x:!1,y:!1};function rT(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}let r_=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function rC(e){return{point:{x:e.pageX,y:e.pageY}}}function rA(e,t,n,r){return rT(e,t,e=>r_(e)&&n(e,rC(e)),r)}function rI(e){return e.max-e.min}function rP(e,t,n,r=.5){e.origin=r,e.originPoint=S(t.min,t.max,e.origin),e.scale=rI(n)/rI(t),e.translate=S(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function rR(e,t,n,r){rP(e.x,t.x,n.x,r?r.originX:void 0),rP(e.y,t.y,n.y,r?r.originY:void 0)}function rM(e,t,n){e.min=n.min+t.min,e.max=e.min+rI(t)}function rO(e,t,n){e.min=t.min-n.min,e.max=e.min+rI(t)}function rD(e,t,n){rO(e.x,t.x,n.x),rO(e.y,t.y,n.y)}function rL(e){return[e("x"),e("y")]}let rj=({current:e})=>e?e.ownerDocument.defaultView:null,rB=(e,t)=>Math.abs(e-t);class rN{constructor(e,t,{transformPagePoint:n,contextWindow:r=window,dragSnapToOrigin:i=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rU(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(rB(e.x,t.x)**2+rB(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=eh;this.history.push({...r,timestamp:i});let{onStart:o,onMove:s}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rF(t,this.transformPagePoint),eu.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=rU("pointercancel"===e.type?this.lastMoveEventInfo:rF(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},!r_(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.distanceThreshold=o,this.contextWindow=r||window;let s=rF(rC(e),this.transformPagePoint),{point:a}=s,{timestamp:l}=eh;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,rU(s,this.history)),this.removeListeners=nr(rA(this.contextWindow,"pointermove",this.handlePointerMove),rA(this.contextWindow,"pointerup",this.handlePointerUp),rA(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),ec(this.updatePoint)}}function rF(e,t){return t?{point:t(e.point)}:e}function rz(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rU({point:e},t){return{point:e,delta:rz(e,rV(t)),offset:rz(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=rV(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>ni(.1)));)n--;if(!r)return{x:0,y:0};let o=(i.timestamp-r.timestamp)/1e3;if(0===o)return{x:0,y:0};let s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function rV(e){return e[e.length-1]}function rH(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function rW(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function r$(e,t,n){return{min:rq(e,t),max:rq(e,n)}}function rq(e,t){return"number"==typeof e?e:e[t]||0}let rK=new WeakMap;class rY{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=tr(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:t=!1,distanceThreshold:n}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let i=e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(rC(e).point)},o=(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(rE[e])return null;else return rE[e]=!0,()=>{rE[e]=!1};return rE.x||rE.y?null:(rE.x=rE.y=!0,()=>{rE.x=rE.y=!1})}(n),!this.openDragLock))return;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rL(e=>{let t=this.getAxisMotionValue(e).get()||0;if(V.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=rI(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&eu.postRender(()=>i(e,t)),ne(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},s=(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:s}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),o&&o(e,t)},a=(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},l=()=>rL(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play()),{dragSnapToOrigin:u}=this.getProps();this.panSession=new rN(e,{onSessionStart:i,onStart:o,onMove:s,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,distanceThreshold:n,contextWindow:rj(this.visualElement)})}stop(e,t){let n=e||this.latestPointerEvent,r=t||this.latestPanInfo,i=this.isDragging;if(this.cancel(),!i||!r||!n)return;let{velocity:o}=r;this.startAnimation(o);let{onDragEnd:s}=this.getProps();s&&eu.postRender(()=>s(n,r))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!rX(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?S(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?S(n,e,r.max):Math.min(e,n)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&t2(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:rH(e.x,n,i),y:rH(e.y,t,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:r$(e,"left","right"),y:r$(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&rL(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!t2(t))return!1;let r=t.current;J(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,n){let r=D(e,n),{scroll:i}=t;return i&&(R(r.x,i.offset.x),R(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),s=(e=i.layout.layoutBox,{x:rW(e.x,o.x),y:rW(e.y,o.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=k(e))}return s}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(rL(s=>{if(!rX(s,t,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[s]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return ne(this.visualElement,e),n.start(rc(e,n,0,t,this.visualElement,!1))}stopAnimation(){rL(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){rL(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){rL(t=>{let{drag:n}=this.getProps();if(!rX(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-S(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!t2(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};rL(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=rI(e),i=rI(t);return i>r?n=nK(t.min,t.max-r,e.min):r>i&&(n=nK(e.min,e.max-i,t.min)),j(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),rL(t=>{if(!rX(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set(S(i,o,r[t]))})}addListeners(){if(!this.visualElement.current)return;rK.set(this.visualElement,this);let e=rA(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();t2(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),eu.read(t);let i=rT(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(rL(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:s}}}function rX(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class rG extends rx{constructor(e){super(e),this.removeGroupControls=ei,this.removeListeners=ei,this.controls=new rY(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ei}unmount(){this.removeGroupControls(),this.removeListeners()}}let rJ=e=>(t,n)=>{e&&eu.postRender(()=>e(t,n))};class rZ extends rx{constructor(){super(...arguments),this.removePointerDownListener=ei}onPointerDown(e){this.session=new rN(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rj(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:rJ(e),onStart:rJ(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&eu.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=rA(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let rQ={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function r0(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let r1={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!H.test(e))return e;else e=parseFloat(e);let n=r0(e,t.target.x),r=r0(e,t.target.y);return`${n}% ${r}%`}},r2=!1;class r5 extends i.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;for(let e in r6)tE[e]=r6[e],v(e)&&(tE[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),r2&&i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rQ.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:o}=n;return o&&(o.isPresent=i,r2=!0,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||eu.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),e9.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r2=!0,r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function r3(e){let[t,n]=function(e=!0){let t=(0,i.useContext)(tG);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:r,register:o}=t,s=(0,i.useId)();(0,i.useEffect)(()=>{if(e)return o(s)},[e]);let a=(0,i.useCallback)(()=>e&&r&&r(s),[s,r,e]);return!n&&r?[!1,a]:[!0]}(),r=(0,i.useContext)(tF);return(0,tN.jsx)(r5,{...e,layoutGroup:r,switchLayoutGroup:(0,i.useContext)(t3),isPresent:t,safeToRemove:n})}let r6={borderRadius:{...r1,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:r1,borderTopRightRadius:r1,borderBottomLeftRadius:r1,borderBottomRightRadius:r1,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=eF.parse(e);if(r.length>5)return e;let i=eF.createTransformer(e),o=+("number"!=typeof r[0]),s=n.x.scale*t.x,a=n.y.scale*t.y;r[0+o]/=s,r[1+o]/=a;let l=S(s,a,.5);return"number"==typeof r[2+o]&&(r[2+o]/=l),"number"==typeof r[3+o]&&(r[3+o]/=l),i(r)}}};function r4(e){return"object"==typeof e&&null!==e}function r9(e){return r4(e)&&"ownerSVGElement"in e}let r8=(e,t)=>e.depth-t.depth;class r7{constructor(){this.children=[],this.isDirty=!1}add(e){e0(this.children,e),this.isDirty=!0}remove(e){e1(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(r8),this.isDirty=!1,this.children.forEach(e)}}let ie=["TopLeft","TopRight","BottomLeft","BottomRight"],it=ie.length,ir=e=>"string"==typeof e?parseFloat(e):e,ii=e=>"number"==typeof e||H.test(e);function io(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let is=il(0,.5,nV),ia=il(.5,.95,ei);function il(e,t,n){return r=>r<e?0:r>t?1:n(nK(e,t,r))}function iu(e,t){e.min=t.min,e.max=t.max}function ic(e,t){iu(e.x,t.x),iu(e.y,t.y)}function ih(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function id(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function ip(e,t,[n,r,i],o,s){!function(e,t=0,n=1,r=.5,i,o=e,s=e){if(V.test(t)&&(t=parseFloat(t),t=S(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let a=S(o.min,o.max,r);e===o&&(a-=t),e.min=id(e.min,t,n,a,i),e.max=id(e.max,t,n,a,i)}(e,t[n],t[r],t[i],t.scale,o,s)}let im=["x","scaleX","originX"],ig=["y","scaleY","originY"];function iy(e,t,n,r){ip(e.x,t,im,n?n.x:void 0,r?r.x:void 0),ip(e.y,t,ig,n?n.y:void 0,r?r.y:void 0)}function iv(e){return 0===e.translate&&1===e.scale}function ib(e){return iv(e.x)&&iv(e.y)}function ix(e,t){return e.min===t.min&&e.max===t.max}function iw(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function ik(e,t){return iw(e.x,t.x)&&iw(e.y,t.y)}function iS(e){return rI(e.x)/rI(e.y)}function iE(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class iT{constructor(){this.members=[]}add(e){e0(this.members,e),e.scheduleRender()}remove(e){if(e1(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let i_={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},iC=["","X","Y","Z"],iA=0;function iI(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function iP({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=iA++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ea.value&&(i_.nodes=i_.calculatedTargetDeltas=i_.calculatedProjections=0),this.nodes.forEach(iO),this.nodes.forEach(iz),this.nodes.forEach(iU),this.nodes.forEach(iD),ea.addProjectionMetrics&&ea.addProjectionMetrics(i_)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new r7)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new e2),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=r9(t)&&!(r9(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n,r=0,i=()=>this.root.updateBlockedByResize=!1;eu.read(()=>{r=window.innerWidth}),e(t,()=>{let e=window.innerWidth;e!==r&&(r=e,this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=eQ.now(),r=({timestamp:t})=>{let i=t-n;i>=250&&(ec(r),e(i-250))};return eu.setup(r,!0),()=>ec(r)}(i,250),rQ.hasAnimatedSinceResize&&(rQ.hasAnimatedSinceResize=!1,this.nodes.forEach(iF)))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||iK,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=i.getProps(),l=!this.targetLayout||!ik(this.targetLayout,r),u=!t&&n;if(this.options.layoutRoot||this.resumeFrom||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...t8(o,"layout"),onPlay:s,onComplete:a};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,u)}else t||iF(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ec(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(iV),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[t5];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",eu,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ij);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(iB);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(iN),this.nodes.forEach(iR),this.nodes.forEach(iM)):this.nodes.forEach(iB),this.clearAllSnapshots();let e=eQ.now();eh.delta=j(0,1e3/60,e-eh.timestamp),eh.timestamp=e,eh.isProcessing=!0,ed.update.process(eh),ed.preRender.process(eh),ed.render.process(eh),eh.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,e9.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(iL),this.sharedNodes.forEach(iH)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,eu.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){eu.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rI(this.snapshot.measuredBox.x)||rI(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=tr(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!ib(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||_(this.latestValues)||o)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),iG((t=r).x),iG(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return tr();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(iZ))){let{scroll:e}=this.root;e&&(R(t.x,e.offset.x),R(t.y,e.offset.y))}return t}removeElementScroll(e){let t=tr();if(ic(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:o}=r;r!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&ic(t,e),R(t.x,i.offset.x),R(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=tr();ic(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&O(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),_(r.latestValues)&&O(n,r.latestValues)}return _(this.latestValues)&&O(n,this.latestValues),n}removeTransform(e){let t=tr();ic(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!_(n.latestValues))continue;T(n.latestValues)&&n.updateSnapshot();let r=tr();ic(r,n.measurePageBox()),iy(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return _(this.latestValues)&&iy(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eh.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==t;if(!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=eh.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tr(),this.relativeTargetOrigin=tr(),rD(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),ic(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=tr(),this.targetWithTransforms=tr()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,s,a;this.forceRelativeParentToResolveTarget(),o=this.target,s=this.relativeTarget,a=this.relativeParent.target,rM(o.x,s.x,a.x),rM(o.y,s.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ic(this.target,this.layout.layoutBox),P(this.target,this.targetDelta)):ic(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tr(),this.relativeTargetOrigin=tr(),rD(this.relativeTargetOrigin,this.target,e.target),ic(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ea.value&&i_.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||T(this.parent.latestValues)||C(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===eh.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;ic(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,s=this.treeScale.y;!function(e,t,n,r=!1){let i,o,s=n.length;if(s){t.x=t.y=1;for(let a=0;a<s;a++){o=(i=n[a]).projectionDelta;let{visualElement:s}=i.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&O(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,P(e,o)),r&&_(i.latestValues)&&O(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=tr());let{target:a}=e;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(ih(this.prevProjectionDelta.x,this.projectionDelta.x),ih(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rR(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===s&&iE(this.projectionDelta.x,this.prevProjectionDelta.x)&&iE(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),ea.value&&i_.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=tt(),this.projectionDelta=tt(),this.projectionDeltaWithTransform=tt()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},o={...this.latestValues},s=tt();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=tr(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,h=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(iq));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(iW(s.x,e.x,r),iW(s.y,e.y,r),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,f,p,m,g;rD(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=a,g=r,i$(f.x,p.x,m.x,g),i$(f.y,p.y,m.y,g),n&&(u=this.relativeTarget,d=n,ix(u.x,d.x)&&ix(u.y,d.y))&&(this.isProjectionDirty=!1),n||(n=tr()),ic(n,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,n,r,i,o){i?(e.opacity=S(0,n.opacity??1,is(r)),e.opacityExit=S(t.opacity??1,0,ia(r))):o&&(e.opacity=S(t.opacity??1,n.opacity??1,r));for(let i=0;i<it;i++){let o=`border${ie[i]}Radius`,s=io(t,o),a=io(n,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||ii(s)===ii(a)?(e[o]=Math.max(S(ir(s),ir(a),r),0),(V.test(a)||V.test(s))&&(e[o]+="%")):e[o]=a)}(t.rotate||n.rotate)&&(e.rotate=S(t.rotate||0,n.rotate||0,r))}(o,i,this.latestValues,r,h,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ec(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=eu.update(()=>{rQ.hasAnimatedSinceResize=!0,no.layout++,this.motionValue||(this.motionValue=e6(0)),this.currentAnimation=function(e,t,n){let r=eJ(e)?e:e6(e);return r.start(rc("",r,t,n)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{no.layout--},onComplete:()=>{no.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&iJ(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||tr();let t=rI(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=rI(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}ic(t,n),O(t,i),rR(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new iT),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&iI("z",e,r,this.animationValues);for(let t=0;t<iC.length;t++)iI(`rotate${iC[t]}`,e,r,this.animationValues),iI(`skew${iC[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible){e.visibility="hidden";return}let n=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=tJ(t?.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tJ(t?.pointerEvents)||""),this.hasProjected&&!_(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1);return}e.visibility="";let i=r.animationValues||r.latestValues;this.applyTransformsToTarget();let o=function(e,t,n){let r="",i=e.x.translate/t.x,o=e.y.translate/t.y,s=n?.z||0;if((i||o||s)&&(r=`translate3d(${i}px, ${o}px, ${s}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:s,skewY:a}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),o&&(r+=`rotateY(${o}deg) `),s&&(r+=`skewX(${s}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i);n&&(o=n(i,o)),e.transform=o;let{x:s,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*a.origin}% 0`,r.animationValues?e.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:e.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,tE){if(void 0===i[t])continue;let{correct:n,applyTo:s,isCSSVariable:a}=tE[t],l="none"===o?i[t]:n(i[t],r);if(s){let t=s.length;for(let n=0;n<t;n++)e[s[n]]=l}else a?this.options.visualElement.renderState.vars[t]=l:e[t]=l}this.options.layoutId&&(e.pointerEvents=r===this?tJ(t?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(ij),this.root.sharedNodes.clear()}}}function iR(e){e.updateLayout()}function iM(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,o=t.source!==e.layout.source;"size"===i?rL(e=>{let r=o?t.measuredBox[e]:t.layoutBox[e],i=rI(r);r.min=n[e].min,r.max=r.min+i}):iJ(i,t.layoutBox,n)&&rL(r=>{let i=o?t.measuredBox[r]:t.layoutBox[r],s=rI(n[r]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+s)});let s=tt();rR(s,n,t.layoutBox);let a=tt();o?rR(a,e.applyTransform(r,!0),t.measuredBox):rR(a,n,t.layoutBox);let l=!ib(s),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:o}=r;if(i&&o){let s=tr();rD(s,t.layoutBox,i.layoutBox);let a=tr();rD(a,n,o.layoutBox),ik(s,a)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=s,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function iO(e){ea.value&&i_.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function iD(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function iL(e){e.clearSnapshot()}function ij(e){e.clearMeasurements()}function iB(e){e.isLayoutDirty=!1}function iN(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function iF(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function iz(e){e.resolveTargetDelta()}function iU(e){e.calcProjection()}function iV(e){e.resetSkewAndRotation()}function iH(e){e.removeLeadSnapshot()}function iW(e,t,n){e.translate=S(t.translate,0,n),e.scale=S(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function i$(e,t,n,r){e.min=S(t.min,n.min,r),e.max=S(t.max,n.max,r)}function iq(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let iK={duration:.45,ease:[.4,0,.1,1]},iY=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),iX=iY("applewebkit/")&&!iY("chrome/")?Math.round:ei;function iG(e){e.min=iX(e.min),e.max=iX(e.max)}function iJ(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(iS(t)-iS(n)))}function iZ(e){return e!==e.root&&e.scroll?.wasRoot}let iQ=iP({attachResizeListener:(e,t)=>rT(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),i0={current:void 0},i1=iP({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!i0.current){let e=new iQ({});e.mount(window),e.setOptions({layoutScroll:!0}),i0.current=e}return i0.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function i2(e,t){let n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,n=(void 0)??t.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function i5(e){return!("touch"===e.pointerType||rE.x||rE.y)}function i3(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&eu.postRender(()=>i(t,rC(t)))}class i6 extends rx{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=i2(e,n),s=e=>{if(!i5(e))return;let{target:n}=e,r=t(n,e);if("function"!=typeof r||!n)return;let o=e=>{i5(e)&&(r(e),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,i)};return r.forEach(e=>{e.addEventListener("pointerenter",s,i)}),o}(e,(e,t)=>(i3(this.node,t,"Start"),e=>i3(this.node,e,"End"))))}unmount(){}}class i4 extends rx{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=nr(rT(this.node.current,"focus",()=>this.onFocus()),rT(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let i9=(e,t)=>!!t&&(e===t||i9(e,t.parentElement)),i8=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),i7=new WeakSet;function oe(e){return t=>{"Enter"===t.key&&e(t)}}function ot(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}function on(e){return r_(e)&&!(rE.x||rE.y)}function or(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&eu.postRender(()=>i(t,rC(t)))}class oi extends rx{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=i2(e,n),s=e=>{let r=e.currentTarget;if(!on(e))return;i7.add(r);let o=t(r,e),s=(e,t)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),i7.has(r)&&i7.delete(r),on(e)&&"function"==typeof o&&o(e,{success:t})},a=e=>{s(e,r===window||r===document||n.useGlobalTarget||i9(r,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{(n.useGlobalTarget?window:e).addEventListener("pointerdown",s,i),r4(e)&&"offsetHeight"in e&&(e.addEventListener("focus",e=>((e,t)=>{let n=e.currentTarget;if(!n)return;let r=oe(()=>{if(i7.has(n))return;ot(n,"down");let e=oe(()=>{ot(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>ot(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)})(e,i)),i8.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),o}(e,(e,t)=>(or(this.node,t,"Start"),(e,{success:t})=>or(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let oo=new WeakMap,os=new WeakMap,oa=e=>{let t=oo.get(e.target);t&&t(e)},ol=e=>{e.forEach(oa)},ou={some:0,all:1};class oc extends rx{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:ou[r]},s=e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)};var a=this.node.current;let l=function({root:e,...t}){let n=e||document;os.has(n)||os.set(n,{});let r=os.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(ol,{root:e,...t})),r[i]}(o);return oo.set(a,s),l.observe(a),()=>{oo.delete(a),l.unobserve(a)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let oh=function(e,t){if("undefined"==typeof Proxy)return t4;let n=new Map,r=(n,r)=>t4(n,r,e,t);return new Proxy((e,t)=>r(e,t),{get:(i,o)=>"create"===o?r:(n.has(o)||n.set(o,t4(o,void 0,e,t)),n.get(o))})}({animation:{Feature:rw},exit:{Feature:rS},inView:{Feature:oc},tap:{Feature:oi},focus:{Feature:i4},hover:{Feature:i6},pan:{Feature:rZ},drag:{Feature:rG,ProjectionNode:i1,MeasureLayout:r3},layout:{ProjectionNode:i1,MeasureLayout:r3}},(e,t)=>tB(e)?new tL(t):new tC(t,{allowProjection:e!==i.Fragment}))},2907:(e,t,n)=>{"use strict";n.d(t,{v:()=>c});var r=n(3655),i=n(2115),o=n(6882),s=n(5455),a=n(7196),l=(e=>(e.Hidden="hidden",e.Floating="floating",e.Normal="normal",e))(l||{}),u=n(5155),c=(0,i.forwardRef)((e,t)=>{let{hideWhenRunning:n,autohide:i,autohideFloat:c,...h}=e,d=(e=>{let{hideWhenRunning:t,autohide:n,autohideFloat:r}=e,i=(0,s.Mp)(),l=(0,o.LN)(),u=(0,o.vd)();return(0,a.y)([i,l,u],(e,i,o)=>t&&e.isRunning?"hidden":"always"!==n&&("not-last"!==n||i.isLast)?"normal":o.isHovering?"always"===r||"single-branch"===r&&i.branchCount<=1?"floating":"normal":"hidden")})({hideWhenRunning:n,autohide:i,autohideFloat:c});return d===l.Hidden?null:(0,u.jsx)(r.sG.div,{...d===l.Floating?{"data-floating":"true"}:null,...h,ref:t})});c.displayName="ActionBarPrimitive.Root"},2910:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(7119),i=n(2115),o=n(3655),s=n(5185),a=n(5155),l=(0,i.forwardRef)((e,t)=>{let{onClick:n,disabled:i,...l}=e,u=(0,r._K)(e=>e.newThread===e.mainThreadId),c=(()=>{let e=(0,r.Ij)();return()=>{e.switchToNewThread()}})();return(0,a.jsx)(o.sG.button,{type:"button",...u?{"data-active":"true","aria-current":"true"}:null,...l,ref:t,disabled:i||!c,onClick:(0,s.mK)(n,()=>{null==c||c()})})});l.displayName="ThreadListPrimitive.New"},3052:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3180:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},3243:(e,t,n)=>{"use strict";n.d(t,{h:()=>o});var r=n(6882),i=n(5155),o=()=>{let e=(0,r.JX)(e=>e.branchCount);return(0,i.jsx)(i.Fragment,{children:e})};o.displayName="BranchPickerPrimitive.Count"},3357:(e,t,n)=>{"use strict";n.d(t,{X:()=>u});var r=n(2115),i=n(5453),o=n(1386),s=n(9935),a=n(4229),l=n(5155),u=e=>{let{runtime:t,children:n}=e,u=(e=>{let[t]=(0,r.useState)(()=>(0,i.v)(()=>e));return(0,r.useEffect)(()=>{(0,a.Q)(e),(0,s.s)(t).setState(e,!0)},[e,t]),t})(t),[c]=(0,r.useState)(()=>({useThreadListItemRuntime:u}));return(0,l.jsx)(o.vz.Provider,{value:c,children:n})}},3360:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},s=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),o=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!o)return!1;for(r in e);return void 0===r||t.call(e,r)},a=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},l=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;else if(i)return i(e,n).value}return e[n]};e.exports=function e(){var t,n,r,i,u,c,h=arguments[0],d=1,f=arguments.length,p=!1;for("boolean"==typeof h&&(p=h,h=arguments[1]||{},d=2),(null==h||"object"!=typeof h&&"function"!=typeof h)&&(h={});d<f;++d)if(t=arguments[d],null!=t)for(n in t)r=l(h,n),h!==(i=l(t,n))&&(p&&i&&(s(i)||(u=o(i)))?(u?(u=!1,c=r&&o(r)?r:[]):c=r&&s(r)?r:{},a(h,{name:n,newValue:e(p,c,i)})):void 0!==i&&a(h,{name:n,newValue:i}));return h}},3386:(e,t,n)=>{"use strict";function r(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}n.d(t,{B:()=>r})},3588:(e,t,n)=>{"use strict";n.d(t,{i3:()=>tP,UC:()=>tI,ZL:()=>tA,Kq:()=>tT,bL:()=>t_,l9:()=>tC});var r=n(2115),i=n(5185),o=n(6101),s=n(6081),a=n(8434),l=n(1285);let u=["top","right","bottom","left"],c=Math.min,h=Math.max,d=Math.round,f=Math.floor,p=e=>({x:e,y:e}),m={left:"right",right:"left",bottom:"top",top:"bottom"},g={start:"end",end:"start"};function y(e,t){return"function"==typeof e?e(t):e}function v(e){return e.split("-")[0]}function b(e){return e.split("-")[1]}function x(e){return"x"===e?"y":"x"}function w(e){return"y"===e?"height":"width"}let k=new Set(["top","bottom"]);function S(e){return k.has(v(e))?"y":"x"}function E(e){return e.replace(/start|end/g,e=>g[e])}let T=["left","right"],_=["right","left"],C=["top","bottom"],A=["bottom","top"];function I(e){return e.replace(/left|right|bottom|top/g,e=>m[e])}function P(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function R(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function M(e,t,n){let r,{reference:i,floating:o}=e,s=S(t),a=x(S(t)),l=w(a),u=v(t),c="y"===s,h=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,f=i[l]/2-o[l]/2;switch(u){case"top":r={x:h,y:i.y-o.height};break;case"bottom":r={x:h,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(b(t)){case"start":r[a]-=f*(n&&c?-1:1);break;case"end":r[a]+=f*(n&&c?-1:1)}return r}let O=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:s}=n,a=o.filter(Boolean),l=await (null==s.isRTL?void 0:s.isRTL(t)),u=await s.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:h}=M(u,r,l),d=r,f={},p=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:y,data:v,reset:b}=await m({x:c,y:h,initialPlacement:r,placement:d,strategy:i,middlewareData:f,rects:u,platform:s,elements:{reference:e,floating:t}});c=null!=g?g:c,h=null!=y?y:h,f={...f,[o]:{...f[o],...v}},b&&p<=50&&(p++,"object"==typeof b&&(b.placement&&(d=b.placement),b.rects&&(u=!0===b.rects?await s.getElementRects({reference:e,floating:t,strategy:i}):b.rects),{x:c,y:h}=M(u,d,l)),n=-1)}return{x:c,y:h,placement:d,strategy:i,middlewareData:f}};async function D(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:s,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:h="floating",altBoundary:d=!1,padding:f=0}=y(t,e),p=P(f),m=a[d?"floating"===h?"reference":"floating":h],g=R(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(m)))||n?m:m.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:l})),v="floating"===h?{x:r,y:i,width:s.floating.width,height:s.floating.height}:s.reference,b=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),x=await (null==o.isElement?void 0:o.isElement(b))&&await (null==o.getScale?void 0:o.getScale(b))||{x:1,y:1},w=R(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:b,strategy:l}):v);return{top:(g.top-w.top+p.top)/x.y,bottom:(w.bottom-g.bottom+p.bottom)/x.y,left:(g.left-w.left+p.left)/x.x,right:(w.right-g.right+p.right)/x.x}}function L(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function j(e){return u.some(t=>e[t]>=0)}let B=new Set(["left","top"]);async function N(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),s=v(n),a=b(n),l="y"===S(n),u=B.has(s)?-1:1,c=o&&l?-1:1,h=y(t,e),{mainAxis:d,crossAxis:f,alignmentAxis:p}="number"==typeof h?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return a&&"number"==typeof p&&(f="end"===a?-1*p:p),l?{x:f*c,y:d*u}:{x:d*u,y:f*c}}function F(){return"undefined"!=typeof window}function z(e){return H(e)?(e.nodeName||"").toLowerCase():"#document"}function U(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function V(e){var t;return null==(t=(H(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function H(e){return!!F()&&(e instanceof Node||e instanceof U(e).Node)}function W(e){return!!F()&&(e instanceof Element||e instanceof U(e).Element)}function $(e){return!!F()&&(e instanceof HTMLElement||e instanceof U(e).HTMLElement)}function q(e){return!!F()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof U(e).ShadowRoot)}let K=new Set(["inline","contents"]);function Y(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=eo(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!K.has(i)}let X=new Set(["table","td","th"]),G=[":popover-open",":modal"];function J(e){return G.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let Z=["transform","translate","scale","rotate","perspective"],Q=["transform","translate","scale","rotate","perspective","filter"],ee=["paint","layout","strict","content"];function et(e){let t=en(),n=W(e)?eo(e):e;return Z.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||Q.some(e=>(n.willChange||"").includes(e))||ee.some(e=>(n.contain||"").includes(e))}function en(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let er=new Set(["html","body","#document"]);function ei(e){return er.has(z(e))}function eo(e){return U(e).getComputedStyle(e)}function es(e){return W(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ea(e){if("html"===z(e))return e;let t=e.assignedSlot||e.parentNode||q(e)&&e.host||V(e);return q(t)?t.host:t}function el(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=ea(t);return ei(n)?t.ownerDocument?t.ownerDocument.body:t.body:$(n)&&Y(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),s=U(i);if(o){let e=eu(s);return t.concat(s,s.visualViewport||[],Y(i)?i:[],e&&n?el(e):[])}return t.concat(i,el(i,[],n))}function eu(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ec(e){let t=eo(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=$(e),o=i?e.offsetWidth:n,s=i?e.offsetHeight:r,a=d(n)!==o||d(r)!==s;return a&&(n=o,r=s),{width:n,height:r,$:a}}function eh(e){return W(e)?e:e.contextElement}function ed(e){let t=eh(e);if(!$(t))return p(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=ec(t),s=(o?d(n.width):n.width)/r,a=(o?d(n.height):n.height)/i;return s&&Number.isFinite(s)||(s=1),a&&Number.isFinite(a)||(a=1),{x:s,y:a}}let ef=p(0);function ep(e){let t=U(e);return en()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ef}function em(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),s=eh(e),a=p(1);t&&(r?W(r)&&(a=ed(r)):a=ed(e));let l=(void 0===(i=n)&&(i=!1),r&&(!i||r===U(s))&&i)?ep(s):p(0),u=(o.left+l.x)/a.x,c=(o.top+l.y)/a.y,h=o.width/a.x,d=o.height/a.y;if(s){let e=U(s),t=r&&W(r)?U(r):r,n=e,i=eu(n);for(;i&&r&&t!==n;){let e=ed(i),t=i.getBoundingClientRect(),r=eo(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,s=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,h*=e.x,d*=e.y,u+=o,c+=s,i=eu(n=U(i))}}return R({width:h,height:d,x:u,y:c})}function eg(e,t){let n=es(e).scrollLeft;return t?t.left+n:em(V(e)).left+n}function ey(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eg(e,r)),y:r.top+t.scrollTop}}let ev=new Set(["absolute","fixed"]);function eb(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=U(e),r=V(e),i=n.visualViewport,o=r.clientWidth,s=r.clientHeight,a=0,l=0;if(i){o=i.width,s=i.height;let e=en();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,l=i.offsetTop)}return{width:o,height:s,x:a,y:l}}(e,n);else if("document"===t)r=function(e){let t=V(e),n=es(e),r=e.ownerDocument.body,i=h(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=h(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),s=-n.scrollLeft+eg(e),a=-n.scrollTop;return"rtl"===eo(r).direction&&(s+=h(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:s,y:a}}(V(e));else if(W(t))r=function(e,t){let n=em(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=$(e)?ed(e):p(1),s=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:s,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=ep(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return R(r)}function ex(e){return"static"===eo(e).position}function ew(e,t){if(!$(e)||"fixed"===eo(e).position)return null;if(t)return t(e);let n=e.offsetParent;return V(e)===n&&(n=n.ownerDocument.body),n}function ek(e,t){var n;let r=U(e);if(J(e))return r;if(!$(e)){let t=ea(e);for(;t&&!ei(t);){if(W(t)&&!ex(t))return t;t=ea(t)}return r}let i=ew(e,t);for(;i&&(n=i,X.has(z(n)))&&ex(i);)i=ew(i,t);return i&&ei(i)&&ex(i)&&!et(i)?r:i||function(e){let t=ea(e);for(;$(t)&&!ei(t);){if(et(t))return t;if(J(t))break;t=ea(t)}return null}(e)||r}let eS=async function(e){let t=this.getOffsetParent||ek,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=$(t),i=V(t),o="fixed"===n,s=em(e,!0,o,t),a={scrollLeft:0,scrollTop:0},l=p(0);if(r||!r&&!o)if(("body"!==z(t)||Y(i))&&(a=es(t)),r){let e=em(t,!0,o,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=eg(i));o&&!r&&i&&(l.x=eg(i));let u=!i||r||o?p(0):ey(i,a);return{x:s.left+a.scrollLeft-l.x-u.x,y:s.top+a.scrollTop-l.y-u.y,width:s.width,height:s.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eE={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,s=V(r),a=!!t&&J(t.floating);if(r===s||a&&o)return n;let l={scrollLeft:0,scrollTop:0},u=p(1),c=p(0),h=$(r);if((h||!h&&!o)&&(("body"!==z(r)||Y(s))&&(l=es(r)),$(r))){let e=em(r);u=ed(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let d=!s||h||o?p(0):ey(s,l,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+c.x+d.x,y:n.y*u.y-l.scrollTop*u.y+c.y+d.y}},getDocumentElement:V,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,o=[..."clippingAncestors"===n?J(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=el(e,[],!1).filter(e=>W(e)&&"body"!==z(e)),i=null,o="fixed"===eo(e).position,s=o?ea(e):e;for(;W(s)&&!ei(s);){let t=eo(s),n=et(s);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&ev.has(i.position)||Y(s)&&!n&&function e(t,n){let r=ea(t);return!(r===n||!W(r)||ei(r))&&("fixed"===eo(r).position||e(r,n))}(e,s))?r=r.filter(e=>e!==s):i=t,s=ea(s)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=o[0],a=o.reduce((e,n)=>{let r=eb(t,n,i);return e.top=h(r.top,e.top),e.right=c(r.right,e.right),e.bottom=c(r.bottom,e.bottom),e.left=h(r.left,e.left),e},eb(t,s,i));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:ek,getElementRects:eS,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ec(e);return{width:t,height:n}},getScale:ed,isElement:W,isRTL:function(e){return"rtl"===eo(e).direction}};function eT(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e_=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:o,platform:s,elements:a,middlewareData:l}=t,{element:u,padding:d=0}=y(e,t)||{};if(null==u)return{};let f=P(d),p={x:n,y:r},m=x(S(i)),g=w(m),v=await s.getDimensions(u),k="y"===m,E=k?"clientHeight":"clientWidth",T=o.reference[g]+o.reference[m]-p[m]-o.floating[g],_=p[m]-o.reference[m],C=await (null==s.getOffsetParent?void 0:s.getOffsetParent(u)),A=C?C[E]:0;A&&await (null==s.isElement?void 0:s.isElement(C))||(A=a.floating[E]||o.floating[g]);let I=A/2-v[g]/2-1,R=c(f[k?"top":"left"],I),M=c(f[k?"bottom":"right"],I),O=A-v[g]-M,D=A/2-v[g]/2+(T/2-_/2),L=h(R,c(D,O)),j=!l.arrow&&null!=b(i)&&D!==L&&o.reference[g]/2-(D<R?R:M)-v[g]/2<0,B=j?D<R?D-R:D-O:0;return{[m]:p[m]+B,data:{[m]:L,centerOffset:D-L-B,...j&&{alignmentOffset:B}},reset:j}}});var eC=n(7650),eA="undefined"!=typeof document?r.useLayoutEffect:function(){};function eI(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eI(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!eI(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eP(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eR(e,t){let n=eP(e);return Math.round(t*n)/n}function eM(e){let t=r.useRef(e);return eA(()=>{t.current=e}),t}var eO=n(3655),eD=n(5155),eL=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eD.jsx)(eO.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eD.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eL.displayName="Arrow";var ej=n(9033),eB=n(2712),eN="Popper",[eF,ez]=(0,s.A)(eN),[eU,eV]=eF(eN),eH=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eD.jsx)(eU,{scope:t,anchor:i,onAnchorChange:o,children:n})};eH.displayName=eN;var eW="PopperAnchor",e$=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...s}=e,a=eV(eW,n),l=r.useRef(null),u=(0,o.s)(t,l),c=r.useRef(null);return r.useEffect(()=>{let e=c.current;c.current=(null==i?void 0:i.current)||l.current,e!==c.current&&a.onAnchorChange(c.current)}),i?null:(0,eD.jsx)(eO.sG.div,{...s,ref:u})});e$.displayName=eW;var eq="PopperContent",[eK,eY]=eF(eq),eX=r.forwardRef((e,t)=>{var n,i,s,a,l,u,d,p;let{__scopePopper:m,side:g="bottom",sideOffset:k=0,align:P="center",alignOffset:R=0,arrowPadding:M=0,avoidCollisions:F=!0,collisionBoundary:z=[],collisionPadding:U=0,sticky:H="partial",hideWhenDetached:W=!1,updatePositionStrategy:$="optimized",onPlaced:q,...K}=e,Y=eV(eq,m),[X,G]=r.useState(null),J=(0,o.s)(t,e=>G(e)),[Z,Q]=r.useState(null),ee=function(e){let[t,n]=r.useState(void 0);return(0,eB.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(Z),et=null!=(d=null==ee?void 0:ee.width)?d:0,en=null!=(p=null==ee?void 0:ee.height)?p:0,er="number"==typeof U?U:{top:0,right:0,bottom:0,left:0,...U},ei=Array.isArray(z)?z:[z],eo=ei.length>0,es={padding:er,boundary:ei.filter(eQ),altBoundary:eo},{refs:ea,floatingStyles:eu,placement:ec,isPositioned:ed,middlewareData:ef}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:s,floating:a}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[h,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[f,p]=r.useState(i);eI(f,i)||p(i);let[m,g]=r.useState(null),[y,v]=r.useState(null),b=r.useCallback(e=>{e!==S.current&&(S.current=e,g(e))},[]),x=r.useCallback(e=>{e!==E.current&&(E.current=e,v(e))},[]),w=s||m,k=a||y,S=r.useRef(null),E=r.useRef(null),T=r.useRef(h),_=null!=u,C=eM(u),A=eM(o),I=eM(c),P=r.useCallback(()=>{if(!S.current||!E.current)return;let e={placement:t,strategy:n,middleware:f};A.current&&(e.platform=A.current),((e,t,n)=>{let r=new Map,i={platform:eE,...n},o={...i.platform,_c:r};return O(e,t,{...i,platform:o})})(S.current,E.current,e).then(e=>{let t={...e,isPositioned:!1!==I.current};R.current&&!eI(T.current,t)&&(T.current=t,eC.flushSync(()=>{d(t)}))})},[f,t,n,A,I]);eA(()=>{!1===c&&T.current.isPositioned&&(T.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let R=r.useRef(!1);eA(()=>(R.current=!0,()=>{R.current=!1}),[]),eA(()=>{if(w&&(S.current=w),k&&(E.current=k),w&&k){if(C.current)return C.current(w,k,P);P()}},[w,k,P,C,_]);let M=r.useMemo(()=>({reference:S,floating:E,setReference:b,setFloating:x}),[b,x]),D=r.useMemo(()=>({reference:w,floating:k}),[w,k]),L=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=eR(D.floating,h.x),r=eR(D.floating,h.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...eP(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,l,D.floating,h.x,h.y]);return r.useMemo(()=>({...h,update:P,refs:M,elements:D,floatingStyles:L}),[h,P,M,D,L])}({strategy:"fixed",placement:g+("center"!==P?"-"+P:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,d=eh(e),p=o||s?[...d?el(d):[],...el(t)]:[];p.forEach(e=>{o&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let m=d&&l?function(e,t){let n,r=null,i=V(e);function o(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(a,l){void 0===a&&(a=!1),void 0===l&&(l=1),o();let u=e.getBoundingClientRect(),{left:d,top:p,width:m,height:g}=u;if(a||t(),!m||!g)return;let y=f(p),v=f(i.clientWidth-(d+m)),b={rootMargin:-y+"px "+-v+"px "+-f(i.clientHeight-(p+g))+"px "+-f(d)+"px",threshold:h(0,c(1,l))||1},x=!0;function w(t){let r=t[0].intersectionRatio;if(r!==l){if(!x)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||eT(u,e.getBoundingClientRect())||s(),x=!1}try{r=new IntersectionObserver(w,{...b,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(w,b)}r.observe(e)}(!0),o}(d,n):null,g=-1,y=null;a&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===d&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),d&&!u&&y.observe(d),y.observe(t));let v=u?em(e):null;return u&&function t(){let r=em(e);v&&!eT(v,r)&&n(),v=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach(e=>{o&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,u&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===$})},elements:{reference:Y.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:s,middlewareData:a}=t,l=await N(t,e);return s===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+l.x,y:o+l.y,data:{...l,placement:s}}}}}(e),options:[e,t]}))({mainAxis:k+en,alignmentAxis:R}),F&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:o=!0,crossAxis:s=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...l}=y(e,t),u={x:n,y:r},d=await D(t,l),f=S(v(i)),p=x(f),m=u[p],g=u[f];if(o){let e="y"===p?"top":"left",t="y"===p?"bottom":"right",n=m+d[e],r=m-d[t];m=h(n,c(m,r))}if(s){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=g+d[e],r=g-d[t];g=h(n,c(g,r))}let b=a.fn({...t,[p]:m,[f]:g});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[p]:o,[f]:s}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===H?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:s}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=y(e,t),c={x:n,y:r},h=S(i),d=x(h),f=c[d],p=c[h],m=y(a,t),g="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(l){let e="y"===d?"height":"width",t=o.reference[d]-o.floating[e]+g.mainAxis,n=o.reference[d]+o.reference[e]-g.mainAxis;f<t?f=t:f>n&&(f=n)}if(u){var b,w;let e="y"===d?"width":"height",t=B.has(v(i)),n=o.reference[h]-o.floating[e]+(t&&(null==(b=s.offset)?void 0:b[h])||0)+(t?0:g.crossAxis),r=o.reference[h]+o.reference[e]+(t?0:(null==(w=s.offset)?void 0:w[h])||0)-(t?g.crossAxis:0);p<n?p=n:p>r&&(p=r)}return{[d]:f,[h]:p}}}}(e),options:[e,t]}))():void 0,...es}),F&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,s;let{placement:a,middlewareData:l,rects:u,initialPlacement:c,platform:h,elements:d}=t,{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:m,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:P=!0,...R}=y(e,t);if(null!=(n=l.arrow)&&n.alignmentOffset)return{};let M=v(a),O=S(c),L=v(c)===c,j=await (null==h.isRTL?void 0:h.isRTL(d.floating)),B=m||(L||!P?[I(c)]:function(e){let t=I(e);return[E(e),t,E(t)]}(c)),N="none"!==k;!m&&N&&B.push(...function(e,t,n,r){let i=b(e),o=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?_:T;return t?T:_;case"left":case"right":return t?C:A;default:return[]}}(v(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(E)))),o}(c,P,k,j));let F=[c,...B],z=await D(t,R),U=[],V=(null==(r=l.flip)?void 0:r.overflows)||[];if(f&&U.push(z[M]),p){let e=function(e,t,n){void 0===n&&(n=!1);let r=b(e),i=x(S(e)),o=w(i),s="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(s=I(s)),[s,I(s)]}(a,u,j);U.push(z[e[0]],z[e[1]])}if(V=[...V,{placement:a,overflows:U}],!U.every(e=>e<=0)){let e=((null==(i=l.flip)?void 0:i.index)||0)+1,t=F[e];if(t&&("alignment"!==p||O===S(t)||V.every(e=>S(e.placement)!==O||e.overflows[0]>0)))return{data:{index:e,overflows:V},reset:{placement:t}};let n=null==(o=V.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(g){case"bestFit":{let e=null==(s=V.filter(e=>{if(N){let t=S(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:s[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...es}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,o,{placement:s,rects:a,platform:l,elements:u}=t,{apply:d=()=>{},...f}=y(e,t),p=await D(t,f),m=v(s),g=b(s),x="y"===S(s),{width:w,height:k}=a.floating;"top"===m||"bottom"===m?(i=m,o=g===(await (null==l.isRTL?void 0:l.isRTL(u.floating))?"start":"end")?"left":"right"):(o=m,i="end"===g?"top":"bottom");let E=k-p.top-p.bottom,T=w-p.left-p.right,_=c(k-p[i],E),C=c(w-p[o],T),A=!t.middlewareData.shift,I=_,P=C;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(P=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(I=E),A&&!g){let e=h(p.left,0),t=h(p.right,0),n=h(p.top,0),r=h(p.bottom,0);x?P=w-2*(0!==e||0!==t?e+t:h(p.left,p.right)):I=k-2*(0!==n||0!==r?n+r:h(p.top,p.bottom))}await d({...t,availableWidth:P,availableHeight:I});let R=await l.getDimensions(u.floating);return w!==R.width||k!==R.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...es,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:s}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(s,"px"))}}),Z&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?e_({element:n.current,padding:r}).fn(t):{}:n?e_({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:Z,padding:M}),e0({arrowWidth:et,arrowHeight:en}),W&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=y(e,t);switch(r){case"referenceHidden":{let e=L(await D(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:j(e)}}}case"escaped":{let e=L(await D(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:j(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...es})]}),[ep,eg]=e1(ec),ey=(0,ej.c)(q);(0,eB.N)(()=>{ed&&(null==ey||ey())},[ed,ey]);let ev=null==(n=ef.arrow)?void 0:n.x,eb=null==(i=ef.arrow)?void 0:i.y,ex=(null==(s=ef.arrow)?void 0:s.centerOffset)!==0,[ew,ek]=r.useState();return(0,eB.N)(()=>{X&&ek(window.getComputedStyle(X).zIndex)},[X]),(0,eD.jsx)("div",{ref:ea.setFloating,"data-radix-popper-content-wrapper":"",style:{...eu,transform:ed?eu.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ew,"--radix-popper-transform-origin":[null==(a=ef.transformOrigin)?void 0:a.x,null==(l=ef.transformOrigin)?void 0:l.y].join(" "),...(null==(u=ef.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eD.jsx)(eK,{scope:m,placedSide:ep,onArrowChange:Q,arrowX:ev,arrowY:eb,shouldHideArrow:ex,children:(0,eD.jsx)(eO.sG.div,{"data-side":ep,"data-align":eg,...K,ref:J,style:{...K.style,animation:ed?void 0:"none"}})})})});eX.displayName=eq;var eG="PopperArrow",eJ={top:"bottom",right:"left",bottom:"top",left:"right"},eZ=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eY(eG,n),o=eJ[i.placedSide];return(0,eD.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eD.jsx)(eL,{...r,ref:t,style:{...r.style,display:"block"}})})});function eQ(e){return null!==e}eZ.displayName=eG;var e0=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,s;let{placement:a,rects:l,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,h=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[f,p]=e1(a),m={start:"0%",center:"50%",end:"100%"}[p],g=(null!=(o=null==(r=u.arrow)?void 0:r.x)?o:0)+h/2,y=(null!=(s=null==(i=u.arrow)?void 0:i.y)?s:0)+d/2,v="",b="";return"bottom"===f?(v=c?m:"".concat(g,"px"),b="".concat(-d,"px")):"top"===f?(v=c?m:"".concat(g,"px"),b="".concat(l.floating.height+d,"px")):"right"===f?(v="".concat(-d,"px"),b=c?m:"".concat(y,"px")):"left"===f&&(v="".concat(l.floating.width+d,"px"),b=c?m:"".concat(y,"px")),{data:{x:v,y:b}}}});function e1(e){let[t,n="center"]=e.split("-");return[t,n]}var e2=n(4378),e5=n(8905),e3=n(9708),e6=n(5845),e4=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),e9=r.forwardRef((e,t)=>(0,eD.jsx)(eO.sG.span,{...e,ref:t,style:{...e4,...e.style}}));e9.displayName="VisuallyHidden";var[e8,e7]=(0,s.A)("Tooltip",[ez]),te=ez(),tt="TooltipProvider",tn="tooltip.open",[tr,ti]=e8(tt),to=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:i=300,disableHoverableContent:o=!1,children:s}=e,a=r.useRef(!0),l=r.useRef(!1),u=r.useRef(0);return r.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,eD.jsx)(tr,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,i)},[i]),isPointerInTransitRef:l,onPointerInTransitChange:r.useCallback(e=>{l.current=e},[]),disableHoverableContent:o,children:s})};to.displayName=tt;var ts="Tooltip",[ta,tl]=e8(ts),tu=e=>{let{__scopeTooltip:t,children:n,open:i,defaultOpen:o,onOpenChange:s,disableHoverableContent:a,delayDuration:u}=e,c=ti(ts,e.__scopeTooltip),h=te(t),[d,f]=r.useState(null),p=(0,l.B)(),m=r.useRef(0),g=null!=a?a:c.disableHoverableContent,y=null!=u?u:c.delayDuration,v=r.useRef(!1),[b,x]=(0,e6.i)({prop:i,defaultProp:null!=o&&o,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(tn))):c.onClose(),null==s||s(e)},caller:ts}),w=r.useMemo(()=>b?v.current?"delayed-open":"instant-open":"closed",[b]),k=r.useCallback(()=>{window.clearTimeout(m.current),m.current=0,v.current=!1,x(!0)},[x]),S=r.useCallback(()=>{window.clearTimeout(m.current),m.current=0,x(!1)},[x]),E=r.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{v.current=!0,x(!0),m.current=0},y)},[y,x]);return r.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,eD.jsx)(eH,{...h,children:(0,eD.jsx)(ta,{scope:t,contentId:p,open:b,stateAttribute:w,trigger:d,onTriggerChange:f,onTriggerEnter:r.useCallback(()=>{c.isOpenDelayedRef.current?E():k()},[c.isOpenDelayedRef,E,k]),onTriggerLeave:r.useCallback(()=>{g?S():(window.clearTimeout(m.current),m.current=0)},[S,g]),onOpen:k,onClose:S,disableHoverableContent:g,children:n})})};tu.displayName=ts;var tc="TooltipTrigger",th=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...s}=e,a=tl(tc,n),l=ti(tc,n),u=te(n),c=r.useRef(null),h=(0,o.s)(t,c,a.onTriggerChange),d=r.useRef(!1),f=r.useRef(!1),p=r.useCallback(()=>d.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),(0,eD.jsx)(e$,{asChild:!0,...u,children:(0,eD.jsx)(eO.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...s,ref:h,onPointerMove:(0,i.mK)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(f.current||l.isPointerInTransitRef.current||(a.onTriggerEnter(),f.current=!0))}),onPointerLeave:(0,i.mK)(e.onPointerLeave,()=>{a.onTriggerLeave(),f.current=!1}),onPointerDown:(0,i.mK)(e.onPointerDown,()=>{a.open&&a.onClose(),d.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:(0,i.mK)(e.onFocus,()=>{d.current||a.onOpen()}),onBlur:(0,i.mK)(e.onBlur,a.onClose),onClick:(0,i.mK)(e.onClick,a.onClose)})})});th.displayName=tc;var td="TooltipPortal",[tf,tp]=e8(td,{forceMount:void 0}),tm=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:i}=e,o=tl(td,t);return(0,eD.jsx)(tf,{scope:t,forceMount:n,children:(0,eD.jsx)(e5.C,{present:n||o.open,children:(0,eD.jsx)(e2.Z,{asChild:!0,container:i,children:r})})})};tm.displayName=td;var tg="TooltipContent",ty=r.forwardRef((e,t)=>{let n=tp(tg,e.__scopeTooltip),{forceMount:r=n.forceMount,side:i="top",...o}=e,s=tl(tg,e.__scopeTooltip);return(0,eD.jsx)(e5.C,{present:r||s.open,children:s.disableHoverableContent?(0,eD.jsx)(tk,{side:i,...o,ref:t}):(0,eD.jsx)(tv,{side:i,...o,ref:t})})}),tv=r.forwardRef((e,t)=>{let n=tl(tg,e.__scopeTooltip),i=ti(tg,e.__scopeTooltip),s=r.useRef(null),a=(0,o.s)(t,s),[l,u]=r.useState(null),{trigger:c,onClose:h}=n,d=s.current,{onPointerInTransitChange:f}=i,p=r.useCallback(()=>{u(null),f(!1)},[f]),m=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},i=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),i=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,r,i,o)){case o:return"left";case i:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,i),...function(e){let{top:t,right:n,bottom:r,left:i}=e;return[{x:i,y:t},{x:n,y:t},{x:n,y:r},{x:i,y:r}]}(t.getBoundingClientRect())])),f(!0)},[f]);return r.useEffect(()=>()=>p(),[p]),r.useEffect(()=>{if(c&&d){let e=e=>m(e,d),t=e=>m(e,c);return c.addEventListener("pointerleave",e),d.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),d.removeEventListener("pointerleave",t)}}},[c,d,m,p]),r.useEffect(()=>{if(l){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==d?void 0:d.contains(t)),i=!function(e,t){let{x:n,y:r}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let s=t[e],a=t[o],l=s.x,u=s.y,c=a.x,h=a.y;u>r!=h>r&&n<(c-l)*(r-u)/(h-u)+l&&(i=!i)}return i}(n,l);r?p():i&&(p(),h())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,d,l,h,p]),(0,eD.jsx)(tk,{...e,ref:a})}),[tb,tx]=e8(ts,{isInside:!1}),tw=(0,e3.Dc)("TooltipContent"),tk=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:i,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:l,...u}=e,c=tl(tg,n),h=te(n),{onClose:d}=c;return r.useEffect(()=>(document.addEventListener(tn,d),()=>document.removeEventListener(tn,d)),[d]),r.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(c.trigger))&&d()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,d]),(0,eD.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:d,children:(0,eD.jsxs)(eX,{"data-state":c.stateAttribute,...h,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,eD.jsx)(tw,{children:i}),(0,eD.jsx)(tb,{scope:n,isInside:!0,children:(0,eD.jsx)(e9,{id:c.contentId,role:"tooltip",children:o||i})})]})})});ty.displayName=tg;var tS="TooltipArrow",tE=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,i=te(n);return tx(tS,n).isInside?null:(0,eD.jsx)(eZ,{...i,...r,ref:t})});tE.displayName=tS;var tT=to,t_=tu,tC=th,tA=tm,tI=ty,tP=tE},3651:(e,t,n)=>{"use strict";n.d(t,{bm:()=>e4,UC:()=>e5,VY:()=>e6,hJ:()=>e2,ZL:()=>e1,bL:()=>e0,hE:()=>e3});var r,i,o=n(2115),s=n(5185),a=n(6101),l=n(6081),u=n(1285),c=n(5845),h=n(8434),d=n(3655),f=n(9033),p=n(5155),m="focusScope.autoFocusOnMount",g="focusScope.autoFocusOnUnmount",y={bubbles:!1,cancelable:!0},v=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:s,...l}=e,[u,c]=o.useState(null),h=(0,f.c)(i),v=(0,f.c)(s),S=o.useRef(null),E=(0,a.s)(t,e=>c(e)),T=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(T.paused||!u)return;let t=e.target;u.contains(t)?S.current=t:w(S.current,{select:!0})},t=function(e){if(T.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||w(S.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&w(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,T.paused]),o.useEffect(()=>{if(u){k.add(T);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(m,y);u.addEventListener(m,h),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(w(r,{select:t}),document.activeElement!==n)return}(b(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&w(u))}return()=>{u.removeEventListener(m,h),setTimeout(()=>{let t=new CustomEvent(g,y);u.addEventListener(g,v),u.dispatchEvent(t),t.defaultPrevented||w(null!=e?e:document.body,{select:!0}),u.removeEventListener(g,v),k.remove(T)},0)}}},[u,h,v,T]);let _=o.useCallback(e=>{if(!n&&!r||T.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,i=document.activeElement;if(t&&i){let t=e.currentTarget,[r,o]=function(e){let t=b(e);return[x(t,e),x(t.reverse(),e)]}(t);r&&o?e.shiftKey||i!==o?e.shiftKey&&i===r&&(e.preventDefault(),n&&w(o,{select:!0})):(e.preventDefault(),n&&w(r,{select:!0})):i===t&&e.preventDefault()}},[n,r,T.paused]);return(0,p.jsx)(d.sG.div,{tabIndex:-1,...l,ref:E,onKeyDown:_})});function b(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function x(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function w(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}v.displayName="FocusScope";var k=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=S(e,t)).unshift(t)},remove(t){var n;null==(n=(e=S(e,t))[0])||n.resume()}}}();function S(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var E=n(4378),T=n(8905),_=0;function C(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var A=function(){return(A=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function I(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n}Object.create;Object.create;var P=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),R="width-before-scroll-bar";function M(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var O="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,D=new WeakMap;function L(e){return e}var j=function(e){void 0===e&&(e={});var t,n,r,i=(void 0===t&&(t=L),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var i=t(e,r);return n.push(i),function(){n=n.filter(function(e){return e!==i})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var i=n;n=[],i.forEach(e),t=n}var o=function(){var n=t;t=[],n.forEach(e)},s=function(){return Promise.resolve().then(o)};s(),n={push:function(e){t.push(e),s()},filter:function(e){return t=t.filter(e),n}}}});return i.options=A({async:!0,ssr:!1},e),i}(),B=function(){},N=o.forwardRef(function(e,t){var n,r,i,s,a=o.useRef(null),l=o.useState({onScrollCapture:B,onWheelCapture:B,onTouchMoveCapture:B}),u=l[0],c=l[1],h=e.forwardProps,d=e.children,f=e.className,p=e.removeScrollBar,m=e.enabled,g=e.shards,y=e.sideCar,v=e.noRelative,b=e.noIsolation,x=e.inert,w=e.allowPinchZoom,k=e.as,S=e.gapMode,E=I(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=(n=[a,t],r=function(e){return n.forEach(function(t){return M(t,e)})},(i=(0,o.useState)(function(){return{value:null,callback:r,facade:{get current(){return i.value},set current(value){var e=i.value;e!==value&&(i.value=value,i.callback(value,e))}}}})[0]).callback=r,s=i.facade,O(function(){var e=D.get(s);if(e){var t=new Set(e),r=new Set(n),i=s.current;t.forEach(function(e){r.has(e)||M(e,null)}),r.forEach(function(e){t.has(e)||M(e,i)})}D.set(s,n)},[n]),s),_=A(A({},E),u);return o.createElement(o.Fragment,null,m&&o.createElement(y,{sideCar:j,removeScrollBar:p,shards:g,noRelative:v,noIsolation:b,inert:x,setCallbacks:c,allowPinchZoom:!!w,lockRef:a,gapMode:S}),h?o.cloneElement(o.Children.only(d),A(A({},_),{ref:T})):o.createElement(void 0===k?"div":k,A({},_,{className:f,ref:T}),d))});N.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},N.classNames={fullWidth:R,zeroRight:P};var F=function(e){var t=e.sideCar,n=I(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return o.createElement(r,A({},n))};F.isSideCarExport=!0;var z=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,s;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),s=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(s)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},U=function(){var e=z();return function(t,n){o.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},V=function(){var e=U();return function(t){return e(t.styles,t.dynamic),null}},H={left:0,top:0,right:0,gap:0},W=function(e){return parseInt(e||"",10)||0},$=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],i=t["padding"===e?"paddingRight":"marginRight"];return[W(n),W(r),W(i)]},q=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return H;var t=$(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},K=V(),Y="data-scroll-locked",X=function(e,t,n,r){var i=e.left,o=e.top,s=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(Y,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(s,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(P," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(R," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(P," .").concat(P," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(R," .").concat(R," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(Y,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},G=function(){var e=parseInt(document.body.getAttribute(Y)||"0",10);return isFinite(e)?e:0},J=function(){o.useEffect(function(){return document.body.setAttribute(Y,(G()+1).toString()),function(){var e=G()-1;e<=0?document.body.removeAttribute(Y):document.body.setAttribute(Y,e.toString())}},[])},Z=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,i=void 0===r?"margin":r;J();var s=o.useMemo(function(){return q(i)},[i]);return o.createElement(K,{styles:X(s,!t,i,n?"":"!important")})},Q=!1;if("undefined"!=typeof window)try{var ee=Object.defineProperty({},"passive",{get:function(){return Q=!0,!0}});window.addEventListener("test",ee,ee),window.removeEventListener("test",ee,ee)}catch(e){Q=!1}var et=!!Q&&{passive:!1},en=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},er=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),ei(e,r)){var i=eo(e,r);if(i[1]>i[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ei=function(e,t){return"v"===e?en(t,"overflowY"):en(t,"overflowX")},eo=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},es=function(e,t,n,r,i){var o,s=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),a=s*r,l=n.target,u=t.contains(l),c=!1,h=a>0,d=0,f=0;do{if(!l)break;var p=eo(e,l),m=p[0],g=p[1]-p[2]-s*m;(m||g)&&ei(e,l)&&(d+=g,f+=m);var y=l.parentNode;l=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return h&&(i&&1>Math.abs(d)||!i&&a>d)?c=!0:!h&&(i&&1>Math.abs(f)||!i&&-a>f)&&(c=!0),c},ea=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},el=function(e){return[e.deltaX,e.deltaY]},eu=function(e){return e&&"current"in e?e.current:e},ec=0,eh=[];let ed=(r=function(e){var t=o.useRef([]),n=o.useRef([0,0]),r=o.useRef(),i=o.useState(ec++)[0],s=o.useState(V)[0],a=o.useRef(e);o.useEffect(function(){a.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(eu),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var l=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var i,o=ea(e),s=n.current,l="deltaX"in e?e.deltaX:s[0]-o[0],u="deltaY"in e?e.deltaY:s[1]-o[1],c=e.target,h=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===h&&"range"===c.type)return!1;var d=er(h,c);if(!d)return!0;if(d?i=h:(i="v"===h?"h":"v",d=er(h,c)),!d)return!1;if(!r.current&&"changedTouches"in e&&(l||u)&&(r.current=i),!i)return!0;var f=r.current||i;return es(f,t,e,"h"===f?l:u,!0)},[]),u=o.useCallback(function(e){if(eh.length&&eh[eh.length-1]===s){var n="deltaY"in e?el(e):ea(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var i=(a.current.shards||[]).map(eu).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?l(e,i[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=o.useCallback(function(e,n,r,i){var o={name:e,delta:n,target:r,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),h=o.useCallback(function(e){n.current=ea(e),r.current=void 0},[]),d=o.useCallback(function(t){c(t.type,el(t),t.target,l(t,e.lockRef.current))},[]),f=o.useCallback(function(t){c(t.type,ea(t),t.target,l(t,e.lockRef.current))},[]);o.useEffect(function(){return eh.push(s),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:f}),document.addEventListener("wheel",u,et),document.addEventListener("touchmove",u,et),document.addEventListener("touchstart",h,et),function(){eh=eh.filter(function(e){return e!==s}),document.removeEventListener("wheel",u,et),document.removeEventListener("touchmove",u,et),document.removeEventListener("touchstart",h,et)}},[]);var p=e.removeScrollBar,m=e.inert;return o.createElement(o.Fragment,null,m?o.createElement(s,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,p?o.createElement(Z,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},j.useMedium(r),F);var ef=o.forwardRef(function(e,t){return o.createElement(N,A({},e,{ref:t,sideCar:ed}))});ef.classNames=N.classNames;var ep=new WeakMap,em=new WeakMap,eg={},ey=0,ev=function(e){return e&&(e.host||ev(e.parentNode))},eb=function(e,t,n,r){var i=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=ev(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eg[n]||(eg[n]=new WeakMap);var o=eg[n],s=[],a=new Set,l=new Set(i),u=function(e){!e||a.has(e)||(a.add(e),u(e.parentNode))};i.forEach(u);var c=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))c(e);else try{var t=e.getAttribute(r),i=null!==t&&"false"!==t,l=(ep.get(e)||0)+1,u=(o.get(e)||0)+1;ep.set(e,l),o.set(e,u),s.push(e),1===l&&i&&em.set(e,!0),1===u&&e.setAttribute(n,"true"),i||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),a.clear(),ey++,function(){s.forEach(function(e){var t=ep.get(e)-1,i=o.get(e)-1;ep.set(e,t),o.set(e,i),t||(em.has(e)||e.removeAttribute(r),em.delete(e)),i||e.removeAttribute(n)}),--ey||(ep=new WeakMap,ep=new WeakMap,em=new WeakMap,eg={})}},ex=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),i=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return i?(r.push.apply(r,Array.from(i.querySelectorAll("[aria-live], script"))),eb(r,i,n,"aria-hidden")):function(){return null}},ew=n(9708),ek="Dialog",[eS,eE]=(0,l.A)(ek),[eT,e_]=eS(ek),eC=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:i,onOpenChange:s,modal:a=!0}=e,l=o.useRef(null),h=o.useRef(null),[d,f]=(0,c.i)({prop:r,defaultProp:null!=i&&i,onChange:s,caller:ek});return(0,p.jsx)(eT,{scope:t,triggerRef:l,contentRef:h,contentId:(0,u.B)(),titleId:(0,u.B)(),descriptionId:(0,u.B)(),open:d,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:a,children:n})};eC.displayName=ek;var eA="DialogTrigger";o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=e_(eA,n),o=(0,a.s)(t,i.triggerRef);return(0,p.jsx)(d.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":eY(i.open),...r,ref:o,onClick:(0,s.mK)(e.onClick,i.onOpenToggle)})}).displayName=eA;var eI="DialogPortal",[eP,eR]=eS(eI,{forceMount:void 0}),eM=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:i}=e,s=e_(eI,t);return(0,p.jsx)(eP,{scope:t,forceMount:n,children:o.Children.map(r,e=>(0,p.jsx)(T.C,{present:n||s.open,children:(0,p.jsx)(E.Z,{asChild:!0,container:i,children:e})}))})};eM.displayName=eI;var eO="DialogOverlay",eD=o.forwardRef((e,t)=>{let n=eR(eO,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=e_(eO,e.__scopeDialog);return o.modal?(0,p.jsx)(T.C,{present:r||o.open,children:(0,p.jsx)(ej,{...i,ref:t})}):null});eD.displayName=eO;var eL=(0,ew.TL)("DialogOverlay.RemoveScroll"),ej=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=e_(eO,n);return(0,p.jsx)(ef,{as:eL,allowPinchZoom:!0,shards:[i.contentRef],children:(0,p.jsx)(d.sG.div,{"data-state":eY(i.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eB="DialogContent",eN=o.forwardRef((e,t)=>{let n=eR(eB,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=e_(eB,e.__scopeDialog);return(0,p.jsx)(T.C,{present:r||o.open,children:o.modal?(0,p.jsx)(eF,{...i,ref:t}):(0,p.jsx)(ez,{...i,ref:t})})});eN.displayName=eB;var eF=o.forwardRef((e,t)=>{let n=e_(eB,e.__scopeDialog),r=o.useRef(null),i=(0,a.s)(t,n.contentRef,r);return o.useEffect(()=>{let e=r.current;if(e)return ex(e)},[]),(0,p.jsx)(eU,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.mK)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,s.mK)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,s.mK)(e.onFocusOutside,e=>e.preventDefault())})}),ez=o.forwardRef((e,t)=>{let n=e_(eB,e.__scopeDialog),r=o.useRef(!1),i=o.useRef(!1);return(0,p.jsx)(eU,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,s;null==(o=e.onCloseAutoFocus)||o.call(e,t),t.defaultPrevented||(r.current||null==(s=n.triggerRef.current)||s.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{var o,s;null==(o=e.onInteractOutside)||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let a=t.target;(null==(s=n.triggerRef.current)?void 0:s.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),eU=o.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:s,...l}=e,u=e_(eB,n),c=o.useRef(null),d=(0,a.s)(t,c);return o.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:C()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:C()),_++,()=>{1===_&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),_--}},[]),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(v,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:s,children:(0,p.jsx)(h.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":eY(u.open),...l,ref:d,onDismiss:()=>u.onOpenChange(!1)})}),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(eZ,{titleId:u.titleId}),(0,p.jsx)(eQ,{contentRef:c,descriptionId:u.descriptionId})]})]})}),eV="DialogTitle",eH=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=e_(eV,n);return(0,p.jsx)(d.sG.h2,{id:i.titleId,...r,ref:t})});eH.displayName=eV;var eW="DialogDescription",e$=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=e_(eW,n);return(0,p.jsx)(d.sG.p,{id:i.descriptionId,...r,ref:t})});e$.displayName=eW;var eq="DialogClose",eK=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=e_(eq,n);return(0,p.jsx)(d.sG.button,{type:"button",...r,ref:t,onClick:(0,s.mK)(e.onClick,()=>i.onOpenChange(!1))})});function eY(e){return e?"open":"closed"}eK.displayName=eq;var eX="DialogTitleWarning",[eG,eJ]=(0,l.q)(eX,{contentName:eB,titleName:eV,docsSlug:"dialog"}),eZ=e=>{let{titleId:t}=e,n=eJ(eX),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return o.useEffect(()=>{t&&(document.getElementById(t)||console.error(r))},[r,t]),null},eQ=e=>{let{contentRef:t,descriptionId:n}=e,r=eJ("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return o.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,t,n]),null},e0=eC,e1=eM,e2=eD,e5=eN,e3=eH,e6=e$,e4=eK},3655:(e,t,n)=>{"use strict";n.d(t,{hO:()=>l,sG:()=>a});var r=n(2115),i=n(7650),o=n(9708),s=n(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.TL)(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},3724:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(7924)),i=n(1300);function o(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,i.camelCase)(e,t)]=r)}),n}o.default=o,e.exports=o},3793:(e,t,n)=>{"use strict";n.d(t,{R:()=>i});var r=n(5455),i=e=>{let{children:t}=e;return(0,r._W)(e=>0===e.messages.length)?t:null};i.displayName="ThreadPrimitive.Empty"},3829:(e,t,n)=>{"use strict";n.d(t,{$7:()=>a,U_:()=>l,fv:()=>s});var r=n(2115),i=n(9912),o=n(5155),s=(0,r.createContext)(null),a=()=>null!==(0,r.useContext)(s),l=(0,r.memo)(e=>{let{children:t,...n}=e;return(0,o.jsx)(s.Provider,{value:n,children:t})},i.x1)},3904:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},3932:(e,t,n)=>{"use strict";n.d(t,{T:()=>o});var r=n(1386),i=n(5155),o=e=>{let{fallback:t}=e,n=(0,r.Xi)(e=>e.title);return(0,i.jsx)(i.Fragment,{children:n||t})};o.displayName="ThreadListItemPrimitive.Title"},4093:(e,t,n)=>{"use strict";function r(){}function i(){}n.d(t,{HB:()=>i,ok:()=>r})},4229:(e,t,n)=>{"use strict";n.d(t,{Q:()=>r});var r=e=>{e.__isBound||(e.__internal_bindMethods?.(),e.__isBound=!0)}},4354:(e,t,n)=>{"use strict";n.d(t,{h:()=>i});var r=n(2115);function i(e,t){return function(n){let i=(0,r.useContext)(e);if(!(null==n?void 0:n.optional)&&!i)throw Error("This component must be used within ".concat(t,"."));return i}}},4357:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},4378:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(2115),i=n(7650),o=n(3655),s=n(2712),a=n(5155),l=r.forwardRef((e,t)=>{var n,l;let{container:u,...c}=e,[h,d]=r.useState(!1);(0,s.N)(()=>d(!0),[]);let f=u||h&&(null==(l=globalThis)||null==(n=l.document)?void 0:n.body);return f?i.createPortal((0,a.jsx)(o.sG.div,{...c,ref:t}),f):null});l.displayName="Portal"},4392:(e,t,n)=>{"use strict";n.d(t,{d:()=>i});let r={};function i(e,t){let n=t||r;return o(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function o(e,t,n){var r;if((r=e)&&"object"==typeof r){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return s(e.children,t,n)}return Array.isArray(e)?s(e,t,n):""}function s(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=o(e[i],t,n);return r.join("")}},4416:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4581:(e,t,n)=>{"use strict";n.d(t,{N:()=>i});var r=n(2556);function i(e,t,n,i){let o=i?i-1:1/0,s=0;return function(i){return(0,r.On)(i)?(e.enter(n),function i(a){return(0,r.On)(a)&&s++<o?(e.consume(a),i):(e.exit(n),t(a))}(i)):t(i)}}},4616:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4773:(e,t,n)=>{"use strict";n.d(t,{s:()=>o});var r=n(6882),i=n(7196),o=e=>{let{children:t,...n}=e;return(e=>{let t=(0,r.LN)(),n=(0,r.vd)();return(0,i.y)([t,n],(t,n)=>{var r;let{role:i,attachments:o,content:s,branchCount:a,isLast:l,speech:u,submittedFeedback:c}=t,{isCopied:h,isHovering:d}=n;return(!0!==e.hasBranches||!(a<2))&&(!e.user||"user"===i)&&(!e.assistant||"assistant"===i)&&(!e.system||"system"===i)&&(!0!==e.lastOrHover||!!d||!!l)&&(void 0===e.last||e.last===l)&&(!0!==e.copied||!!h)&&(!1!==e.copied||!h)&&(!0!==e.speaking||null!=u)&&(!1!==e.speaking||null==u)&&(!0!==e.hasAttachments||"user"===i&&!!o.length)&&(!1!==e.hasAttachments||"user"!==i||!o.length)&&(!0!==e.hasContent||0!==s.length)&&(!1!==e.hasContent||!(s.length>0))&&(void 0===e.submittedFeedback||(null!=(r=null==c?void 0:c.type)?r:null)===e.submittedFeedback)&&!0})})(n)?t:null};o.displayName="MessagePrimitive.If"},4823:(e,t,n)=>{"use strict";function r(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,i=n.indexOf(t);for(;-1!==i;)r++,i=n.indexOf(t,i+t.length);return r}n.d(t,{A:()=>eO});var i=n(4093),o=n(2556),s=n(1922),a=n(7915);let l="phrasing",u=["autolink","link","image","label"];function c(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function h(e){this.config.enter.autolinkProtocol.call(this,e)}function d(e){this.config.exit.autolinkProtocol.call(this,e)}function f(e){this.config.exit.data.call(this,e);let t=this.stack[this.stack.length-1];(0,i.ok)("link"===t.type),t.url="http://"+this.sliceSerialize(e)}function p(e){this.config.exit.autolinkEmail.call(this,e)}function m(e){this.exit(e)}function g(e){!function(e,t,n){let r=(0,a.C)((n||{}).ignore||[]),i=function(e){let t=[];if(!Array.isArray(e))throw TypeError("Expected find and replace tuple or list of tuples");let n=!e[0]||Array.isArray(e[0])?e:[e],r=-1;for(;++r<n.length;){var i;let e=n[r];t.push(["string"==typeof(i=e[0])?RegExp(function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(i),"g"):i,function(e){return"function"==typeof e?e:function(){return e}}(e[1])])}return t}(t),o=-1;for(;++o<i.length;)(0,s.VG)(e,"text",l);function l(e,t){let n,s=-1;for(;++s<t.length;){let e=t[s],i=n?n.children:void 0;if(r(e,i?i.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){let n=t[t.length-1],r=i[o][0],s=i[o][1],a=0,l=n.children.indexOf(e),u=!1,c=[];r.lastIndex=0;let h=r.exec(e.value);for(;h;){let n=h.index,i={index:h.index,input:h.input,stack:[...t,e]},o=s(...h,i);if("string"==typeof o&&(o=o.length>0?{type:"text",value:o}:void 0),!1===o?r.lastIndex=n+1:(a!==n&&c.push({type:"text",value:e.value.slice(a,n)}),Array.isArray(o)?c.push(...o):o&&c.push(o),a=n+h[0].length,u=!0),!r.global)break;h=r.exec(e.value)}return u?(a<e.value.length&&c.push({type:"text",value:e.value.slice(a)}),n.children.splice(l,1,...c)):c=[e],l+c.length}(e,t)}}(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,y],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,v]],{ignore:["link","linkReference"]})}function y(e,t,n,i,o){let s="";if(!b(o)||(/^w/i.test(t)&&(n=t+n,t="",s="http://"),!function(e){let t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}(n)))return!1;let a=function(e){let t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],i=n.indexOf(")"),o=r(e,"("),s=r(e,")");for(;-1!==i&&o>s;)e+=n.slice(0,i+1),i=(n=n.slice(i+1)).indexOf(")"),s++;return[e,n]}(n+i);if(!a[0])return!1;let l={type:"link",title:null,url:s+t+a[0],children:[{type:"text",value:t+a[0]}]};return a[1]?[l,{type:"text",value:a[1]}]:l}function v(e,t,n,r){return!(!b(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function b(e,t){let n=e.input.charCodeAt(e.index-1);return(0===e.index||(0,o.Ny)(n)||(0,o.es)(n))&&(!t||47!==n)}var x=n(3386);function w(){this.buffer()}function k(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function S(){this.buffer()}function E(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function T(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteReference"===n.type),n.identifier=(0,x.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function _(e){this.exit(e)}function C(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteDefinition"===n.type),n.identifier=(0,x.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function A(e){this.exit(e)}function I(e,t,n,r){let i=n.createTracker(r),o=i.move("[^"),s=n.enter("footnoteReference"),a=n.enter("reference");return o+=i.move(n.safe(n.associationId(e),{after:"]",before:o})),a(),s(),o+=i.move("]")}function P(e,t,n){return 0===t?e:R(e,t,n)}function R(e,t,n){return(n?"":"    ")+e}I.peek=function(){return"["};let M=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function O(e){this.enter({type:"delete",children:[]},e)}function D(e){this.exit(e)}function L(e,t,n,r){let i=n.createTracker(r),o=n.enter("strikethrough"),s=i.move("~~");return s+=n.containerPhrasing(e,{...i.current(),before:s,after:"~"}),s+=i.move("~~"),o(),s}function j(e){return e.length}function B(e){let t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:114*(82===t||114===t)}L.peek=function(){return"~"};var N=n(9535);n(8428);n(4392);function F(e,t,n){let r=e.value||"",i="`",o=-1;for(;RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++o<n.unsafe.length;){let e,t=n.unsafe[o],i=n.compilePattern(t);if(t.atBreak)for(;e=i.exec(r);){let t=e.index;10===r.charCodeAt(t)&&13===r.charCodeAt(t-1)&&t--,r=r.slice(0,t)+" "+r.slice(e.index+1)}}return i+r+i}F.peek=function(){return"`"};(0,a.C)(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);let z={inlineCode:F,listItem:function(e,t,n,r){let i=function(e){let t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n),o=n.bulletCurrent||function(e){let t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}(n);t&&"list"===t.type&&t.ordered&&(o=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+o);let s=o.length+1;("tab"===i||"mixed"===i&&(t&&"list"===t.type&&t.spread||e.spread))&&(s=4*Math.ceil(s/4));let a=n.createTracker(r);a.move(o+" ".repeat(s-o.length)),a.shift(s);let l=n.enter("listItem"),u=n.indentLines(n.containerFlow(e,a.current()),function(e,t,n){return t?(n?"":" ".repeat(s))+e:(n?o:o+" ".repeat(s-o.length))+e});return l(),u}};function U(e){let t=e._align;(0,i.ok)(t,"expected `_align` on table"),this.enter({type:"table",align:t.map(function(e){return"none"===e?null:e}),children:[]},e),this.data.inTable=!0}function V(e){this.exit(e),this.data.inTable=void 0}function H(e){this.enter({type:"tableRow",children:[]},e)}function W(e){this.exit(e)}function $(e){this.enter({type:"tableCell",children:[]},e)}function q(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,K));let n=this.stack[this.stack.length-1];(0,i.ok)("inlineCode"===n.type),n.value=t,this.exit(e)}function K(e,t){return"|"===t?t:e}function Y(e){let t=this.stack[this.stack.length-2];(0,i.ok)("listItem"===t.type),t.checked="taskListCheckValueChecked"===e.type}function X(e){let t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){let e=this.stack[this.stack.length-1];(0,i.ok)("paragraph"===e.type);let n=e.children[0];if(n&&"text"===n.type){let r,i=t.children,o=-1;for(;++o<i.length;){let e=i[o];if("paragraph"===e.type){r=e;break}}r===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function G(e,t,n,r){let i=e.children[0],o="boolean"==typeof e.checked&&i&&"paragraph"===i.type,s="["+(e.checked?"x":" ")+"] ",a=n.createTracker(r);o&&a.move(s);let l=z.listItem(e,t,n,{...r,...a.current()});return o&&(l=l.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+s})),l}var J=n(9381);let Z={tokenize:function(e,t,n){let r=0;return function t(o){return(87===o||119===o)&&r<3?(r++,e.consume(o),t):46===o&&3===r?(e.consume(o),i):n(o)};function i(e){return null===e?n(e):t(e)}},partial:!0},Q={tokenize:function(e,t,n){let r,i,s;return a;function a(t){return 46===t||95===t?e.check(et,u,l)(t):null===t||(0,o.Ee)(t)||(0,o.Ny)(t)||45!==t&&(0,o.es)(t)?u(t):(s=!0,e.consume(t),a)}function l(t){return 95===t?r=!0:(i=r,r=void 0),e.consume(t),a}function u(e){return i||r||!s?n(e):t(e)}},partial:!0},ee={tokenize:function(e,t){let n=0,r=0;return i;function i(a){return 40===a?(n++,e.consume(a),i):41===a&&r<n?s(a):33===a||34===a||38===a||39===a||41===a||42===a||44===a||46===a||58===a||59===a||60===a||63===a||93===a||95===a||126===a?e.check(et,t,s)(a):null===a||(0,o.Ee)(a)||(0,o.Ny)(a)?t(a):(e.consume(a),i)}function s(t){return 41===t&&r++,e.consume(t),i}},partial:!0},et={tokenize:function(e,t,n){return r;function r(a){return 33===a||34===a||39===a||41===a||42===a||44===a||46===a||58===a||59===a||63===a||95===a||126===a?(e.consume(a),r):38===a?(e.consume(a),s):93===a?(e.consume(a),i):60===a||null===a||(0,o.Ee)(a)||(0,o.Ny)(a)?t(a):n(a)}function i(e){return null===e||40===e||91===e||(0,o.Ee)(e)||(0,o.Ny)(e)?t(e):r(e)}function s(t){return(0,o.CW)(t)?function t(i){return 59===i?(e.consume(i),r):(0,o.CW)(i)?(e.consume(i),t):n(i)}(t):n(t)}},partial:!0},en={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return(0,o.lV)(e)?n(e):t(e)}},partial:!0},er={name:"wwwAutolink",tokenize:function(e,t,n){let r=this;return function(t){return 87!==t&&119!==t||!el.call(r,r.previous)||ed(r.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(Z,e.attempt(Q,e.attempt(ee,i),n),n)(t))};function i(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:el},ei={name:"protocolAutolink",tokenize:function(e,t,n){let r=this,i="",s=!1;return function(t){return(72===t||104===t)&&eu.call(r,r.previous)&&!ed(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),i+=String.fromCodePoint(t),e.consume(t),a):n(t)};function a(t){if((0,o.CW)(t)&&i.length<5)return i+=String.fromCodePoint(t),e.consume(t),a;if(58===t){let n=i.toLowerCase();if("http"===n||"https"===n)return e.consume(t),l}return n(t)}function l(t){return 47===t?(e.consume(t),s)?u:(s=!0,l):n(t)}function u(t){return null===t||(0,o.JQ)(t)||(0,o.Ee)(t)||(0,o.Ny)(t)||(0,o.es)(t)?n(t):e.attempt(Q,e.attempt(ee,c),n)(t)}function c(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:eu},eo={name:"emailAutolink",tokenize:function(e,t,n){let r,i,s=this;return function(t){return!eh(t)||!ec.call(s,s.previous)||ed(s.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),function t(r){return eh(r)?(e.consume(r),t):64===r?(e.consume(r),a):n(r)}(t))};function a(t){return 46===t?e.check(en,u,l)(t):45===t||95===t||(0,o.lV)(t)?(i=!0,e.consume(t),a):u(t)}function l(t){return e.consume(t),r=!0,a}function u(a){return i&&r&&(0,o.CW)(s.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(a)):n(a)}},previous:ec},es={},ea=48;for(;ea<123;)es[ea]=eo,58==++ea?ea=65:91===ea&&(ea=97);function el(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||(0,o.Ee)(e)}function eu(e){return!(0,o.CW)(e)}function ec(e){return!(47===e||eh(e))}function eh(e){return 43===e||45===e||46===e||95===e||(0,o.lV)(e)}function ed(e){let t=e.length,n=!1;for(;t--;){let r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}es[43]=eo,es[45]=eo,es[46]=eo,es[95]=eo,es[72]=[eo,ei],es[104]=[eo,ei],es[87]=[eo,er],es[119]=[eo,er];var ef=n(5333),ep=n(4581);let em={tokenize:function(e,t,n){let r=this;return(0,ep.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"gfmFootnoteDefinitionIndent"===i[1].type&&4===i[2].sliceSerialize(i[1],!0).length?t(e):n(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function eg(e,t,n){let r,i=this,o=i.events.length,s=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]);for(;o--;){let e=i.events[o][1];if("labelImage"===e.type){r=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(o){if(!r||!r._balanced)return n(o);let a=(0,x.B)(i.sliceSerialize({start:r.end,end:i.now()}));return 94===a.codePointAt(0)&&s.includes(a.slice(1))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(o),e.exit("gfmFootnoteCallLabelMarker"),t(o)):n(o)}}function ey(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";let r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;let o={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},s={type:"chunkString",contentType:"string",start:Object.assign({},o.start),end:Object.assign({},o.end)},a=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",i,t],["exit",i,t],["enter",o,t],["enter",s,t],["exit",s,t],["exit",o,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...a),e}function ev(e,t,n){let r,i=this,s=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]),a=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),l};function l(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",u)}function u(l){if(a>999||93===l&&!r||null===l||91===l||(0,o.Ee)(l))return n(l);if(93===l){e.exit("chunkString");let r=e.exit("gfmFootnoteCallString");return s.includes((0,x.B)(i.sliceSerialize(r)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(l),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(l)}return(0,o.Ee)(l)||(r=!0),a++,e.consume(l),92===l?c:u}function c(t){return 91===t||92===t||93===t?(e.consume(t),a++,u):u(t)}}function eb(e,t,n){let r,i,s=this,a=s.parser.gfmFootnotes||(s.parser.gfmFootnotes=[]),l=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),u};function u(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",c):n(t)}function c(t){if(l>999||93===t&&!i||null===t||91===t||(0,o.Ee)(t))return n(t);if(93===t){e.exit("chunkString");let n=e.exit("gfmFootnoteDefinitionLabelString");return r=(0,x.B)(s.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),d}return(0,o.Ee)(t)||(i=!0),l++,e.consume(t),92===t?h:c}function h(t){return 91===t||92===t||93===t?(e.consume(t),l++,c):c(t)}function d(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),a.includes(r)||a.push(r),(0,ep.N)(e,f,"gfmFootnoteDefinitionWhitespace")):n(t)}function f(e){return t(e)}}function ex(e,t,n){return e.check(ef.B,t,e.attempt(em,t,n))}function ew(e){e.exit("gfmFootnoteDefinition")}var ek=n(1603),eS=n(1877);class eE{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let i=0;if(0!==n||0!==r.length){for(;i<e.map.length;){if(e.map[i][0]===t){e.map[i][1]+=n,e.map[i][2].push(...r);return}i+=1}e.map.push([t,n,r])}}(this,e,t,n)}consume(e){if(this.map.sort(function(e,t){return e[0]-t[0]}),0===this.map.length)return;let t=this.map.length,n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(let t of r)e.push(t);r=n.pop()}this.map.length=0}}function eT(e,t,n){let r,i=this,s=0,a=0;return function(e){let t=i.events.length-1;for(;t>-1;){let e=i.events[t][1].type;if("lineEnding"===e||"linePrefix"===e)t--;else break}let r=t>-1?i.events[t][1].type:null,o="tableHead"===r||"tableRow"===r?b:l;return o===b&&i.parser.lazy[i.now().line]?n(e):o(e)};function l(t){var n;return e.enter("tableHead"),e.enter("tableRow"),124===(n=t)||(r=!0,a+=1),u(n)}function u(t){return null===t?n(t):(0,o.HP)(t)?a>1?(a=0,i.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),d):n(t):(0,o.On)(t)?(0,ep.N)(e,u,"whitespace")(t):(a+=1,r&&(r=!1,s+=1),124===t)?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),r=!0,u):(e.enter("data"),c(t))}function c(t){return null===t||124===t||(0,o.Ee)(t)?(e.exit("data"),u(t)):(e.consume(t),92===t?h:c)}function h(t){return 92===t||124===t?(e.consume(t),c):c(t)}function d(t){return(i.interrupt=!1,i.parser.lazy[i.now().line])?n(t):(e.enter("tableDelimiterRow"),r=!1,(0,o.On)(t))?(0,ep.N)(e,f,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):f(t)}function f(t){return 45===t||58===t?m(t):124===t?(r=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),p):n(t)}function p(t){return(0,o.On)(t)?(0,ep.N)(e,m,"whitespace")(t):m(t)}function m(t){return 58===t?(a+=1,r=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),g):45===t?(a+=1,g(t)):null===t||(0,o.HP)(t)?v(t):n(t)}function g(t){return 45===t?(e.enter("tableDelimiterFiller"),function t(n){return 45===n?(e.consume(n),t):58===n?(r=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(n),e.exit("tableDelimiterMarker"),y):(e.exit("tableDelimiterFiller"),y(n))}(t)):n(t)}function y(t){return(0,o.On)(t)?(0,ep.N)(e,v,"whitespace")(t):v(t)}function v(i){if(124===i)return f(i);if(null===i||(0,o.HP)(i))return r&&s===a?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(i)):n(i);return n(i)}function b(t){return e.enter("tableRow"),x(t)}function x(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),x):null===n||(0,o.HP)(n)?(e.exit("tableRow"),t(n)):(0,o.On)(n)?(0,ep.N)(e,x,"whitespace")(n):(e.enter("data"),w(n))}function w(t){return null===t||124===t||(0,o.Ee)(t)?(e.exit("data"),x(t)):(e.consume(t),92===t?k:w)}function k(t){return 92===t||124===t?(e.consume(t),w):w(t)}}function e_(e,t){let n,r,i,o=-1,s=!0,a=0,l=[0,0,0,0],u=[0,0,0,0],c=!1,h=0,d=new eE;for(;++o<e.length;){let f=e[o],p=f[1];"enter"===f[0]?"tableHead"===p.type?(c=!1,0!==h&&(eA(d,t,h,n,r),r=void 0,h=0),n={type:"table",start:Object.assign({},p.start),end:Object.assign({},p.end)},d.add(o,0,[["enter",n,t]])):"tableRow"===p.type||"tableDelimiterRow"===p.type?(s=!0,i=void 0,l=[0,0,0,0],u=[0,o+1,0,0],c&&(c=!1,r={type:"tableBody",start:Object.assign({},p.start),end:Object.assign({},p.end)},d.add(o,0,[["enter",r,t]])),a="tableDelimiterRow"===p.type?2:r?3:1):a&&("data"===p.type||"tableDelimiterMarker"===p.type||"tableDelimiterFiller"===p.type)?(s=!1,0===u[2]&&(0!==l[1]&&(u[0]=u[1],i=eC(d,t,l,a,void 0,i),l=[0,0,0,0]),u[2]=o)):"tableCellDivider"===p.type&&(s?s=!1:(0!==l[1]&&(u[0]=u[1],i=eC(d,t,l,a,void 0,i)),u=[(l=u)[1],o,0,0])):"tableHead"===p.type?(c=!0,h=o):"tableRow"===p.type||"tableDelimiterRow"===p.type?(h=o,0!==l[1]?(u[0]=u[1],i=eC(d,t,l,a,o,i)):0!==u[1]&&(i=eC(d,t,u,a,o,i)),a=0):a&&("data"===p.type||"tableDelimiterMarker"===p.type||"tableDelimiterFiller"===p.type)&&(u[3]=o)}for(0!==h&&eA(d,t,h,n,r),d.consume(t.events),o=-1;++o<t.events.length;){let e=t.events[o];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=function(e,t){let n=!1,r=[];for(;t<e.length;){let i=e[t];if(n){if("enter"===i[0])"tableContent"===i[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===i[1].type){if("tableDelimiterMarker"===e[t-1][1].type){let e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===i[1].type)break}else"enter"===i[0]&&"tableDelimiterRow"===i[1].type&&(n=!0);t+=1}return r}(t.events,o))}return e}function eC(e,t,n,r,i,o){0!==n[0]&&(o.end=Object.assign({},eI(t.events,n[0])),e.add(n[0],0,[["exit",o,t]]));let s=eI(t.events,n[1]);if(o={type:1===r?"tableHeader":2===r?"tableDelimiter":"tableData",start:Object.assign({},s),end:Object.assign({},s)},e.add(n[1],0,[["enter",o,t]]),0!==n[2]){let i=eI(t.events,n[2]),o=eI(t.events,n[3]),s={type:"tableContent",start:Object.assign({},i),end:Object.assign({},o)};if(e.add(n[2],0,[["enter",s,t]]),2!==r){let r=t.events[n[2]],i=t.events[n[3]];if(r[1].end=Object.assign({},i[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){let t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",s,t]])}return void 0!==i&&(o.end=Object.assign({},eI(t.events,i)),e.add(i,0,[["exit",o,t]]),o=void 0),o}function eA(e,t,n,r,i){let o=[],s=eI(t.events,n);i&&(i.end=Object.assign({},s),o.push(["exit",i,t])),r.end=Object.assign({},s),o.push(["exit",r,t]),e.add(n+1,0,o)}function eI(e,t){let n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}let eP={name:"tasklistCheck",tokenize:function(e,t,n){let r=this;return function(t){return null===r.previous&&r._gfmTasklistFirstContentOfListItem?(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),i):n(t)};function i(t){return(0,o.Ee)(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),s):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),s):n(t)}function s(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),a):n(t)}function a(r){return(0,o.HP)(r)?t(r):(0,o.On)(r)?e.check({tokenize:eR},t,n)(r):n(r)}}};function eR(e,t,n){return(0,ep.N)(e,function(e){return null===e?n(e):t(e)},"whitespace")}let eM={};function eO(e){let t,n=e||eM,r=this.data(),i=r.micromarkExtensions||(r.micromarkExtensions=[]),o=r.fromMarkdownExtensions||(r.fromMarkdownExtensions=[]),s=r.toMarkdownExtensions||(r.toMarkdownExtensions=[]);i.push((0,J.y)([{text:es},{document:{91:{name:"gfmFootnoteDefinition",tokenize:eb,continuation:{tokenize:ex},exit:ew}},text:{91:{name:"gfmFootnoteCall",tokenize:ev},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:eg,resolveTo:ey}}},function(e){let t=(e||{}).singleTilde,n={name:"strikethrough",tokenize:function(e,n,r){let i=this.previous,o=this.events,s=0;return function(a){return 126===i&&"characterEscape"!==o[o.length-1][1].type?r(a):(e.enter("strikethroughSequenceTemporary"),function o(a){let l=(0,N.S)(i);if(126===a)return s>1?r(a):(e.consume(a),s++,o);if(s<2&&!t)return r(a);let u=e.exit("strikethroughSequenceTemporary"),c=(0,N.S)(a);return u._open=!c||2===c&&!!l,u._close=!l||2===l&&!!c,n(a)}(a))}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";let i={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},o={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},s=[["enter",i,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",o,t]],a=t.parser.constructs.insideSpan.null;a&&(0,ek.m)(s,s.length,0,(0,eS.W)(a,e.slice(r+1,n),t)),(0,ek.m)(s,s.length,0,[["exit",o,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",i,t]]),(0,ek.m)(e,r-1,n-r+3,s),n=r+s.length-2;break}}for(n=-1;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}(n),{flow:{null:{name:"table",tokenize:eT,resolveAll:e_}}},{text:{91:eP}}])),o.push([{transforms:[g],enter:{literalAutolink:c,literalAutolinkEmail:h,literalAutolinkHttp:h,literalAutolinkWww:h},exit:{literalAutolink:m,literalAutolinkEmail:p,literalAutolinkHttp:d,literalAutolinkWww:f}},{enter:{gfmFootnoteCallString:w,gfmFootnoteCall:k,gfmFootnoteDefinitionLabelString:S,gfmFootnoteDefinition:E},exit:{gfmFootnoteCallString:T,gfmFootnoteCall:_,gfmFootnoteDefinitionLabelString:C,gfmFootnoteDefinition:A}},{canContainEols:["delete"],enter:{strikethrough:O},exit:{strikethrough:D}},{enter:{table:U,tableData:$,tableHeader:$,tableRow:H},exit:{codeText:q,table:V,tableData:W,tableHeader:W,tableRow:W}},{exit:{taskListCheckValueChecked:Y,taskListCheckValueUnchecked:Y,paragraph:X}}]),s.push({extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:l,notInConstruct:u},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:l,notInConstruct:u},{character:":",before:"[ps]",after:"\\/",inConstruct:l,notInConstruct:u}]},(t=!1,n&&n.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,i){let o=r.createTracker(i),s=o.move("[^"),a=r.enter("footnoteDefinition"),l=r.enter("label");return s+=o.move(r.safe(r.associationId(e),{before:s,after:"]"})),l(),s+=o.move("]:"),e.children&&e.children.length>0&&(o.shift(4),s+=o.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,o.current()),t?R:P))),a(),s},footnoteReference:I},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:M}],handlers:{delete:L}},function(e){let t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,i=t.stringLength,o=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=z.inlineCode(e,t,n);return n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&")),r},table:function(e,t,n,r){return a(function(e,t,n){let r=e.children,i=-1,o=[],s=t.enter("table");for(;++i<r.length;)o[i]=l(r[i],t,n);return s(),o}(e,n,r),e.align)},tableCell:s,tableRow:function(e,t,n,r){let i=a([l(e,n,r)]);return i.slice(0,i.indexOf("\n"))}}};function s(e,t,n,r){let i=n.enter("tableCell"),s=n.enter("phrasing"),a=n.containerPhrasing(e,{...r,before:o,after:o});return s(),i(),a}function a(e,t){return function(e,t){let n=t||{},r=(n.align||[]).concat(),i=n.stringLength||j,o=[],s=[],a=[],l=[],u=0,c=-1;for(;++c<e.length;){let t=[],r=[],o=-1;for(e[c].length>u&&(u=e[c].length);++o<e[c].length;){var h;let s=null==(h=e[c][o])?"":String(h);if(!1!==n.alignDelimiters){let e=i(s);r[o]=e,(void 0===l[o]||e>l[o])&&(l[o]=e)}t.push(s)}s[c]=t,a[c]=r}let d=-1;if("object"==typeof r&&"length"in r)for(;++d<u;)o[d]=B(r[d]);else{let e=B(r);for(;++d<u;)o[d]=e}d=-1;let f=[],p=[];for(;++d<u;){let e=o[d],t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let i=!1===n.alignDelimiters?1:Math.max(1,l[d]-t.length-r.length),s=t+"-".repeat(i)+r;!1!==n.alignDelimiters&&((i=t.length+i+r.length)>l[d]&&(l[d]=i),p[d]=i),f[d]=s}s.splice(1,0,f),a.splice(1,0,p),c=-1;let m=[];for(;++c<s.length;){let e=s[c],t=a[c];d=-1;let r=[];for(;++d<u;){let i=e[d]||"",s="",a="";if(!1!==n.alignDelimiters){let e=l[d]-(t[d]||0),n=o[d];114===n?s=" ".repeat(e):99===n?e%2?(s=" ".repeat(e/2+.5),a=" ".repeat(e/2-.5)):a=s=" ".repeat(e/2):a=" ".repeat(e)}!1===n.delimiterStart||d||r.push("|"),!1!==n.padding&&(!1!==n.alignDelimiters||""!==i)&&(!1!==n.delimiterStart||d)&&r.push(" "),!1!==n.alignDelimiters&&r.push(s),r.push(i),!1!==n.alignDelimiters&&r.push(a),!1!==n.padding&&r.push(" "),(!1!==n.delimiterEnd||d!==u-1)&&r.push("|")}m.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:i})}function l(e,t,n){let r=e.children,i=-1,o=[],a=t.enter("tableRow");for(;++i<r.length;)o[i]=s(r[i],e,t,n);return a(),o}}(n),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:G}}]})}},4866:(e,t,n)=>{"use strict";var r=n(2700).hp;let i=void 0!==r,o=/"(?:_|\\u005[Ff])(?:_|\\u005[Ff])(?:p|\\u0070)(?:r|\\u0072)(?:o|\\u006[Ff])(?:t|\\u0074)(?:o|\\u006[Ff])(?:_|\\u005[Ff])(?:_|\\u005[Ff])"\s*:/,s=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/;function a(e,t,n){null==n&&null!==t&&"object"==typeof t&&(n=t,t=void 0),i&&r.isBuffer(e)&&(e=e.toString()),e&&65279===e.charCodeAt(0)&&(e=e.slice(1));let a=JSON.parse(e,t);if(null===a||"object"!=typeof a)return a;let u=n&&n.protoAction||"error",c=n&&n.constructorAction||"error";if("ignore"===u&&"ignore"===c)return a;if("ignore"!==u&&"ignore"!==c){if(!1===o.test(e)&&!1===s.test(e))return a}else if("ignore"!==u&&"ignore"===c){if(!1===o.test(e))return a}else if(!1===s.test(e))return a;return l(a,{protoAction:u,constructorAction:c,safe:n&&n.safe})}function l(e,{protoAction:t="error",constructorAction:n="error",safe:r}={}){let i=[e];for(;i.length;){let e=i;for(let o of(i=[],e)){if("ignore"!==t&&Object.prototype.hasOwnProperty.call(o,"__proto__")){if(!0===r)return null;if("error"===t)throw SyntaxError("Object contains forbidden prototype property");delete o.__proto__}if("ignore"!==n&&Object.prototype.hasOwnProperty.call(o,"constructor")&&Object.prototype.hasOwnProperty.call(o.constructor,"prototype")){if(!0===r)return null;if("error"===n)throw SyntaxError("Object contains forbidden prototype property");delete o.constructor}for(let e in o){let t=o[e];t&&"object"==typeof t&&i.push(t)}}}return e}function u(e,t,n){let{stackTraceLimit:r}=Error;Error.stackTraceLimit=0;try{return a(e,t,n)}finally{Error.stackTraceLimit=r}}e.exports=u,e.exports.default=u,e.exports.parse=u,e.exports.safeParse=function(e,t){let{stackTraceLimit:n}=Error;Error.stackTraceLimit=0;try{return a(e,t,{safe:!0})}catch{return}finally{Error.stackTraceLimit=n}},e.exports.scan=l},4967:(e,t,n)=>{"use strict";n.d(t,{i:()=>l});var r=n(8716),i=n(2115),o=n(6882),s=n(5455),a=n(7196),l=(0,r.x)("ActionBarPrimitive.Reload",()=>{let e=(0,o.LN)(),t=(0,s.Mp)(),n=(0,a.y)([t,e],(e,t)=>e.isRunning||e.isDisabled||"assistant"!==t.role),r=(0,i.useCallback)(()=>{e.reload()},[e]);return n?null:r})},5058:(e,t,n)=>{"use strict";n.d(t,{W:()=>s});var r=n(8716),i=n(2115),o=n(2605),s=(0,r.x)("ComposerPrimitive.Cancel",()=>{let e=(0,o.I)(),t=(0,o.s)(e=>!e.canCancel),n=(0,i.useCallback)(()=>{e.cancel()},[e]);return t?null:n})},5185:(e,t,n)=>{"use strict";function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}n.d(t,{mK:()=>r}),"undefined"!=typeof window&&window.document&&window.document.createElement},5196:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5333:(e,t,n)=>{"use strict";n.d(t,{B:()=>o});var r=n(4581),i=n(2556);let o={partial:!0,tokenize:function(e,t,n){return function(t){return(0,i.On)(t)?(0,r.N)(e,o,"linePrefix")(t):o(t)};function o(e){return null===e||(0,i.HP)(e)?t(e):n(e)}}}},5453:(e,t,n)=>{"use strict";n.d(t,{v:()=>s});var r=n(2115);let i=e=>{let t,n=new Set,r=(e,r)=>{let i="function"==typeof e?e(t):e;if(!Object.is(i,t)){let e=t;t=(null!=r?r:"object"!=typeof i||null===i)?i:Object.assign({},t,i),n.forEach(n=>n(t,e))}},i=()=>t,o={setState:r,getState:i,getInitialState:()=>s,subscribe:e=>(n.add(e),()=>n.delete(e))},s=t=e(r,i,o);return o},o=e=>{let t=(e=>e?i(e):i)(e),n=e=>(function(e,t=e=>e){let n=r.useSyncExternalStore(e.subscribe,r.useCallback(()=>t(e.getState()),[e,t]),r.useCallback(()=>t(e.getInitialState()),[e,t]));return r.useDebugValue(n),n})(t,e);return Object.assign(n,t),n},s=e=>e?o(e):o},5455:(e,t,n)=>{"use strict";n.d(t,{Mp:()=>l,_W:()=>u,qm:()=>s});var r=n(2115),i=n(4354),o=n(2288),s=(0,r.createContext)(null),a=(0,i.h)(s,"AssistantRuntimeProvider");function l(e){let t=a(e);return t?t.useThreadRuntime():null}var u=(0,o.k)(l);(0,o.k)(e=>{var t,n;return null!=(n=null==(t=l(e))?void 0:t.composer)?n:null})},5586:(e,t,n)=>{"use strict";n.d(t,{P:()=>a});var r=n(3655),i=n(2115),o=n(1386),s=n(5155),a=(0,i.forwardRef)((e,t)=>{let n=(0,o.Xi)(e=>e.isMain);return(0,s.jsx)(r.sG.div,{...n?{"data-active":"true","aria-current":"true"}:null,...e,ref:t})});a.displayName="ThreadListItemPrimitive.Root"},5845:(e,t,n)=>{"use strict";n.d(t,{i:()=>a});var r,i=n(2115),o=n(2712),s=(r||(r=n.t(i,2)))[" useInsertionEffect ".trim().toString()]||o.N;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,a,l]=function({defaultProp:e,onChange:t}){let[n,r]=i.useState(e),o=i.useRef(n),a=i.useRef(t);return s(()=>{a.current=t},[t]),i.useEffect(()=>{o.current!==n&&(a.current?.(n),o.current=n)},[n,o]),[n,r,a]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,i.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else a(t)},[u,e,a,l])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{"use strict";n.d(t,{A:()=>s,q:()=>o});var r=n(2115),i=n(5155);function o(e,t){let n=r.createContext(t),o=e=>{let{children:t,...o}=e,s=r.useMemo(()=>o,Object.values(o));return(0,i.jsx)(n.Provider,{value:s,children:t})};return o.displayName=e+"Provider",[o,function(i){let o=r.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function s(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return o.scopeName=e,[function(t,o){let s=r.createContext(o),a=n.length;n=[...n,o];let l=t=>{let{scope:n,children:o,...l}=t,u=n?.[e]?.[a]||s,c=r.useMemo(()=>l,Object.values(l));return(0,i.jsx)(u.Provider,{value:c,children:o})};return l.displayName=t+"Provider",[l,function(n,i){let l=i?.[e]?.[a]||s,u=r.useContext(l);if(u)return u;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(o,...t)]}},6101:(e,t,n)=>{"use strict";n.d(t,{s:()=>s,t:()=>o});var r=n(2115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function s(...e){return r.useCallback(o(...e),e)}},6301:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,o=/^:\s*/,s=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,l=/^\s+|\s+$/g;function u(e){return e?e.replace(l,""):""}e.exports=function(e,l){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];l=l||{};var c=1,h=1;function d(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");h=~r?e.length-r:h+e.length}function f(){var e={line:c,column:h};return function(t){return t.position=new p(e),y(r),t}}function p(e){this.start=e,this.end={line:c,column:h},this.source=l.source}p.prototype.content=e;var m=[];function g(t){var n=Error(l.source+":"+c+":"+h+": "+t);if(n.reason=t,n.filename=l.source,n.line=c,n.column=h,n.source=e,l.silent)m.push(n);else throw n}function y(t){var n=t.exec(e);if(n){var r=n[0];return d(r),e=e.slice(r.length),n}}function v(e){var t;for(e=e||[];t=b();)!1!==t&&e.push(t);return e}function b(){var t=f();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return g("End of comment missing");var r=e.slice(2,n-2);return h+=2,d(r),e=e.slice(n),h+=2,t({type:"comment",comment:r})}}y(r);var x,w=[];for(v(w);x=function(){var e=f(),n=y(i);if(n){if(b(),!y(o))return g("property missing ':'");var r=y(s),l=e({type:"declaration",property:u(n[0].replace(t,"")),value:r?u(r[0].replace(t,"")):""});return y(a),l}}();)!1!==x&&(w.push(x),v(w));return w}},6474:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6654:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=n(2115);function i(e,t){let n=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(n.current=o(e,r)),t&&(i.current=o(t,r))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6853:(e,t,n)=>{"use strict";n.d(t,{I:()=>l,y:()=>u});var r=n(8716),i=n(2115),o=n(7196),s=n(5455),a=n(2605),l=()=>{let e=(0,a.I)(),t=(0,s.Mp)(),n=(0,o.y)([t,e],(e,t)=>e.isRunning||!t.isEditing||t.isEmpty),r=(0,i.useCallback)(()=>{e.send()},[e]);return n?null:r},u=(0,r.x)("ComposerPrimitive.Send",l)},6874:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return v}});let r=n(6966),i=n(5155),o=r._(n(2115)),s=n(2757),a=n(5227),l=n(9818),u=n(6654),c=n(9991),h=n(5929);n(3230);let d=n(4930),f=n(2664),p=n(6634);function m(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function g(e){let t,n,r,[s,g]=(0,o.useOptimistic)(d.IDLE_LINK_STATUS),v=(0,o.useRef)(null),{href:b,as:x,children:w,prefetch:k=null,passHref:S,replace:E,shallow:T,scroll:_,onClick:C,onMouseEnter:A,onTouchStart:I,legacyBehavior:P=!1,onNavigate:R,ref:M,unstable_dynamicOnHover:O,...D}=e;t=w,P&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let L=o.default.useContext(a.AppRouterContext),j=!1!==k,B=null===k||"auto"===k?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:N,as:F}=o.default.useMemo(()=>{let e=m(b);return{href:e,as:x?m(x):e}},[b,x]);P&&(n=o.default.Children.only(t));let z=P?n&&"object"==typeof n&&n.ref:M,U=o.default.useCallback(e=>(null!==L&&(v.current=(0,d.mountLinkInstance)(e,N,L,B,j,g)),()=>{v.current&&((0,d.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,d.unmountPrefetchableInstance)(e)}),[j,N,L,B,g]),V={ref:(0,u.useMergedRef)(U,z),onClick(e){P||"function"!=typeof C||C(e),P&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),L&&(e.defaultPrevented||function(e,t,n,r,i,s,a){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),a){let e=!1;if(a({preventDefault:()=>{e=!0}}),e)return}o.default.startTransition(()=>{(0,p.dispatchNavigateAction)(n||t,i?"replace":"push",null==s||s,r.current)})}}(e,N,F,v,E,_,R))},onMouseEnter(e){P||"function"!=typeof A||A(e),P&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),L&&j&&(0,d.onNavigationIntent)(e.currentTarget,!0===O)},onTouchStart:function(e){P||"function"!=typeof I||I(e),P&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),L&&j&&(0,d.onNavigationIntent)(e.currentTarget,!0===O)}};return(0,c.isAbsoluteUrl)(F)?V.href=F:P&&!S&&("a"!==n.type||"href"in n.props)||(V.href=(0,h.addBasePath)(F)),r=P?o.default.cloneElement(n,V):(0,i.jsx)("a",{...D,...V,children:t}),(0,i.jsx)(y.Provider,{value:s,children:r})}n(3180);let y=(0,o.createContext)(d.IDLE_LINK_STATUS),v=()=>(0,o.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6882:(e,t,n)=>{"use strict";n.d(t,{JX:()=>c,LN:()=>u,Nf:()=>a,iP:()=>h,uW:()=>d,vd:()=>f});var r=n(2115),i=n(4354),o=n(97),s=n(2288),a=(0,r.createContext)(null),l=(0,i.h)(a,"a component passed to <ThreadPrimitive.Messages components={...} />");function u(e){let t=l(e);return t?t.useMessageRuntime():null}var c=(0,s.k)(u),h=(0,s.k)(e=>{var t,n;return null!=(n=null==(t=u(e))?void 0:t.composer)?n:null}),{useMessageUtils:d,useMessageUtilsStore:f}=(0,o.o)(l,"useMessageUtils")},6906:(e,t,n)=>{"use strict";n.d(t,{Bs:()=>f,D0:()=>c,a2:()=>d});var r=n(2115),i=n(5453),o=n(949),s=n(97),a=n(5155),l=(0,r.createContext)(null),u=e=>{let{children:t}=e,n=h({optional:!0}),s=(0,o.oM)(),[u]=(0,r.useState)(()=>{var e;return e=s.getState().status,{useSmoothStatus:(0,i.v)(()=>e)}});return n?t:(0,a.jsx)(l.Provider,{value:u,children:t})},c=e=>{let t=(0,r.forwardRef)((t,n)=>(0,a.jsx)(u,{children:(0,a.jsx)(e,{...t,ref:n})}));return t.displayName=e.displayName,t};function h(e){let t=(0,r.useContext)(l);if(!(null==e?void 0:e.optional)&&!t)throw Error("This component must be used within a SmoothContextProvider.");return t}var{useSmoothStatus:d,useSmoothStatusStore:f}=(0,s.o)(h,"useSmoothStatus")},7004:(e,t,n)=>{"use strict";n.d(t,{u:()=>s});var r=n(8716),i=n(2115),o=n(5455),s=(0,r.x)("ThreadPrimitive.Suggestion",e=>{let{prompt:t,autoSend:n}=e,r=(0,o.Mp)(),s=(0,o._W)(e=>e.isDisabled),a=(0,i.useCallback)(()=>{n&&!r.getState().isRunning?r.append(t):r.composer.setText(t)},[r,n,t]);return s?null:a},["prompt","autoSend","method"])},7016:(e,t)=>{t.read=function(e,t,n,r,i){var o,s,a=8*i-r-1,l=(1<<a)-1,u=l>>1,c=-7,h=n?i-1:0,d=n?-1:1,f=e[t+h];for(h+=d,o=f&(1<<-c)-1,f>>=-c,c+=a;c>0;o=256*o+e[t+h],h+=d,c-=8);for(s=o&(1<<-c)-1,o>>=-c,c+=r;c>0;s=256*s+e[t+h],h+=d,c-=8);if(0===o)o=1-u;else{if(o===l)return s?NaN:1/0*(f?-1:1);s+=Math.pow(2,r),o-=u}return(f?-1:1)*s*Math.pow(2,o-r)},t.write=function(e,t,n,r,i,o){var s,a,l,u=8*o-i-1,c=(1<<u)-1,h=c>>1,d=5960464477539062e-23*(23===i),f=r?0:o-1,p=r?1:-1,m=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(a=+!!isNaN(t),s=c):(s=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-s))<1&&(s--,l*=2),s+h>=1?t+=d/l:t+=d*Math.pow(2,1-h),t*l>=2&&(s++,l/=2),s+h>=c?(a=0,s=c):s+h>=1?(a=(t*l-1)*Math.pow(2,i),s+=h):(a=t*Math.pow(2,h-1)*Math.pow(2,i),s=0));i>=8;e[n+f]=255&a,f+=p,a/=256,i-=8);for(s=s<<i|a,u+=i;u>0;e[n+f]=255&s,f+=p,s/=256,u-=8);e[n+f-p]|=128*m}},7119:(e,t,n)=>{"use strict";n.d(t,{Dr:()=>c,Ij:()=>u,WF:()=>a,_K:()=>d});var r=n(2115),i=n(4354),o=n(97),s=n(2288),a=(0,r.createContext)(null),l=(0,i.h)(a,"AssistantRuntimeProvider");function u(e){let t=l(e);return t?t.useAssistantRuntime():null}var{useToolUIs:c,useToolUIsStore:h}=(0,o.o)(l,"useToolUIs"),d=(0,s.k)(e=>{var t,n;return null!=(n=null==(t=u(e))?void 0:t.threads)?n:null})},7126:(e,t,n)=>{"use strict";n.d(t,{X:()=>u});var r=n(3655),i=n(2115),o=n(6882),s=n(2156),a=n(6101),l=n(5155),u=(0,i.forwardRef)((e,t)=>{let n=(()=>{let e=(0,o.vd)(),t=(0,i.useCallback)(t=>{let n=e.getState().setIsHovering,r=()=>{n(!0)},i=()=>{n(!1)};return t.addEventListener("mouseenter",r),t.addEventListener("mouseleave",i),()=>{t.removeEventListener("mouseenter",r),t.removeEventListener("mouseleave",i),n(!1)}},[e]);return(0,s.s)(t)})(),u=(0,a.s)(t,n);return(0,l.jsx)(r.sG.div,{...e,ref:u})});u.displayName="MessagePrimitive.Root"},7156:(e,t,n)=>{"use strict";n.d(t,{U:()=>l});var r=n(5185),i=n(3655),o=n(2115),s=n(6853),a=n(5155),l=(0,o.forwardRef)((e,t)=>{let{onSubmit:n,...o}=e,l=(0,s.I)();return(0,a.jsx)(i.sG.form,{...o,ref:t,onSubmit:(0,r.mK)(n,e=>{e.preventDefault(),l&&l()})})});l.displayName="ComposerPrimitive.Root"},7196:(e,t,n)=>{"use strict";n.d(t,{y:()=>i});var r=n(2115),i=(e,t)=>(0,r.useMemo)(()=>(e=>{let t=t=>{let n=e.map(e=>e.subscribe(t));return()=>{for(let e of n)e()}};return n=>{let i=()=>n(...e.map(e=>e.getState()));return(0,r.useSyncExternalStore)(t,i,i)}})(e),e)(t)},7295:(e,t,n)=>{"use strict";n.d(t,{r:()=>P});var r=n(2115),i=n(7119),o=n(949),s=n(5453),a=n(9935),l=n(1174),u=n(4229),c=n(5155),h={type:"complete"},d={type:"running"},f=e=>{let{children:t,text:n,isRunning:i}=e,[f]=(0,r.useState)(()=>{let e=(0,s.v)(()=>({status:i?d:h,type:"text",text:n})),t=new l.D({path:{ref:"text",threadSelector:{type:"main"},messageSelector:{type:"messageId",messageId:""},messagePartSelector:{type:"index",index:0}},getState:e.getState,subscribe:e.subscribe});return(0,u.Q)(t),{useMessagePartRuntime:(0,s.v)(()=>t),useMessagePart:e}});return(0,r.useEffect)(()=>{let e=f.useMessagePart.getState(),t=e.text!==n,r=i?d:h,o=e.status!==r;(t||o)&&(0,a.s)(f.useMessagePart).setState({type:"text",text:n,status:r},!0)},[f,i,n]),(0,c.jsx)(o.iW.Provider,{value:f,children:t})},p=n(6882),m=e=>{let{runtime:t,children:n}=e,i=(e=>{let[t]=(0,r.useState)(()=>(0,s.v)(()=>e));return(0,r.useEffect)(()=>{(0,u.Q)(e),(0,a.s)(t).setState(e,!0)},[e,t]),t})(t),[l]=(0,r.useState)(()=>({useMessagePartRuntime:i}));return(0,c.jsx)(o.iW.Provider,{value:l,children:n})},g=n(2500),y=n(1246),v=(0,r.forwardRef)((e,t)=>{let{smooth:n=!0,component:r="span",...i}=e,{text:o,status:s}=(0,y.c)((0,g.d)(),n);return(0,c.jsx)(r,{"data-status":s.type,...i,ref:t,children:o})});v.displayName="MessagePartPrimitive.Text";var b=n(3655),x=(0,r.forwardRef)((e,t)=>{let{image:n}=(0,o.ME)(e=>{if("image"!==e.type)throw Error("MessagePartImage can only be used inside image message parts.");return e});return(0,c.jsx)(b.sG.img,{src:n,...e,ref:t})});x.displayName="MessagePartPrimitive.Image";var w=e=>{let{children:t}=e;return(0,o.ME)(e=>"running"===e.status.type)?t:null};w.displayName="MessagePartPrimitive.InProgress";let k=(e,t)=>{let n=e instanceof Map?e:new Map(e.entries()),r=t instanceof Map?t:new Map(t.entries());if(n.size!==r.size)return!1;for(let[e,t]of n)if(!r.has(e)||!Object.is(t,r.get(e)))return!1;return!0};var S=e=>{var t;let{Fallback:n,...r}=e,o=null!=(t=(0,i.Dr)(e=>e.getToolUI(r.toolName)))?t:n;return o?(0,c.jsx)(o,{...r}):null},E={Text:()=>(0,c.jsxs)("p",{style:{whiteSpace:"pre-line"},children:[(0,c.jsx)(v,{}),(0,c.jsx)(w,{children:(0,c.jsx)("span",{style:{fontFamily:"revert"},children:" ●"})})]}),Reasoning:()=>null,Source:()=>null,Image:()=>(0,c.jsx)(x,{}),File:()=>null,Unstable_Audio:()=>null,ToolGroup:e=>{let{children:t}=e;return t}},T=e=>{let{components:{Text:t=E.Text,Reasoning:n=E.Reasoning,Image:r=E.Image,Source:i=E.Source,File:s=E.File,Unstable_Audio:a=E.Unstable_Audio,tools:l={}}={}}=e,u=(0,o.oM)(),h=(0,o.ME)(),d=h.type;if("tool-call"===d){var f,p;let e=e=>u.addToolResult(e);if("Override"in l)return(0,c.jsx)(l.Override,{...h,addResult:e});let t=null!=(p=null==(f=l.by_name)?void 0:f[h.toolName])?p:l.Fallback;return(0,c.jsx)(S,{...h,Fallback:t,addResult:e})}if("requires-action"===h.status.type)throw Error("Encountered unexpected requires-action status");switch(d){case"text":return(0,c.jsx)(t,{...h});case"reasoning":return(0,c.jsx)(n,{...h});case"source":return(0,c.jsx)(i,{...h});case"image":return(0,c.jsx)(r,{...h});case"file":return(0,c.jsx)(s,{...h});case"audio":return(0,c.jsx)(a,{...h});default:throw Error("Unknown message part type: ".concat(d))}},_=(0,r.memo)(e=>{let{index:t,components:n}=e,i=(0,p.LN)(),o=(0,r.useMemo)(()=>i.getMessagePartByIndex(t),[i,t]);return(0,c.jsx)(m,{runtime:o,children:(0,c.jsx)(T,{components:n})})},(e,t)=>{var n,r,i,o,s,a,l,u,c,h,d,f,p,m,g,y;return e.index===t.index&&(null==(n=e.components)?void 0:n.Text)===(null==(r=t.components)?void 0:r.Text)&&(null==(i=e.components)?void 0:i.Reasoning)===(null==(o=t.components)?void 0:o.Reasoning)&&(null==(s=e.components)?void 0:s.Source)===(null==(a=t.components)?void 0:a.Source)&&(null==(l=e.components)?void 0:l.Image)===(null==(u=t.components)?void 0:u.Image)&&(null==(c=e.components)?void 0:c.File)===(null==(h=t.components)?void 0:h.File)&&(null==(d=e.components)?void 0:d.Unstable_Audio)===(null==(f=t.components)?void 0:f.Unstable_Audio)&&(null==(p=e.components)?void 0:p.tools)===(null==(m=t.components)?void 0:m.tools)&&(null==(g=e.components)?void 0:g.ToolGroup)===(null==(y=t.components)?void 0:y.ToolGroup)});_.displayName="MessagePrimitive.PartByIndex";var C=Object.freeze({type:"complete"}),A=e=>{let{status:t,component:n}=e;return(0,c.jsx)(f,{text:"",isRunning:"running"===t.type,children:(0,c.jsx)(n,{type:"text",text:"",status:t})})},I=(0,r.memo)(e=>{var t,n;let{components:r}=e,i=null!=(t=(0,p.JX)(e=>e.status))?t:C;return(null==r?void 0:r.Empty)?(0,c.jsx)(r.Empty,{status:i}):(0,c.jsx)(A,{status:i,component:null!=(n=null==r?void 0:r.Text)?n:E.Text})},(e,t)=>{var n,r,i,o;return(null==(n=e.components)?void 0:n.Empty)===(null==(r=t.components)?void 0:r.Empty)&&(null==(i=e.components)?void 0:i.Text)===(null==(o=t.components)?void 0:o.Text)}),P=e=>{let{components:t}=e,n=(0,p.JX)(e=>e.content.length),i=(()=>{let e=(0,p.JX)(function(e){let t=r.useRef(void 0);return n=>{let r=e(n);return!function(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t||Object.getPrototypeOf(e)!==Object.getPrototypeOf(t))return!1;if(Symbol.iterator in e&&Symbol.iterator in t){if("entries"in e&&"entries"in t)return k(e,t);let n=e[Symbol.iterator](),r=t[Symbol.iterator](),i=n.next(),o=r.next();for(;!i.done&&!o.done;){if(!Object.is(i.value,o.value))return!1;i=n.next(),o=r.next()}return!!i.done&&!!o.done}return k({entries:()=>Object.entries(e)},{entries:()=>Object.entries(t)})}(t.current,r)?t.current=r:t.current}}(e=>e.content.map(e=>e.type)));return(0,r.useMemo)(()=>0===e.length?[]:(e=>{let t=[],n=-1;for(let r=0;r<e.length;r++)"tool-call"===e[r]?-1===n&&(n=r):(-1!==n&&(t.push({type:"toolGroup",startIndex:n,endIndex:r-1}),n=-1),t.push({type:"single",index:r}));return -1!==n&&t.push({type:"toolGroup",startIndex:n,endIndex:e.length-1}),t})(e),[e])})(),o=(0,r.useMemo)(()=>0===n?(0,c.jsx)(I,{components:t}):i.map(e=>{if("single"===e.type)return(0,c.jsx)(_,{index:e.index,components:t},e.index);{var n;let r=null!=(n=t.ToolGroup)?n:E.ToolGroup;return(0,c.jsx)(r,{startIndex:e.startIndex,endIndex:e.endIndex,children:Array.from({length:e.endIndex-e.startIndex+1},(n,r)=>(0,c.jsx)(_,{index:e.startIndex+r,components:t},r))},e.startIndex)}}),[i,t,n]);return(0,c.jsx)(c.Fragment,{children:o})};P.displayName="MessagePrimitive.Parts"},7489:(e,t,n)=>{"use strict";n.d(t,{b:()=>u});var r=n(2115),i=n(3655),o=n(5155),s="horizontal",a=["horizontal","vertical"],l=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:l=s,...u}=e,c=(n=l,a.includes(n))?l:s;return(0,o.jsx)(i.sG.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...u,ref:t})});l.displayName="Separator";var u=l},7589:(e,t,n)=>{"use strict";n.d(t,{D0:()=>u,Ek:()=>s,qZ:()=>l});var r=n(2115),i=n(4354),o=n(97),s=(0,r.createContext)(null),a=(0,i.h)(s,"ThreadPrimitive.Viewport"),{useThreadViewport:l,useThreadViewportStore:u}=(0,o.o)(a,"useThreadViewport")},7788:(e,t,n)=>{"use strict";n.d(t,{U:()=>l});var r=n(2115),i=n(5453),o=n(7589),s=n(9935),a=n(5155),l=e=>{let{children:t}=e,n=(()=>{let e=(0,o.D0)({optional:!0}),[t]=(0,r.useState)(()=>(()=>{let e=new Set;return(0,i.v)(()=>({isAtBottom:!0,scrollToBottom:()=>{for(let t of e)t()},onScrollToBottom:t=>(e.add(t),()=>{e.delete(t)})}))})());return(0,r.useEffect)(()=>null==e?void 0:e.getState().onScrollToBottom(()=>{t.getState().scrollToBottom()}),[e,t]),(0,r.useEffect)(()=>{if(e)return t.subscribe(t=>{e.getState().isAtBottom!==t.isAtBottom&&(0,s.s)(e).setState({isAtBottom:t.isAtBottom})})},[t,e]),t})(),[l]=(0,r.useState)(()=>({useThreadViewport:n}));return(0,a.jsx)(o.Ek.Provider,{value:l,children:t})}},7863:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},7915:(e,t,n)=>{"use strict";n.d(t,{C:()=>r});let r=function(e){var t,n;if(null==e)return o;if("function"==typeof e)return i(e);if("object"==typeof e){return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=r(e[n]);return i(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):(t=e,i(function(e){let n;for(n in t)if(e[n]!==t[n])return!1;return!0}))}if("string"==typeof e){return n=e,i(function(e){return e&&e.type===n})}throw Error("Expected function, string, or object as test")};function i(e){return function(t,n,r){return!!(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function o(){return!0}},7924:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,i.default)(e),o="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;o?t(r,i,e):i&&((n=n||{})[r]=i)}}),n};var i=r(n(6301))},8010:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(3655),i=n(2115),o=n(5155),s=(0,i.forwardRef)((e,t)=>(0,o.jsx)(r.sG.div,{role:"alert",...e,ref:t}));s.displayName="ErrorPrimitive.Root"},8135:()=>{},8428:(e,t,n)=>{"use strict";n.d(t,{YR:()=>i});var r=n(1922);function i(e,t,n,i){let o,s,a;"function"==typeof t&&"function"!=typeof n?(s=void 0,a=t,o=n):(s=t,a=n,o=i),(0,r.VG)(e,s,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return a(e,r,n)},o)}},8434:(e,t,n)=>{"use strict";n.d(t,{qW:()=>f});var r,i=n(2115),o=n(5185),s=n(3655),a=n(6101),l=n(9033),u=n(1595),c=n(5155),h="dismissableLayer.update",d=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=i.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:g=!1,onEscapeKeyDown:y,onPointerDownOutside:v,onFocusOutside:b,onInteractOutside:x,onDismiss:w,...k}=e,S=i.useContext(d),[E,T]=i.useState(null),_=null!=(f=null==E?void 0:E.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,C]=i.useState({}),A=(0,a.s)(t,e=>T(e)),I=Array.from(S.layers),[P]=[...S.layersWithOutsidePointerEventsDisabled].slice(-1),R=I.indexOf(P),M=E?I.indexOf(E):-1,O=S.layersWithOutsidePointerEventsDisabled.size>0,D=M>=R,L=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,l.c)(e),o=i.useRef(!1),s=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){m("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",s.current),s.current=t,n.addEventListener("click",s.current,{once:!0})):t()}else n.removeEventListener("click",s.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",s.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...S.branches].some(e=>e.contains(t));D&&!n&&(null==v||v(e),null==x||x(e),e.defaultPrevented||null==w||w())},_),j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,l.c)(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...S.branches].some(e=>e.contains(t))&&(null==b||b(e),null==x||x(e),e.defaultPrevented||null==w||w())},_);return(0,u.U)(e=>{M===S.layers.size-1&&(null==y||y(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},_),i.useEffect(()=>{if(E)return g&&(0===S.layersWithOutsidePointerEventsDisabled.size&&(r=_.body.style.pointerEvents,_.body.style.pointerEvents="none"),S.layersWithOutsidePointerEventsDisabled.add(E)),S.layers.add(E),p(),()=>{g&&1===S.layersWithOutsidePointerEventsDisabled.size&&(_.body.style.pointerEvents=r)}},[E,_,g,S]),i.useEffect(()=>()=>{E&&(S.layers.delete(E),S.layersWithOutsidePointerEventsDisabled.delete(E),p())},[E,S]),i.useEffect(()=>{let e=()=>C({});return document.addEventListener(h,e),()=>document.removeEventListener(h,e)},[]),(0,c.jsx)(s.sG.div,{...k,ref:A,style:{pointerEvents:O?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.mK)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,o.mK)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,o.mK)(e.onPointerDownCapture,L.onPointerDownCapture)})});function p(){let e=new CustomEvent(h);document.dispatchEvent(e)}function m(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,s.hO)(o,a):o.dispatchEvent(a)}f.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(d),r=i.useRef(null),o=(0,a.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(s.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch"},8441:(e,t,n)=>{"use strict";n.d(t,{D:()=>a});var r=n(3655),i=n(2115),o=n(6882),s=n(5155),a=(0,i.forwardRef)((e,t)=>{let{children:n,...i}=e,a=(0,o.JX)(e=>{var t;return(null==(t=e.status)?void 0:t.type)==="incomplete"&&"error"===e.status.reason?e.status.error:void 0});return void 0===a?null:(0,s.jsx)(r.sG.span,{...i,ref:t,children:null!=n?n:String(a)})});a.displayName="ErrorPrimitive.Message"},8494:(e,t,n)=>{"use strict";n.d(t,{r:()=>o});var r=n(8716),i=n(1386),o=(0,r.x)("ThreadListItemPrimitive.Trigger",()=>{let e=(0,i.pK)();return()=>{e.switchTo()}})},8592:(e,t,n)=>{"use strict";n.d(t,{z:()=>o});var r=n(6882),i=n(5155),o=e=>{let{children:t}=e;return(0,r.JX)(e=>{var t;return(null==(t=e.status)?void 0:t.type)==="incomplete"&&"error"===e.status.reason})?(0,i.jsx)(i.Fragment,{children:t}):null};o.displayName="MessagePrimitive.Error"},8716:(e,t,n)=>{"use strict";n.d(t,{x:()=>a});var r=n(2115),i=n(3655),o=n(5185),s=n(5155),a=(e,t,n=[])=>{let a=(0,r.forwardRef)((e,r)=>{let a={},l={};Object.keys(e).forEach(t=>{n.includes(t)?a[t]=e[t]:l[t]=e[t]});let u=t(a)??void 0;return(0,s.jsx)(i.sG.button,{type:"button",...l,ref:r,disabled:l.disabled||!u,onClick:(0,o.mK)(l.onClick,u)})});return a.displayName=e,a}},8804:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("messages-square",[["path",{d:"M16 10a2 2 0 0 1-2 2H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.71.71 0 0 1 2 14.286V4a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z",key:"1n2ejm"}],["path",{d:"M20 9a2 2 0 0 1 2 2v10.286a.71.71 0 0 1-1.212.502l-2.202-2.202A2 2 0 0 0 17.172 19H10a2 2 0 0 1-2-2v-1",key:"1qfcsi"}]])},8832:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},8859:(e,t)=>{"use strict";function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(n,r(e));else t.set(n,r(i));return t}function o(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},8862:(e,t,n)=>{"use strict";n.d(t,{eD:()=>g});var r=n(2115),i=n(5455),o=n(5453),s=n(6882),a=n(9935),l=n(4229),u=n(5155),c=e=>{let{runtime:t,children:n}=e,i=(e=>{let[t]=(0,r.useState)(()=>(0,o.v)(()=>e));return(0,r.useEffect)(()=>{(0,l.Q)(e),(0,a.s)(t).setState(e,!0)},[e,t]),t})(t),c=(()=>{let[e]=(0,r.useState)(()=>(0,o.v)(e=>({isCopied:!1,setIsCopied:t=>{e({isCopied:t})},isHovering:!1,setIsHovering:t=>{e({isHovering:t})}})));return e})(),[h]=(0,r.useState)(()=>({useMessageRuntime:i,useMessageUtils:c}));return(0,u.jsx)(s.Nf.Provider,{value:h,children:n})},h=(e,t)=>e.Message===t.Message&&e.EditComposer===t.EditComposer&&e.UserEditComposer===t.UserEditComposer&&e.AssistantEditComposer===t.AssistantEditComposer&&e.SystemEditComposer===t.SystemEditComposer&&e.UserMessage===t.UserMessage&&e.AssistantMessage===t.AssistantMessage&&e.SystemMessage===t.SystemMessage,d=()=>null,f=e=>{let{components:t}=e,n=((e,t,n)=>{var r,i,o,s,a,l,u,c,h,f,p,m;switch(t){case"user":if(n)return null!=(o=null!=(i=null!=(r=e.UserEditComposer)?r:e.EditComposer)?i:e.UserMessage)?o:e.Message;return null!=(s=e.UserMessage)?s:e.Message;case"assistant":if(n)return null!=(u=null!=(l=null!=(a=e.AssistantEditComposer)?a:e.EditComposer)?l:e.AssistantMessage)?u:e.Message;return null!=(c=e.AssistantMessage)?c:e.Message;case"system":if(n)return null!=(p=null!=(f=null!=(h=e.SystemEditComposer)?h:e.EditComposer)?f:e.SystemMessage)?p:e.Message;return null!=(m=e.SystemMessage)?m:d;default:throw Error("Unknown message role: ".concat(t))}})(t,(0,s.JX)(e=>e.role),(0,s.iP)(e=>e.isEditing));return(0,u.jsx)(n,{})},p=(0,r.memo)(e=>{let{index:t,components:n}=e,o=(0,i.Mp)(),s=(0,r.useMemo)(()=>o.getMesssageByIndex(t),[o,t]);return(0,u.jsx)(c,{runtime:s,children:(0,u.jsx)(f,{components:n})})},(e,t)=>e.index===t.index&&h(e.components,t.components));p.displayName="ThreadPrimitive.MessageByIndex";var m=e=>{let{components:t}=e,n=(0,i._W)(e=>e.messages.length);return(0,r.useMemo)(()=>0===n?null:Array.from({length:n},(e,n)=>(0,u.jsx)(p,{index:n,components:t},n)),[n,t])};m.displayName="ThreadPrimitive.Messages";var g=(0,r.memo)(m,(e,t)=>h(e.components,t.components))},8905:(e,t,n)=>{"use strict";n.d(t,{C:()=>s});var r=n(2115),i=n(6101),o=n(2712),s=e=>{let{present:t,children:n}=e,s=function(e){var t,n;let[i,s]=r.useState(),l=r.useRef(null),u=r.useRef(e),c=r.useRef("none"),[h,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(l.current);c.current="mounted"===h?e:"none"},[h]),(0,o.N)(()=>{let t=l.current,n=u.current;if(n!==e){let r=c.current,i=a(t);e?d("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==i?d("ANIMATION_OUT"):d("UNMOUNT"),u.current=e}},[e,d]),(0,o.N)(()=>{if(i){var e;let t,n=null!=(e=i.ownerDocument.defaultView)?e:window,r=e=>{let r=a(l.current).includes(CSS.escape(e.animationName));if(e.target===i&&r&&(d("ANIMATION_END"),!u.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},o=e=>{e.target===i&&(c.current=a(l.current))};return i.addEventListener("animationstart",o),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{n.clearTimeout(t),i.removeEventListener("animationstart",o),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}d("ANIMATION_END")},[i,d]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,s(e)},[])}}(t),l="function"==typeof n?n({present:s.isPresent}):r.Children.only(n),u=(0,i.s)(s.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?e.ref:(i=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||s.isPresent?r.cloneElement(l,{ref:u}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}s.displayName="Presence"},8979:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]])},8997:(e,t,n)=>{"use strict";n.d(t,{l:()=>o});var r=n(6882),i=n(5155),o=()=>{let e=(0,r.JX)(e=>e.branchNumber);return(0,i.jsx)(i.Fragment,{children:e})};o.displayName="BranchPickerPrimitive.Number"},9022:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},9033:(e,t,n)=>{"use strict";n.d(t,{c:()=>i});var r=n(2115);function i(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9059:(e,t,n)=>{"use strict";n.d(t,{e:()=>M});var r=n(5185),i=n(6101),o=n(9708),s=n(2115);function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}var l=s.useLayoutEffect,u=function(e){var t=s.useRef(e);return l(function(){t.current=e}),t},c=function(e,t){if("function"==typeof e)return void e(t);e.current=t},h=function(e,t){var n=s.useRef();return s.useCallback(function(r){e.current=r,n.current&&c(n.current,null),n.current=t,t&&c(t,r)},[t])},d={"min-height":"0","max-height":"none",height:"0",visibility:"hidden",overflow:"hidden",position:"absolute","z-index":"-1000",top:"0",right:"0",display:"block"},f=function(e){Object.keys(d).forEach(function(t){e.style.setProperty(t,d[t],"important")})},p=null,m=function(e,t){var n=e.scrollHeight;return"border-box"===t.sizingStyle.boxSizing?n+t.borderSize:n-t.paddingSize},g=function(){},y=["borderBottomWidth","borderLeftWidth","borderRightWidth","borderTopWidth","boxSizing","fontFamily","fontSize","fontStyle","fontWeight","letterSpacing","lineHeight","paddingBottom","paddingLeft","paddingRight","paddingTop","tabSize","textIndent","textRendering","textTransform","width","wordBreak","wordSpacing","scrollbarGutter"],v=!!document.documentElement.currentStyle,b=function(e){var t=window.getComputedStyle(e);if(null===t)return null;var n=y.reduce(function(e,n){return e[n]=t[n],e},{}),r=n.boxSizing;if(""===r)return null;v&&"border-box"===r&&(n.width=parseFloat(n.width)+parseFloat(n.borderRightWidth)+parseFloat(n.borderLeftWidth)+parseFloat(n.paddingRight)+parseFloat(n.paddingLeft)+"px");var i=parseFloat(n.paddingBottom)+parseFloat(n.paddingTop),o=parseFloat(n.borderBottomWidth)+parseFloat(n.borderTopWidth);return{sizingStyle:n,paddingSize:i,borderSize:o}};function x(e,t,n){var r=u(n);s.useLayoutEffect(function(){var n=function(e){return r.current(e)};if(e)return e.addEventListener(t,n),function(){return e.removeEventListener(t,n)}},[])}var w=function(e,t){x(document.body,"reset",function(n){e.current.form===n.target&&t(n)})},k=function(e){x(window,"resize",e)},S=function(e){x(document.fonts,"loadingdone",e)},E=["cacheMeasurements","maxRows","minRows","onChange","onHeightChange"],T=s.forwardRef(function(e,t){var n=e.cacheMeasurements,r=e.maxRows,i=e.minRows,o=e.onChange,l=void 0===o?g:o,u=e.onHeightChange,c=void 0===u?g:u,d=function(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,E),y=void 0!==d.value,v=s.useRef(null),x=h(v,t),T=s.useRef(0),_=s.useRef(),C=function(){var e=v.current,t=n&&_.current?_.current:b(e);if(t){_.current=t;var o,s,a,l,u,h,d,g,y,x,w,k=(o=e.value||e.placeholder||"x",void 0===(s=i)&&(s=1),void 0===(a=r)&&(a=1/0),p||((p=document.createElement("textarea")).setAttribute("tabindex","-1"),p.setAttribute("aria-hidden","true"),f(p)),null===p.parentNode&&document.body.appendChild(p),l=t.paddingSize,u=t.borderSize,d=(h=t.sizingStyle).boxSizing,Object.keys(h).forEach(function(e){p.style[e]=h[e]}),f(p),p.value=o,g=m(p,t),p.value=o,g=m(p,t),p.value="x",x=(y=p.scrollHeight-l)*s,"border-box"===d&&(x=x+l+u),g=Math.max(x,g),w=y*a,"border-box"===d&&(w=w+l+u),[g=Math.min(w,g),y]),S=k[0],E=k[1];T.current!==S&&(T.current=S,e.style.setProperty("height",S+"px","important"),c(S,{rowHeight:E}))}};return s.useLayoutEffect(C),w(v,function(){if(!y){var e=v.current.value;requestAnimationFrame(function(){var t=v.current;t&&e!==t.value&&C()})}}),k(C),S(C),s.createElement("textarea",a({},d,{onChange:function(e){y||C(),l(e)},ref:x}))}),_=n(2605),C=n(5455),A=n(1595),I=n(1748),P=n(1386),R=n(5155),M=(0,s.forwardRef)((e,t)=>{let{autoFocus:n=!1,asChild:a,disabled:l,onChange:u,onKeyDown:c,onPaste:h,submitOnEnter:d=!0,cancelOnEscape:f=!0,unstable_focusOnRunStart:p=!0,unstable_focusOnScrollToBottom:m=!0,unstable_focusOnThreadSwitched:g=!0,addAttachmentOnPaste:y=!0,...v}=e,b=(0,P.pK)(),x=(0,C.Mp)(),w=(0,_.I)(),k=(0,_.s)(e=>e.isEditing?e.text:""),S=a?o.DX:T,E=!!((0,C._W)(e=>e.isDisabled)||l),M=(0,s.useRef)(null),O=(0,i.s)(t,M);(0,A.U)(e=>{f&&w.getState().canCancel&&(w.cancel(),e.preventDefault())});let D=async e=>{var t;if(!y)return;let n=x.getState().capabilities,r=Array.from((null==(t=e.clipboardData)?void 0:t.files)||[]);if(n.attachments&&r.length>0)try{e.preventDefault(),await Promise.all(r.map(e=>w.addAttachment(e)))}catch(e){console.error("Error adding attachment:",e)}},L=n&&!E,j=(0,s.useCallback)(()=>{let e=M.current;e&&L&&(e.focus({preventScroll:!0}),e.setSelectionRange(e.value.length,e.value.length))},[L]);return(0,s.useEffect)(()=>j(),[j]),(0,I.O)(()=>{"thread"===w.type&&m&&j()}),(0,s.useEffect)(()=>{if("thread"===w.type&&p)return x.unstable_on("run-start",j)},[p,j,w,x]),(0,s.useEffect)(()=>{if("thread"===w.type&&g)return b.unstable_on("switched-to",j)},[g,j,w,b]),(0,R.jsx)(S,{name:"input",value:k,...v,ref:O,disabled:E,onChange:(0,r.mK)(u,e=>{if(w.getState().isEditing)return w.setText(e.target.value)}),onKeyDown:(0,r.mK)(c,e=>{if(!E&&d&&!e.nativeEvent.isComposing&&"Enter"===e.key&&!1===e.shiftKey){let{isRunning:r}=x.getState();if(!r){var t,n;e.preventDefault(),null==(n=M.current)||null==(t=n.closest("form"))||t.requestSubmit()}}}),onPaste:(0,r.mK)(h,D)})});M.displayName="ComposerPrimitive.Input"},9099:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},9300:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function i(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=o(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return i.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=o(t,n));return t}(n)))}return e}function o(e,t){return t?e?e+" "+t:e+t:e}e.exports?(i.default=i,e.exports=i):void 0===(n=(function(){return i}).apply(t,[]))||(e.exports=n)}()},9381:(e,t,n)=>{"use strict";n.d(t,{y:()=>o});var r=n(1603);let i={}.hasOwnProperty;function o(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let o,s=(i.call(e,n)?e[n]:void 0)||(e[n]={}),a=t[n];if(a)for(o in a){i.call(s,o)||(s[o]=[]);let e=a[o];!function(e,t){let n=-1,i=[];for(;++n<t.length;)("after"===t[n].add?e:i).push(t[n]);(0,r.m)(e,0,0,i)}(s[o],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}},9535:(e,t,n)=>{"use strict";n.d(t,{S:()=>i});var r=n(2556);function i(e){return null===e||(0,r.Ee)(e)||(0,r.Ny)(e)?1:(0,r.es)(e)?2:void 0}},9688:(e,t,n)=>{"use strict";n.d(t,{QP:()=>ee});let r=(e,t)=>{if(0===e.length)return t.classGroupId;let n=e[0],i=t.nextPart.get(n),o=i?r(e.slice(1),i):void 0;if(o)return o;if(0===t.validators.length)return;let s=e.join("-");return t.validators.find(({validator:e})=>e(s))?.classGroupId},i=/^\[(.+)\]$/,o=(e,t,n,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:s(t,e)).classGroupId=n;return}if("function"==typeof e)return a(e)?void o(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(([e,i])=>{o(i,s(t,e),n,r)})})},s=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},a=e=>e.isThemeGetter,l=/\s+/;function u(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=c(e))&&(r&&(r+=" "),r+=t);return r}let c=e=>{let t;if("string"==typeof e)return e;let n="";for(let r=0;r<e.length;r++)e[r]&&(t=c(e[r]))&&(n&&(n+=" "),n+=t);return n},h=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},d=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,f=/^\((?:(\w[\w-]*):)?(.+)\)$/i,p=/^\d+\/\d+$/,m=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,y=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,v=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,b=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,x=e=>p.test(e),w=e=>!!e&&!Number.isNaN(Number(e)),k=e=>!!e&&Number.isInteger(Number(e)),S=e=>e.endsWith("%")&&w(e.slice(0,-1)),E=e=>m.test(e),T=()=>!0,_=e=>g.test(e)&&!y.test(e),C=()=>!1,A=e=>v.test(e),I=e=>b.test(e),P=e=>!M(e)&&!N(e),R=e=>$(e,X,C),M=e=>d.test(e),O=e=>$(e,G,_),D=e=>$(e,J,w),L=e=>$(e,K,C),j=e=>$(e,Y,I),B=e=>$(e,Q,A),N=e=>f.test(e),F=e=>q(e,G),z=e=>q(e,Z),U=e=>q(e,K),V=e=>q(e,X),H=e=>q(e,Y),W=e=>q(e,Q,!0),$=(e,t,n)=>{let r=d.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},q=(e,t,n=!1)=>{let r=f.exec(e);return!!r&&(r[1]?t(r[1]):n)},K=e=>"position"===e||"percentage"===e,Y=e=>"image"===e||"url"===e,X=e=>"length"===e||"size"===e||"bg-size"===e,G=e=>"length"===e,J=e=>"number"===e,Z=e=>"family-name"===e,Q=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...t){let n,s,a,c=function(l){let u;return s=(n={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,r=new Map,i=(i,o)=>{n.set(i,o),++t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(i(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):i(e,t)}}})((u=t.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:n}=e,r=e=>{let t,n,r=[],i=0,o=0,s=0;for(let n=0;n<e.length;n++){let a=e[n];if(0===i&&0===o){if(":"===a){r.push(e.slice(s,n)),s=n+1;continue}if("/"===a){t=n;continue}}"["===a?i++:"]"===a?i--:"("===a?o++:")"===a&&o--}let a=0===r.length?e:e.substring(s),l=(n=a).endsWith("!")?n.substring(0,n.length-1):n.startsWith("!")?n.substring(1):n;return{modifiers:r,hasImportantModifier:l!==a,baseClassName:l,maybePostfixModifierPosition:t&&t>s?t-s:void 0}};if(t){let e=t+":",n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){let e=r;r=t=>n({className:t,parseClassName:e})}return r})(u),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let n=[],r=[];return e.forEach(e=>{"["===e[0]||t[e]?(n.push(...r.sort(),e),r=[]):r.push(e)}),n.push(...r.sort()),n}})(u),...(e=>{let t=(e=>{let{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(let e in n)o(n[e],r,e,t);return r})(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:s}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),r(n,t)||(e=>{if(i.test(e)){let t=i.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}})(e)},getConflictingClassGroupIds:(e,t)=>{let r=n[e]||[];return t&&s[e]?[...r,...s[e]]:r}}})(u)}).cache.get,a=n.cache.set,c=h,h(l)};function h(e){let t=s(e);if(t)return t;let r=((e,t)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:i,sortModifiers:o}=t,s=[],a=e.trim().split(l),u="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{isExternal:l,modifiers:c,hasImportantModifier:h,baseClassName:d,maybePostfixModifierPosition:f}=n(t);if(l){u=t+(u.length>0?" "+u:u);continue}let p=!!f,m=r(p?d.substring(0,f):d);if(!m){if(!p||!(m=r(d))){u=t+(u.length>0?" "+u:u);continue}p=!1}let g=o(c).join(":"),y=h?g+"!":g,v=y+m;if(s.includes(v))continue;s.push(v);let b=i(m,p);for(let e=0;e<b.length;++e){let t=b[e];s.push(y+t)}u=t+(u.length>0?" "+u:u)}return u})(e,n);return a(e,r),r}return function(){return c(u.apply(null,arguments))}}(()=>{let e=h("color"),t=h("font"),n=h("text"),r=h("font-weight"),i=h("tracking"),o=h("leading"),s=h("breakpoint"),a=h("container"),l=h("spacing"),u=h("radius"),c=h("shadow"),d=h("inset-shadow"),f=h("text-shadow"),p=h("drop-shadow"),m=h("blur"),g=h("perspective"),y=h("aspect"),v=h("ease"),b=h("animate"),_=()=>["auto","avoid","all","avoid-page","page","left","right","column"],C=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],A=()=>[...C(),N,M],I=()=>["auto","hidden","clip","visible","scroll"],$=()=>["auto","contain","none"],q=()=>[N,M,l],K=()=>[x,"full","auto",...q()],Y=()=>[k,"none","subgrid",N,M],X=()=>["auto",{span:["full",k,N,M]},k,N,M],G=()=>[k,"auto",N,M],J=()=>["auto","min","max","fr",N,M],Z=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Q=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...q()],et=()=>[x,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...q()],en=()=>[e,N,M],er=()=>[...C(),U,L,{position:[N,M]}],ei=()=>["no-repeat",{repeat:["","x","y","space","round"]}],eo=()=>["auto","cover","contain",V,R,{size:[N,M]}],es=()=>[S,F,O],ea=()=>["","none","full",u,N,M],el=()=>["",w,F,O],eu=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eh=()=>[w,S,U,L],ed=()=>["","none",m,N,M],ef=()=>["none",w,N,M],ep=()=>["none",w,N,M],em=()=>[w,N,M],eg=()=>[x,"full",...q()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[E],breakpoint:[E],color:[T],container:[E],"drop-shadow":[E],ease:["in","out","in-out"],font:[P],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[E],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[E],shadow:[E],spacing:["px",w],text:[E],"text-shadow":[E],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",x,M,N,y]}],container:["container"],columns:[{columns:[w,M,N,a]}],"break-after":[{"break-after":_()}],"break-before":[{"break-before":_()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:A()}],overflow:[{overflow:I()}],"overflow-x":[{"overflow-x":I()}],"overflow-y":[{"overflow-y":I()}],overscroll:[{overscroll:$()}],"overscroll-x":[{"overscroll-x":$()}],"overscroll-y":[{"overscroll-y":$()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:K()}],"inset-x":[{"inset-x":K()}],"inset-y":[{"inset-y":K()}],start:[{start:K()}],end:[{end:K()}],top:[{top:K()}],right:[{right:K()}],bottom:[{bottom:K()}],left:[{left:K()}],visibility:["visible","invisible","collapse"],z:[{z:[k,"auto",N,M]}],basis:[{basis:[x,"full","auto",a,...q()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[w,x,"auto","initial","none",M]}],grow:[{grow:["",w,N,M]}],shrink:[{shrink:["",w,N,M]}],order:[{order:[k,"first","last","none",N,M]}],"grid-cols":[{"grid-cols":Y()}],"col-start-end":[{col:X()}],"col-start":[{"col-start":G()}],"col-end":[{"col-end":G()}],"grid-rows":[{"grid-rows":Y()}],"row-start-end":[{row:X()}],"row-start":[{"row-start":G()}],"row-end":[{"row-end":G()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":J()}],"auto-rows":[{"auto-rows":J()}],gap:[{gap:q()}],"gap-x":[{"gap-x":q()}],"gap-y":[{"gap-y":q()}],"justify-content":[{justify:[...Z(),"normal"]}],"justify-items":[{"justify-items":[...Q(),"normal"]}],"justify-self":[{"justify-self":["auto",...Q()]}],"align-content":[{content:["normal",...Z()]}],"align-items":[{items:[...Q(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Q(),{baseline:["","last"]}]}],"place-content":[{"place-content":Z()}],"place-items":[{"place-items":[...Q(),"baseline"]}],"place-self":[{"place-self":["auto",...Q()]}],p:[{p:q()}],px:[{px:q()}],py:[{py:q()}],ps:[{ps:q()}],pe:[{pe:q()}],pt:[{pt:q()}],pr:[{pr:q()}],pb:[{pb:q()}],pl:[{pl:q()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":q()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":q()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[a,"screen",...et()]}],"min-w":[{"min-w":[a,"screen","none",...et()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[s]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",n,F,O]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,N,D]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",S,M]}],"font-family":[{font:[z,M,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,N,M]}],"line-clamp":[{"line-clamp":[w,"none",N,D]}],leading:[{leading:[o,...q()]}],"list-image":[{"list-image":["none",N,M]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",N,M]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:en()}],"text-color":[{text:en()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[w,"from-font","auto",N,O]}],"text-decoration-color":[{decoration:en()}],"underline-offset":[{"underline-offset":[w,"auto",N,M]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:q()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",N,M]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",N,M]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:er()}],"bg-repeat":[{bg:ei()}],"bg-size":[{bg:eo()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},k,N,M],radial:["",N,M],conic:[k,N,M]},H,j]}],"bg-color":[{bg:en()}],"gradient-from-pos":[{from:es()}],"gradient-via-pos":[{via:es()}],"gradient-to-pos":[{to:es()}],"gradient-from":[{from:en()}],"gradient-via":[{via:en()}],"gradient-to":[{to:en()}],rounded:[{rounded:ea()}],"rounded-s":[{"rounded-s":ea()}],"rounded-e":[{"rounded-e":ea()}],"rounded-t":[{"rounded-t":ea()}],"rounded-r":[{"rounded-r":ea()}],"rounded-b":[{"rounded-b":ea()}],"rounded-l":[{"rounded-l":ea()}],"rounded-ss":[{"rounded-ss":ea()}],"rounded-se":[{"rounded-se":ea()}],"rounded-ee":[{"rounded-ee":ea()}],"rounded-es":[{"rounded-es":ea()}],"rounded-tl":[{"rounded-tl":ea()}],"rounded-tr":[{"rounded-tr":ea()}],"rounded-br":[{"rounded-br":ea()}],"rounded-bl":[{"rounded-bl":ea()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:en()}],"border-color-x":[{"border-x":en()}],"border-color-y":[{"border-y":en()}],"border-color-s":[{"border-s":en()}],"border-color-e":[{"border-e":en()}],"border-color-t":[{"border-t":en()}],"border-color-r":[{"border-r":en()}],"border-color-b":[{"border-b":en()}],"border-color-l":[{"border-l":en()}],"divide-color":[{divide:en()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[w,N,M]}],"outline-w":[{outline:["",w,F,O]}],"outline-color":[{outline:en()}],shadow:[{shadow:["","none",c,W,B]}],"shadow-color":[{shadow:en()}],"inset-shadow":[{"inset-shadow":["none",d,W,B]}],"inset-shadow-color":[{"inset-shadow":en()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:en()}],"ring-offset-w":[{"ring-offset":[w,O]}],"ring-offset-color":[{"ring-offset":en()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":en()}],"text-shadow":[{"text-shadow":["none",f,W,B]}],"text-shadow-color":[{"text-shadow":en()}],opacity:[{opacity:[w,N,M]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[w]}],"mask-image-linear-from-pos":[{"mask-linear-from":eh()}],"mask-image-linear-to-pos":[{"mask-linear-to":eh()}],"mask-image-linear-from-color":[{"mask-linear-from":en()}],"mask-image-linear-to-color":[{"mask-linear-to":en()}],"mask-image-t-from-pos":[{"mask-t-from":eh()}],"mask-image-t-to-pos":[{"mask-t-to":eh()}],"mask-image-t-from-color":[{"mask-t-from":en()}],"mask-image-t-to-color":[{"mask-t-to":en()}],"mask-image-r-from-pos":[{"mask-r-from":eh()}],"mask-image-r-to-pos":[{"mask-r-to":eh()}],"mask-image-r-from-color":[{"mask-r-from":en()}],"mask-image-r-to-color":[{"mask-r-to":en()}],"mask-image-b-from-pos":[{"mask-b-from":eh()}],"mask-image-b-to-pos":[{"mask-b-to":eh()}],"mask-image-b-from-color":[{"mask-b-from":en()}],"mask-image-b-to-color":[{"mask-b-to":en()}],"mask-image-l-from-pos":[{"mask-l-from":eh()}],"mask-image-l-to-pos":[{"mask-l-to":eh()}],"mask-image-l-from-color":[{"mask-l-from":en()}],"mask-image-l-to-color":[{"mask-l-to":en()}],"mask-image-x-from-pos":[{"mask-x-from":eh()}],"mask-image-x-to-pos":[{"mask-x-to":eh()}],"mask-image-x-from-color":[{"mask-x-from":en()}],"mask-image-x-to-color":[{"mask-x-to":en()}],"mask-image-y-from-pos":[{"mask-y-from":eh()}],"mask-image-y-to-pos":[{"mask-y-to":eh()}],"mask-image-y-from-color":[{"mask-y-from":en()}],"mask-image-y-to-color":[{"mask-y-to":en()}],"mask-image-radial":[{"mask-radial":[N,M]}],"mask-image-radial-from-pos":[{"mask-radial-from":eh()}],"mask-image-radial-to-pos":[{"mask-radial-to":eh()}],"mask-image-radial-from-color":[{"mask-radial-from":en()}],"mask-image-radial-to-color":[{"mask-radial-to":en()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":C()}],"mask-image-conic-pos":[{"mask-conic":[w]}],"mask-image-conic-from-pos":[{"mask-conic-from":eh()}],"mask-image-conic-to-pos":[{"mask-conic-to":eh()}],"mask-image-conic-from-color":[{"mask-conic-from":en()}],"mask-image-conic-to-color":[{"mask-conic-to":en()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:er()}],"mask-repeat":[{mask:ei()}],"mask-size":[{mask:eo()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",N,M]}],filter:[{filter:["","none",N,M]}],blur:[{blur:ed()}],brightness:[{brightness:[w,N,M]}],contrast:[{contrast:[w,N,M]}],"drop-shadow":[{"drop-shadow":["","none",p,W,B]}],"drop-shadow-color":[{"drop-shadow":en()}],grayscale:[{grayscale:["",w,N,M]}],"hue-rotate":[{"hue-rotate":[w,N,M]}],invert:[{invert:["",w,N,M]}],saturate:[{saturate:[w,N,M]}],sepia:[{sepia:["",w,N,M]}],"backdrop-filter":[{"backdrop-filter":["","none",N,M]}],"backdrop-blur":[{"backdrop-blur":ed()}],"backdrop-brightness":[{"backdrop-brightness":[w,N,M]}],"backdrop-contrast":[{"backdrop-contrast":[w,N,M]}],"backdrop-grayscale":[{"backdrop-grayscale":["",w,N,M]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w,N,M]}],"backdrop-invert":[{"backdrop-invert":["",w,N,M]}],"backdrop-opacity":[{"backdrop-opacity":[w,N,M]}],"backdrop-saturate":[{"backdrop-saturate":[w,N,M]}],"backdrop-sepia":[{"backdrop-sepia":["",w,N,M]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":q()}],"border-spacing-x":[{"border-spacing-x":q()}],"border-spacing-y":[{"border-spacing-y":q()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",N,M]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[w,"initial",N,M]}],ease:[{ease:["linear","initial",v,N,M]}],delay:[{delay:[w,N,M]}],animate:[{animate:["none",b,N,M]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,N,M]}],"perspective-origin":[{"perspective-origin":A()}],rotate:[{rotate:ef()}],"rotate-x":[{"rotate-x":ef()}],"rotate-y":[{"rotate-y":ef()}],"rotate-z":[{"rotate-z":ef()}],scale:[{scale:ep()}],"scale-x":[{"scale-x":ep()}],"scale-y":[{"scale-y":ep()}],"scale-z":[{"scale-z":ep()}],"scale-3d":["scale-3d"],skew:[{skew:em()}],"skew-x":[{"skew-x":em()}],"skew-y":[{"skew-y":em()}],transform:[{transform:[N,M,"","none","gpu","cpu"]}],"transform-origin":[{origin:A()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:en()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:en()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",N,M]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":q()}],"scroll-mx":[{"scroll-mx":q()}],"scroll-my":[{"scroll-my":q()}],"scroll-ms":[{"scroll-ms":q()}],"scroll-me":[{"scroll-me":q()}],"scroll-mt":[{"scroll-mt":q()}],"scroll-mr":[{"scroll-mr":q()}],"scroll-mb":[{"scroll-mb":q()}],"scroll-ml":[{"scroll-ml":q()}],"scroll-p":[{"scroll-p":q()}],"scroll-px":[{"scroll-px":q()}],"scroll-py":[{"scroll-py":q()}],"scroll-ps":[{"scroll-ps":q()}],"scroll-pe":[{"scroll-pe":q()}],"scroll-pt":[{"scroll-pt":q()}],"scroll-pr":[{"scroll-pr":q()}],"scroll-pb":[{"scroll-pb":q()}],"scroll-pl":[{"scroll-pl":q()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",N,M]}],fill:[{fill:["none",...en()]}],"stroke-w":[{stroke:[w,F,O,D]}],stroke:[{stroke:["none",...en()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9708:(e,t,n)=>{"use strict";n.d(t,{DX:()=>a,Dc:()=>u,TL:()=>s});var r=n(2115),i=n(6101),o=n(5155);function s(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var s;let e,a,l=(s=n,(a=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(a=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),u=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{let t=o(...e);return i(...e),t}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(u.ref=t?(0,i.t)(t,l):l),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...s}=e,a=r.Children.toArray(i),l=a.find(c);if(l){let e=l.props.children,i=a.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...s,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...s,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}var a=s("Slot"),l=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=l,t}function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},9881:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},9912:(e,t,n)=>{"use strict";n.d(t,{Y_:()=>s,x1:()=>o});var r=n(2115),i=n(5155),o=(e,t)=>((e,t)=>{if(!e||!t)return!1;let n=e=>{let{position:t,data:n,...r}=e||{};return r};return JSON.stringify(n(e.properties))===JSON.stringify(n(t.properties))&&((e,t)=>"string"==typeof e?e===t:JSON.stringify(e)===JSON.stringify(t))(e.children,t.children)})(e.node,t.node),s=(e={})=>Object.fromEntries(Object.entries(e??{}).map(([e,t])=>t?[e,(0,r.memo)(({node:e,...n})=>(0,i.jsx)(t,{...n}),o)]:[e,t]))},9917:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},9935:(e,t,n)=>{"use strict";n.d(t,{s:()=>r});var r=e=>e},9946:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(2115);let i=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:c,iconNode:h,...d}=e;return(0,r.createElement)("svg",{ref:t,...s,width:i,height:i,stroke:n,strokeWidth:l?24*Number(a)/Number(i):a,className:o("lucide",u),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(d)&&{"aria-hidden":"true"},...d},[...h.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),l=(e,t)=>{let n=(0,r.forwardRef)((n,s)=>{let{className:l,...u}=n;return(0,r.createElement)(a,{ref:s,iconNode:t,className:o("lucide-".concat(i(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),l),...u})});return n.displayName=i(e),n}},9954:(e,t,n)=>{"use strict";n.d(t,{o:()=>f});var r=n(2115),i=n(7119),o=n(5453),s=n(5455),a=n(9935),l=n(3357),u=n(4229),c=n(7788),h=n(5155),d=e=>{let{children:t,listItemRuntime:n,runtime:i}=e,d=(e=>{let[t]=(0,r.useState)(()=>(0,o.v)(()=>e));return(0,r.useEffect)(()=>{(0,u.Q)(e),(0,u.Q)(e.composer),(0,a.s)(t).setState(e,!0)},[e,t]),t})(i),[f]=(0,r.useState)(()=>({useThreadRuntime:d}));return(0,h.jsx)(l.X,{runtime:n,children:(0,h.jsx)(s.qm.Provider,{value:f,children:(0,h.jsx)(c.U,{children:t})})})},f=(0,r.memo)(e=>{let{children:t,runtime:n}=e,s=(e=>{let[t]=(0,r.useState)(()=>(0,o.v)(()=>e));return(0,r.useEffect)(()=>{(0,u.Q)(e),(0,u.Q)(e.threads),(0,a.s)(t).setState(e,!0)},[e,t]),t})(n),l=(0,r.useMemo)(()=>(0,o.v)(e=>{let t=new Map;return Object.freeze({getToolUI:e=>{let n=t.get(e);return n?.at(-1)||null},setToolUI:(n,r)=>{let i=t.get(n);return i||(i=[],t.set(n,i)),i.push(r),e({}),()=>{let t=i.indexOf(r);-1!==t&&i.splice(t,1),t===i.length&&e({})}}})}),[]),[c]=(0,r.useState)(()=>({useToolUIs:l,useAssistantRuntime:s})),f=(e=>{var t;return null==(t=e._core)?void 0:t.RenderComponent})(n);return(0,h.jsxs)(i.WF.Provider,{value:c,children:[f&&(0,h.jsx)(f,{}),(0,h.jsx)(d,{runtime:n.thread,listItemRuntime:n.threads.mainItem,children:t})]})})},9991:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return f},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return a},isAbsoluteUrl:function(){return o},isResSent:function(){return u},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,i=Array(r),o=0;o<r;o++)i[o]=arguments[o];return n||(n=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function a(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function h(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await h(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,f=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);