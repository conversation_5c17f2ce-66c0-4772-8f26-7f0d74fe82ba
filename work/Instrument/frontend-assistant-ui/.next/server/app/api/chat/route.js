(()=>{var a={};a.id=276,a.ids=[276],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},4870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6487:()=>{},6559:(a,b,c)=>{"use strict";a.exports=c(4870)},6946:(a,b,c)=>{"use strict";Object.defineProperty(b,"I",{enumerable:!0,get:function(){return g}});let d=c(898),e=c(2471),f=c(7912);async function g(a,b,c,g){if((0,d.isNodeNextResponse)(b)){var h;b.statusCode=c.status,b.statusMessage=c.statusText;let d=["set-cookie","www-authenticate","proxy-authenticate","vary"];null==(h=c.headers)||h.forEach((a,c)=>{if("x-middleware-set-cookie"!==c.toLowerCase())if("set-cookie"===c.toLowerCase())for(let d of(0,f.splitCookiesString)(a))b.appendHeader(c,d);else{let e=void 0!==b.getHeader(c);(d.includes(c.toLowerCase())||!e)&&b.appendHeader(c,a)}});let{originalResponse:i}=b;c.body&&"HEAD"!==a.method?await (0,e.pipeToNodeResponse)(c.body,i,g):i.end()}}},8335:()=>{},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9808:(a,b,c)=>{"use strict";let d,e;c.r(b),c.d(b,{handler:()=>kx,patchFetch:()=>kw,routeModule:()=>ks,serverHooks:()=>kv,workAsyncStorage:()=>kt,workUnitAsyncStorage:()=>ku});var f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U={};c.r(U),c.d(U,{POST:()=>kr});var V=c(6559),W=c(8088),X=c(7719),Y=c(6191),Z=c(1289),$=c(261),_=c(2603),aa=c(9893),ab=c(4823),ac=c(7220),ad=c(6946),ae=c(7912),af=c(9786),ag=c(6143),ah=c(6439),ai=c(3365),aj="vercel.ai.error",ak=Symbol.for(aj),al=class a extends Error{constructor({name:a,message:b,cause:c}){super(b),this[f]=!0,this.name=a,this.cause=c}static isInstance(b){return a.hasMarker(b,aj)}static hasMarker(a,b){let c=Symbol.for(b);return null!=a&&"object"==typeof a&&c in a&&"boolean"==typeof a[c]&&!0===a[c]}};f=ak;var am=al,an="AI_APICallError",ao=`vercel.ai.error.${an}`,ap=Symbol.for(ao),aq=class extends am{constructor({message:a,url:b,requestBodyValues:c,statusCode:d,responseHeaders:e,responseBody:f,cause:h,isRetryable:i=null!=d&&(408===d||409===d||429===d||d>=500),data:j}){super({name:an,message:a,cause:h}),this[g]=!0,this.url=b,this.requestBodyValues=c,this.statusCode=d,this.responseHeaders=e,this.responseBody=f,this.isRetryable=i,this.data=j}static isInstance(a){return am.hasMarker(a,ao)}};g=ap;var ar="AI_EmptyResponseBodyError",as=`vercel.ai.error.${ar}`,at=Symbol.for(as),au=class extends am{constructor({message:a="Empty response body"}={}){super({name:ar,message:a}),this[h]=!0}static isInstance(a){return am.hasMarker(a,as)}};function av(a){return null==a?"unknown error":"string"==typeof a?a:a instanceof Error?a.message:JSON.stringify(a)}h=at;var aw="AI_InvalidArgumentError",ax=`vercel.ai.error.${aw}`,ay=Symbol.for(ax),az=class extends am{constructor({message:a,cause:b,argument:c}){super({name:aw,message:a,cause:b}),this[i]=!0,this.argument=c}static isInstance(a){return am.hasMarker(a,ax)}};i=ay;var aA="AI_InvalidPromptError",aB=`vercel.ai.error.${aA}`,aC=Symbol.for(aB),aD=class extends am{constructor({prompt:a,message:b,cause:c}){super({name:aA,message:`Invalid prompt: ${b}`,cause:c}),this[j]=!0,this.prompt=a}static isInstance(a){return am.hasMarker(a,aB)}};j=aC;var aE="AI_InvalidResponseDataError",aF=`vercel.ai.error.${aE}`,aG=Symbol.for(aF),aH=class extends am{constructor({data:a,message:b=`Invalid response data: ${JSON.stringify(a)}.`}){super({name:aE,message:b}),this[k]=!0,this.data=a}static isInstance(a){return am.hasMarker(a,aF)}};k=aG;var aI="AI_JSONParseError",aJ=`vercel.ai.error.${aI}`,aK=Symbol.for(aJ),aL=class extends am{constructor({text:a,cause:b}){super({name:aI,message:`JSON parsing failed: Text: ${a}.
Error message: ${av(b)}`,cause:b}),this[l]=!0,this.text=a}static isInstance(a){return am.hasMarker(a,aJ)}};l=aK;var aM="AI_LoadAPIKeyError",aN=`vercel.ai.error.${aM}`,aO=Symbol.for(aN),aP=class extends am{constructor({message:a}){super({name:aM,message:a}),this[m]=!0}static isInstance(a){return am.hasMarker(a,aN)}};m=aO,Symbol.for("vercel.ai.error.AI_LoadSettingError"),Symbol.for("vercel.ai.error.AI_NoContentGeneratedError");var aQ="AI_NoSuchModelError",aR=`vercel.ai.error.${aQ}`,aS=Symbol.for(aR),aT=class extends am{constructor({errorName:a=aQ,modelId:b,modelType:c,message:d=`No such ${c}: ${b}`}){super({name:a,message:d}),this[n]=!0,this.modelId=b,this.modelType=c}static isInstance(a){return am.hasMarker(a,aR)}};n=aS;var aU="AI_TooManyEmbeddingValuesForCallError",aV=`vercel.ai.error.${aU}`,aW=Symbol.for(aV),aX=class extends am{constructor(a){super({name:aU,message:`Too many values for a single embedding call. The ${a.provider} model "${a.modelId}" can only embed up to ${a.maxEmbeddingsPerCall} values per call, but ${a.values.length} values were provided.`}),this[o]=!0,this.provider=a.provider,this.modelId=a.modelId,this.maxEmbeddingsPerCall=a.maxEmbeddingsPerCall,this.values=a.values}static isInstance(a){return am.hasMarker(a,aV)}};o=aW;var aY="AI_TypeValidationError",aZ=`vercel.ai.error.${aY}`,a$=Symbol.for(aZ),a_=class a extends am{constructor({value:a,cause:b}){super({name:aY,message:`Type validation failed: Value: ${JSON.stringify(a)}.
Error message: ${av(b)}`,cause:b}),this[p]=!0,this.value=a}static isInstance(a){return am.hasMarker(a,aZ)}static wrap({value:b,cause:c}){return a.isInstance(c)&&c.value===b?c:new a({value:b,cause:c})}};p=a$;var a0="AI_UnsupportedFunctionalityError",a1=`vercel.ai.error.${a0}`,a2=Symbol.for(a1),a3=class extends am{constructor({functionality:a,message:b=`'${a}' functionality not supported.`}){super({name:a0,message:b}),this[q]=!0,this.functionality=a}static isInstance(a){return am.hasMarker(a,a1)}};q=a2;class a4 extends Error{constructor(a,b){super(a),this.name="ParseError",this.type=b.type,this.field=b.field,this.value=b.value,this.line=b.line}}function a5(a){}class a6 extends TransformStream{constructor({onError:a,onRetry:b,onComment:c}={}){let d;super({start(e){d=function(a){if("function"==typeof a)throw TypeError("`callbacks` must be an object, got a function instead. Did you mean `{onEvent: fn}`?");let{onEvent:b=a5,onError:c=a5,onRetry:d=a5,onComment:e}=a,f="",g=!0,h,i="",j="";function k(a){if(""===a)return void(i.length>0&&b({id:h,event:j||void 0,data:i.endsWith(`
`)?i.slice(0,-1):i}),h=void 0,i="",j="");if(a.startsWith(":")){e&&e(a.slice(a.startsWith(": ")?2:1));return}let c=a.indexOf(":");if(-1!==c){let b=a.slice(0,c),d=" "===a[c+1]?2:1;l(b,a.slice(c+d),a);return}l(a,"",a)}function l(a,b,e){switch(a){case"event":j=b;break;case"data":i=`${i}${b}
`;break;case"id":h=b.includes("\0")?void 0:b;break;case"retry":/^\d+$/.test(b)?d(parseInt(b,10)):c(new a4(`Invalid \`retry\` value: "${b}"`,{type:"invalid-retry",value:b,line:e}));break;default:c(new a4(`Unknown field "${a.length>20?`${a.slice(0,20)}\u2026`:a}"`,{type:"unknown-field",field:a,value:b,line:e}))}}return{feed:function(a){let b=g?a.replace(/^\xEF\xBB\xBF/,""):a,[c,d]=function(a){let b=[],c="",d=0;for(;d<a.length;){let e=a.indexOf("\r",d),f=a.indexOf(`
`,d),g=-1;if(-1!==e&&-1!==f?g=Math.min(e,f):-1!==e?g=e===a.length-1?-1:e:-1!==f&&(g=f),-1===g){c=a.slice(d);break}{let c=a.slice(d,g);b.push(c),"\r"===a[(d=g+1)-1]&&a[d]===`
`&&d++}}return[b,c]}(`${f}${b}`);for(let a of c)k(a);f=d,g=!1},reset:function(a={}){f&&a.consume&&k(f),g=!0,h=void 0,i="",j="",f=""}}}({onEvent:a=>{e.enqueue(a)},onError(b){"terminate"===a?e.error(b):"function"==typeof a&&a(b)},onRetry:b,onComment:c})},transform(a){d.feed(a)}})}}Symbol("ZodOutput"),Symbol("ZodInput");class a7{constructor(){this._map=new Map,this._idmap=new Map}add(a,...b){let c=b[0];if(this._map.set(a,c),c&&"object"==typeof c&&"id"in c){if(this._idmap.has(c.id))throw Error(`ID ${c.id} already exists in the registry`);this._idmap.set(c.id,a)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(a){let b=this._map.get(a);return b&&"object"==typeof b&&"id"in b&&this._idmap.delete(b.id),this._map.delete(a),this}get(a){let b=a._zod.parent;if(b){let c={...this.get(b)??{}};return delete c.id,{...c,...this._map.get(a)}}return this._map.get(a)}has(a){return this._map.has(a)}}let a8=new a7;function a9(a){let b=Object.values(a).filter(a=>"number"==typeof a);return Object.entries(a).filter(([a,c])=>-1===b.indexOf(+a)).map(([a,b])=>b)}function ba(a,b){return"bigint"==typeof b?b.toString():b}function bb(a){return{get value(){{let b=a();return Object.defineProperty(this,"value",{value:b}),b}}}}function bc(a){let b=+!!a.startsWith("^"),c=a.endsWith("$")?a.length-1:a.length;return a.slice(b,c)}function bd(a,b,c){Object.defineProperty(a,b,{get(){{let d=c();return a[b]=d,d}},set(c){Object.defineProperty(a,b,{value:c})},configurable:!0})}function be(a,b,c){Object.defineProperty(a,b,{value:c,writable:!0,enumerable:!0,configurable:!0})}function bf(a){return JSON.stringify(a)}let bg=Error.captureStackTrace?Error.captureStackTrace:(...a)=>{};function bh(a){return"object"==typeof a&&null!==a&&!Array.isArray(a)}let bi=bb(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(a){return!1}});function bj(a){if(!1===bh(a))return!1;let b=a.constructor;if(void 0===b)return!0;let c=b.prototype;return!1!==bh(c)&&!1!==Object.prototype.hasOwnProperty.call(c,"isPrototypeOf")}let bk=new Set(["string","number","symbol"]);function bl(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function bm(a,b,c){let d=new a._zod.constr(b??a._zod.def);return(!b||c?.parent)&&(d._zod.parent=a),d}function bn(a){if(!a)return{};if("string"==typeof a)return{error:()=>a};if(a?.message!==void 0){if(a?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");a.error=a.message}return(delete a.message,"string"==typeof a.error)?{...a,error:()=>a.error}:a}let bo={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-0x80000000,0x7fffffff],uint32:[0,0xffffffff],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function bp(a,b=0){for(let c=b;c<a.issues.length;c++)if(a.issues[c]?.continue!==!0)return!0;return!1}function bq(a,b){return b.map(b=>(b.path??(b.path=[]),b.path.unshift(a),b))}function br(a){return"string"==typeof a?a:a?.message}function bs(a,b,c){let d={...a,path:a.path??[]};return a.message||(d.message=br(a.inst?._zod.def?.error?.(a))??br(b?.error?.(a))??br(c.customError?.(a))??br(c.localeError?.(a))??"Invalid input"),delete d.inst,delete d.continue,b?.reportInput||delete d.input,d}function bt(a){return Array.isArray(a)?"array":"string"==typeof a?"string":"unknown"}function bu(...a){let[b,c,d]=a;return"string"==typeof b?{message:b,code:"custom",input:c,inst:d}:{...b}}class bv{constructor(a){this.counter=0,this.metadataRegistry=a?.metadata??a8,this.target=a?.target??"draft-2020-12",this.unrepresentable=a?.unrepresentable??"throw",this.override=a?.override??(()=>{}),this.io=a?.io??"output",this.seen=new Map}process(a,b={path:[],schemaPath:[]}){var c;let d=a._zod.def,e=this.seen.get(a);if(e)return e.count++,b.schemaPath.includes(a)&&(e.cycle=b.path),e.schema;let f={schema:{},count:1,cycle:void 0,path:b.path};this.seen.set(a,f);let g=a._zod.toJSONSchema?.();if(g)f.schema=g;else{let c={...b,schemaPath:[...b.schemaPath,a],path:b.path},e=a._zod.parent;if(e)f.ref=e,this.process(e,c),this.seen.get(e).isParent=!0;else{let b=f.schema;switch(d.type){case"string":{b.type="string";let{minimum:c,maximum:d,format:e,patterns:g,contentEncoding:h}=a._zod.bag;if("number"==typeof c&&(b.minLength=c),"number"==typeof d&&(b.maxLength=d),e&&(b.format=({guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""})[e]??e,""===b.format&&delete b.format),h&&(b.contentEncoding=h),g&&g.size>0){let a=[...g];1===a.length?b.pattern=a[0].source:a.length>1&&(f.schema.allOf=[...a.map(a=>({..."draft-7"===this.target?{type:"string"}:{},pattern:a.source}))])}break}case"number":{let{minimum:c,maximum:d,format:e,multipleOf:f,exclusiveMaximum:g,exclusiveMinimum:h}=a._zod.bag;"string"==typeof e&&e.includes("int")?b.type="integer":b.type="number","number"==typeof h&&(b.exclusiveMinimum=h),"number"==typeof c&&(b.minimum=c,"number"==typeof h&&(h>=c?delete b.minimum:delete b.exclusiveMinimum)),"number"==typeof g&&(b.exclusiveMaximum=g),"number"==typeof d&&(b.maximum=d,"number"==typeof g&&(g<=d?delete b.maximum:delete b.exclusiveMaximum)),"number"==typeof f&&(b.multipleOf=f);break}case"boolean":case"success":b.type="boolean";break;case"bigint":if("throw"===this.unrepresentable)throw Error("BigInt cannot be represented in JSON Schema");break;case"symbol":if("throw"===this.unrepresentable)throw Error("Symbols cannot be represented in JSON Schema");break;case"null":b.type="null";break;case"any":case"unknown":break;case"undefined":if("throw"===this.unrepresentable)throw Error("Undefined cannot be represented in JSON Schema");break;case"void":if("throw"===this.unrepresentable)throw Error("Void cannot be represented in JSON Schema");break;case"never":b.not={};break;case"date":if("throw"===this.unrepresentable)throw Error("Date cannot be represented in JSON Schema");break;case"array":{let{minimum:e,maximum:f}=a._zod.bag;"number"==typeof e&&(b.minItems=e),"number"==typeof f&&(b.maxItems=f),b.type="array",b.items=this.process(d.element,{...c,path:[...c.path,"items"]});break}case"object":{b.type="object",b.properties={};let a=d.shape;for(let d in a)b.properties[d]=this.process(a[d],{...c,path:[...c.path,"properties",d]});let e=new Set([...new Set(Object.keys(a))].filter(a=>{let b=d.shape[a]._zod;return"input"===this.io?void 0===b.optin:void 0===b.optout}));e.size>0&&(b.required=Array.from(e)),d.catchall?._zod.def.type==="never"?b.additionalProperties=!1:d.catchall?d.catchall&&(b.additionalProperties=this.process(d.catchall,{...c,path:[...c.path,"additionalProperties"]})):"output"===this.io&&(b.additionalProperties=!1);break}case"union":b.anyOf=d.options.map((a,b)=>this.process(a,{...c,path:[...c.path,"anyOf",b]}));break;case"intersection":{let a=this.process(d.left,{...c,path:[...c.path,"allOf",0]}),e=this.process(d.right,{...c,path:[...c.path,"allOf",1]}),f=a=>"allOf"in a&&1===Object.keys(a).length;b.allOf=[...f(a)?a.allOf:[a],...f(e)?e.allOf:[e]];break}case"tuple":{b.type="array";let e=d.items.map((a,b)=>this.process(a,{...c,path:[...c.path,"prefixItems",b]}));if("draft-2020-12"===this.target?b.prefixItems=e:b.items=e,d.rest){let a=this.process(d.rest,{...c,path:[...c.path,"items"]});"draft-2020-12"===this.target?b.items=a:b.additionalItems=a}d.rest&&(b.items=this.process(d.rest,{...c,path:[...c.path,"items"]}));let{minimum:f,maximum:g}=a._zod.bag;"number"==typeof f&&(b.minItems=f),"number"==typeof g&&(b.maxItems=g);break}case"record":b.type="object",b.propertyNames=this.process(d.keyType,{...c,path:[...c.path,"propertyNames"]}),b.additionalProperties=this.process(d.valueType,{...c,path:[...c.path,"additionalProperties"]});break;case"map":if("throw"===this.unrepresentable)throw Error("Map cannot be represented in JSON Schema");break;case"set":if("throw"===this.unrepresentable)throw Error("Set cannot be represented in JSON Schema");break;case"enum":{let a=a9(d.entries);a.every(a=>"number"==typeof a)&&(b.type="number"),a.every(a=>"string"==typeof a)&&(b.type="string"),b.enum=a;break}case"literal":{let a=[];for(let b of d.values)if(void 0===b){if("throw"===this.unrepresentable)throw Error("Literal `undefined` cannot be represented in JSON Schema")}else if("bigint"==typeof b)if("throw"===this.unrepresentable)throw Error("BigInt literals cannot be represented in JSON Schema");else a.push(Number(b));else a.push(b);if(0===a.length);else if(1===a.length){let c=a[0];b.type=null===c?"null":typeof c,b.const=c}else a.every(a=>"number"==typeof a)&&(b.type="number"),a.every(a=>"string"==typeof a)&&(b.type="string"),a.every(a=>"boolean"==typeof a)&&(b.type="string"),a.every(a=>null===a)&&(b.type="null"),b.enum=a;break}case"file":{let c={type:"string",format:"binary",contentEncoding:"binary"},{minimum:d,maximum:e,mime:f}=a._zod.bag;void 0!==d&&(c.minLength=d),void 0!==e&&(c.maxLength=e),f?1===f.length?(c.contentMediaType=f[0],Object.assign(b,c)):b.anyOf=f.map(a=>({...c,contentMediaType:a})):Object.assign(b,c);break}case"transform":if("throw"===this.unrepresentable)throw Error("Transforms cannot be represented in JSON Schema");break;case"nullable":b.anyOf=[this.process(d.innerType,c),{type:"null"}];break;case"nonoptional":case"promise":case"optional":this.process(d.innerType,c),f.ref=d.innerType;break;case"default":this.process(d.innerType,c),f.ref=d.innerType,b.default=JSON.parse(JSON.stringify(d.defaultValue));break;case"prefault":this.process(d.innerType,c),f.ref=d.innerType,"input"===this.io&&(b._prefault=JSON.parse(JSON.stringify(d.defaultValue)));break;case"catch":{let a;this.process(d.innerType,c),f.ref=d.innerType;try{a=d.catchValue(void 0)}catch{throw Error("Dynamic catch values are not supported in JSON Schema")}b.default=a;break}case"nan":if("throw"===this.unrepresentable)throw Error("NaN cannot be represented in JSON Schema");break;case"template_literal":{let c=a._zod.pattern;if(!c)throw Error("Pattern not found in template literal");b.type="string",b.pattern=c.source;break}case"pipe":{let a="input"===this.io?"transform"===d.in._zod.def.type?d.out:d.in:d.out;this.process(a,c),f.ref=a;break}case"readonly":this.process(d.innerType,c),f.ref=d.innerType,b.readOnly=!0;break;case"lazy":{let b=a._zod.innerType;this.process(b,c),f.ref=b;break}case"custom":if("throw"===this.unrepresentable)throw Error("Custom types cannot be represented in JSON Schema")}}}let h=this.metadataRegistry.get(a);return h&&Object.assign(f.schema,h),"input"===this.io&&function a(b,c){let d=c??{seen:new Set};if(d.seen.has(b))return!1;d.seen.add(b);let e=b._zod.def;switch(e.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":case"custom":case"success":case"catch":return!1;case"array":return a(e.element,d);case"object":for(let b in e.shape)if(a(e.shape[b],d))return!0;return!1;case"union":for(let b of e.options)if(a(b,d))return!0;return!1;case"intersection":return a(e.left,d)||a(e.right,d);case"tuple":for(let b of e.items)if(a(b,d))return!0;if(e.rest&&a(e.rest,d))return!0;return!1;case"record":case"map":return a(e.keyType,d)||a(e.valueType,d);case"set":return a(e.valueType,d);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":case"default":case"prefault":return a(e.innerType,d);case"lazy":return a(e.getter(),d);case"transform":return!0;case"pipe":return a(e.in,d)||a(e.out,d)}throw Error(`Unknown schema type: ${e.type}`)}(a)&&(delete f.schema.examples,delete f.schema.default),"input"===this.io&&f.schema._prefault&&((c=f.schema).default??(c.default=f.schema._prefault)),delete f.schema._prefault,this.seen.get(a).schema}emit(a,b){let c={cycles:b?.cycles??"ref",reused:b?.reused??"inline",external:b?.external??void 0},d=this.seen.get(a);if(!d)throw Error("Unprocessed schema. This is a bug in Zod.");let e=a=>{let b="draft-2020-12"===this.target?"$defs":"definitions";if(c.external){let d=c.external.registry.get(a[0])?.id,e=c.external.uri??(a=>a);if(d)return{ref:e(d)};let f=a[1].defId??a[1].schema.id??`schema${this.counter++}`;return a[1].defId=f,{defId:f,ref:`${e("__shared")}#/${b}/${f}`}}if(a[1]===d)return{ref:"#"};let e=`#/${b}/`,f=a[1].schema.id??`__schema${this.counter++}`;return{defId:f,ref:e+f}},f=a=>{if(a[1].schema.$ref)return;let b=a[1],{ref:c,defId:d}=e(a);b.def={...b.schema},d&&(b.defId=d);let f=b.schema;for(let a in f)delete f[a];f.$ref=c};if("throw"===c.cycles)for(let a of this.seen.entries()){let b=a[1];if(b.cycle)throw Error(`Cycle detected: #/${b.cycle?.join("/")}/<root>

Set the \`cycles\` parameter to \`"ref"\` to resolve cyclical schemas with defs.`)}for(let b of this.seen.entries()){let d=b[1];if(a===b[0]){f(b);continue}if(c.external){let d=c.external.registry.get(b[0])?.id;if(a!==b[0]&&d){f(b);continue}}if(this.metadataRegistry.get(b[0])?.id||d.cycle||d.count>1&&"ref"===c.reused){f(b);continue}}let g=(a,b)=>{let c=this.seen.get(a),d=c.def??c.schema,e={...d};if(null===c.ref)return;let f=c.ref;if(c.ref=null,f){g(f,b);let a=this.seen.get(f).schema;a.$ref&&"draft-7"===b.target?(d.allOf=d.allOf??[],d.allOf.push(a)):(Object.assign(d,a),Object.assign(d,e))}c.isParent||this.override({zodSchema:a,jsonSchema:d,path:c.path??[]})};for(let a of[...this.seen.entries()].reverse())g(a[0],{target:this.target});let h={};if("draft-2020-12"===this.target?h.$schema="https://json-schema.org/draft/2020-12/schema":"draft-7"===this.target?h.$schema="http://json-schema.org/draft-07/schema#":console.warn(`Invalid target: ${this.target}`),c.external?.uri){let b=c.external.registry.get(a)?.id;if(!b)throw Error("Schema is missing an `id` property");h.$id=c.external.uri(b)}Object.assign(h,d.def);let i=c.external?.defs??{};for(let a of this.seen.entries()){let b=a[1];b.def&&b.defId&&(i[b.defId]=b.def)}c.external||Object.keys(i).length>0&&("draft-2020-12"===this.target?h.$defs=i:h.definitions=i);try{return JSON.parse(JSON.stringify(h))}catch(a){throw Error("Error converting schema to JSON.")}}}function bw(a,b,c){function d(c,d){var e;for(let f in Object.defineProperty(c,"_zod",{value:c._zod??{},enumerable:!1}),(e=c._zod).traits??(e.traits=new Set),c._zod.traits.add(a),b(c,d),g.prototype)f in c||Object.defineProperty(c,f,{value:g.prototype[f].bind(c)});c._zod.constr=g,c._zod.def=d}let e=c?.Parent??Object;class f extends e{}function g(a){var b;let e=c?.Parent?new f:this;for(let c of(d(e,a),(b=e._zod).deferred??(b.deferred=[]),e._zod.deferred))c();return e}return Object.defineProperty(f,"name",{value:a}),Object.defineProperty(g,"init",{value:d}),Object.defineProperty(g,Symbol.hasInstance,{value:b=>!!c?.Parent&&b instanceof c.Parent||b?._zod?.traits?.has(a)}),Object.defineProperty(g,"name",{value:a}),g}Object.freeze({status:"aborted"}),Symbol("zod_brand");class bx extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}let by={};function bz(a){return a&&Object.assign(by,a),by}let bA=(a,b)=>{a.name="$ZodError",Object.defineProperty(a,"_zod",{value:a._zod,enumerable:!1}),Object.defineProperty(a,"issues",{value:b,enumerable:!1}),Object.defineProperty(a,"message",{get:()=>JSON.stringify(b,ba,2),enumerable:!0}),Object.defineProperty(a,"toString",{value:()=>a.message,enumerable:!1})},bB=bw("$ZodError",bA),bC=bw("$ZodError",bA,{Parent:Error}),bD=a=>(b,c,d)=>{let e=d?{...d,async:!1}:{async:!1},f=b._zod.run({value:c,issues:[]},e);if(f instanceof Promise)throw new bx;return f.issues.length?{success:!1,error:new(a??bB)(f.issues.map(a=>bs(a,e,bz())))}:{success:!0,data:f.value}},bE=bD(bC),bF=a=>async(b,c,d)=>{let e=d?Object.assign(d,{async:!0}):{async:!0},f=b._zod.run({value:c,issues:[]},e);return f instanceof Promise&&(f=await f),f.issues.length?{success:!1,error:new a(f.issues.map(a=>bs(a,e,bz())))}:{success:!0,data:f.value}},bG=bF(bC),bH=(a,b)=>{bB.init(a,b),a.name="ZodError",Object.defineProperties(a,{format:{value:b=>(function(a,b){let c=b||function(a){return a.message},d={_errors:[]},e=a=>{for(let b of a.issues)if("invalid_union"===b.code&&b.errors.length)b.errors.map(a=>e({issues:a}));else if("invalid_key"===b.code)e({issues:b.issues});else if("invalid_element"===b.code)e({issues:b.issues});else if(0===b.path.length)d._errors.push(c(b));else{let a=d,e=0;for(;e<b.path.length;){let d=b.path[e];e===b.path.length-1?(a[d]=a[d]||{_errors:[]},a[d]._errors.push(c(b))):a[d]=a[d]||{_errors:[]},a=a[d],e++}}};return e(a),d})(a,b)},flatten:{value:b=>(function(a,b=a=>a.message){let c={},d=[];for(let e of a.issues)e.path.length>0?(c[e.path[0]]=c[e.path[0]]||[],c[e.path[0]].push(b(e))):d.push(b(e));return{formErrors:d,fieldErrors:c}})(a,b)},addIssue:{value:b=>a.issues.push(b)},addIssues:{value:b=>a.issues.push(...b)},isEmpty:{get:()=>0===a.issues.length}})};bw("ZodError",bH);let bI=bw("ZodError",bH,{Parent:Error}),bJ=(a,b,c,d)=>{let e=c?Object.assign(c,{async:!1}):{async:!1},f=a._zod.run({value:b,issues:[]},e);if(f instanceof Promise)throw new bx;if(f.issues.length){let a=new(d?.Err??bI)(f.issues.map(a=>bs(a,e,bz())));throw bg(a,d?.callee),a}return f.value},bK=async(a,b,c,d)=>{let e=c?Object.assign(c,{async:!0}):{async:!0},f=a._zod.run({value:b,issues:[]},e);if(f instanceof Promise&&(f=await f),f.issues.length){let a=new(d?.Err??bI)(f.issues.map(a=>bs(a,e,bz())));throw bg(a,d?.callee),a}return f.value},bL=bD(bI),bM=bF(bI),bN=Symbol("Let zodToJsonSchema decide on which parser to use"),bO={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",allowedAdditionalProperties:!0,rejectedAdditionalProperties:!1,definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref",openAiAnyTypeName:"OpenAiAnyType"};!function(a){a.assertEqual=a=>{},a.assertIs=function(a){},a.assertNever=function(a){throw Error()},a.arrayToEnum=a=>{let b={};for(let c of a)b[c]=c;return b},a.getValidEnumValues=b=>{let c=a.objectKeys(b).filter(a=>"number"!=typeof b[b[a]]),d={};for(let a of c)d[a]=b[a];return a.objectValues(d)},a.objectValues=b=>a.objectKeys(b).map(function(a){return b[a]}),a.objectKeys="function"==typeof Object.keys?a=>Object.keys(a):a=>{let b=[];for(let c in a)Object.prototype.hasOwnProperty.call(a,c)&&b.push(c);return b},a.find=(a,b)=>{for(let c of a)if(b(c))return c},a.isInteger="function"==typeof Number.isInteger?a=>Number.isInteger(a):a=>"number"==typeof a&&Number.isFinite(a)&&Math.floor(a)===a,a.joinValues=function(a,b=" | "){return a.map(a=>"string"==typeof a?`'${a}'`:a).join(b)},a.jsonStringifyReplacer=(a,b)=>"bigint"==typeof b?b.toString():b}(r||(r={})),(s||(s={})).mergeShapes=(a,b)=>({...a,...b});let bP=r.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),bQ=a=>{switch(typeof a){case"undefined":return bP.undefined;case"string":return bP.string;case"number":return Number.isNaN(a)?bP.nan:bP.number;case"boolean":return bP.boolean;case"function":return bP.function;case"bigint":return bP.bigint;case"symbol":return bP.symbol;case"object":if(Array.isArray(a))return bP.array;if(null===a)return bP.null;if(a.then&&"function"==typeof a.then&&a.catch&&"function"==typeof a.catch)return bP.promise;if("undefined"!=typeof Map&&a instanceof Map)return bP.map;if("undefined"!=typeof Set&&a instanceof Set)return bP.set;if("undefined"!=typeof Date&&a instanceof Date)return bP.date;return bP.object;default:return bP.unknown}},bR=r.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class bS extends Error{get errors(){return this.issues}constructor(a){super(),this.issues=[],this.addIssue=a=>{this.issues=[...this.issues,a]},this.addIssues=(a=[])=>{this.issues=[...this.issues,...a]};let b=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,b):this.__proto__=b,this.name="ZodError",this.issues=a}format(a){let b=a||function(a){return a.message},c={_errors:[]},d=a=>{for(let e of a.issues)if("invalid_union"===e.code)e.unionErrors.map(d);else if("invalid_return_type"===e.code)d(e.returnTypeError);else if("invalid_arguments"===e.code)d(e.argumentsError);else if(0===e.path.length)c._errors.push(b(e));else{let a=c,d=0;for(;d<e.path.length;){let c=e.path[d];d===e.path.length-1?(a[c]=a[c]||{_errors:[]},a[c]._errors.push(b(e))):a[c]=a[c]||{_errors:[]},a=a[c],d++}}};return d(this),c}static assert(a){if(!(a instanceof bS))throw Error(`Not a ZodError: ${a}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,r.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(a=a=>a.message){let b={},c=[];for(let d of this.issues)if(d.path.length>0){let c=d.path[0];b[c]=b[c]||[],b[c].push(a(d))}else c.push(a(d));return{formErrors:c,fieldErrors:b}}get formErrors(){return this.flatten()}}bS.create=a=>new bS(a);let bT=(a,b)=>{let c;switch(a.code){case bR.invalid_type:c=a.received===bP.undefined?"Required":`Expected ${a.expected}, received ${a.received}`;break;case bR.invalid_literal:c=`Invalid literal value, expected ${JSON.stringify(a.expected,r.jsonStringifyReplacer)}`;break;case bR.unrecognized_keys:c=`Unrecognized key(s) in object: ${r.joinValues(a.keys,", ")}`;break;case bR.invalid_union:c="Invalid input";break;case bR.invalid_union_discriminator:c=`Invalid discriminator value. Expected ${r.joinValues(a.options)}`;break;case bR.invalid_enum_value:c=`Invalid enum value. Expected ${r.joinValues(a.options)}, received '${a.received}'`;break;case bR.invalid_arguments:c="Invalid function arguments";break;case bR.invalid_return_type:c="Invalid function return type";break;case bR.invalid_date:c="Invalid date";break;case bR.invalid_string:"object"==typeof a.validation?"includes"in a.validation?(c=`Invalid input: must include "${a.validation.includes}"`,"number"==typeof a.validation.position&&(c=`${c} at one or more positions greater than or equal to ${a.validation.position}`)):"startsWith"in a.validation?c=`Invalid input: must start with "${a.validation.startsWith}"`:"endsWith"in a.validation?c=`Invalid input: must end with "${a.validation.endsWith}"`:r.assertNever(a.validation):c="regex"!==a.validation?`Invalid ${a.validation}`:"Invalid";break;case bR.too_small:c="array"===a.type?`Array must contain ${a.exact?"exactly":a.inclusive?"at least":"more than"} ${a.minimum} element(s)`:"string"===a.type?`String must contain ${a.exact?"exactly":a.inclusive?"at least":"over"} ${a.minimum} character(s)`:"number"===a.type||"bigint"===a.type?`Number must be ${a.exact?"exactly equal to ":a.inclusive?"greater than or equal to ":"greater than "}${a.minimum}`:"date"===a.type?`Date must be ${a.exact?"exactly equal to ":a.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(a.minimum))}`:"Invalid input";break;case bR.too_big:c="array"===a.type?`Array must contain ${a.exact?"exactly":a.inclusive?"at most":"less than"} ${a.maximum} element(s)`:"string"===a.type?`String must contain ${a.exact?"exactly":a.inclusive?"at most":"under"} ${a.maximum} character(s)`:"number"===a.type?`Number must be ${a.exact?"exactly":a.inclusive?"less than or equal to":"less than"} ${a.maximum}`:"bigint"===a.type?`BigInt must be ${a.exact?"exactly":a.inclusive?"less than or equal to":"less than"} ${a.maximum}`:"date"===a.type?`Date must be ${a.exact?"exactly":a.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(a.maximum))}`:"Invalid input";break;case bR.custom:c="Invalid input";break;case bR.invalid_intersection_types:c="Intersection results could not be merged";break;case bR.not_multiple_of:c=`Number must be a multiple of ${a.multipleOf}`;break;case bR.not_finite:c="Number must be finite";break;default:c=b.defaultError,r.assertNever(a)}return{message:c}};!function(a){a.errToObj=a=>"string"==typeof a?{message:a}:a||{},a.toString=a=>"string"==typeof a?a:a?.message}(t||(t={}));let bU=a=>{let{data:b,path:c,errorMaps:d,issueData:e}=a,f=[...c,...e.path||[]],g={...e,path:f};if(void 0!==e.message)return{...e,path:f,message:e.message};let h="";for(let a of d.filter(a=>!!a).slice().reverse())h=a(g,{data:b,defaultError:h}).message;return{...e,path:f,message:h}};function bV(a,b){let c=bU({issueData:b,data:a.data,path:a.path,errorMaps:[a.common.contextualErrorMap,a.schemaErrorMap,bT,void 0].filter(a=>!!a)});a.common.issues.push(c)}class bW{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(a,b){let c=[];for(let d of b){if("aborted"===d.status)return bX;"dirty"===d.status&&a.dirty(),c.push(d.value)}return{status:a.value,value:c}}static async mergeObjectAsync(a,b){let c=[];for(let a of b){let b=await a.key,d=await a.value;c.push({key:b,value:d})}return bW.mergeObjectSync(a,c)}static mergeObjectSync(a,b){let c={};for(let d of b){let{key:b,value:e}=d;if("aborted"===b.status||"aborted"===e.status)return bX;"dirty"===b.status&&a.dirty(),"dirty"===e.status&&a.dirty(),"__proto__"!==b.value&&(void 0!==e.value||d.alwaysSet)&&(c[b.value]=e.value)}return{status:a.value,value:c}}}let bX=Object.freeze({status:"aborted"}),bY=a=>({status:"dirty",value:a}),bZ=a=>({status:"valid",value:a}),b$=a=>"undefined"!=typeof Promise&&a instanceof Promise;class b_{constructor(a,b,c,d){this._cachedPath=[],this.parent=a,this.data=b,this._path=c,this._key=d}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let b0=(a,b)=>{if("valid"===b.status)return{success:!0,data:b.value};if(!a.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let b=new bS(a.common.issues);return this._error=b,this._error}}};function b1(a){if(!a)return{};let{errorMap:b,invalid_type_error:c,required_error:d,description:e}=a;if(b&&(c||d))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return b?{errorMap:b,description:e}:{errorMap:(b,e)=>{let{message:f}=a;return"invalid_enum_value"===b.code?{message:f??e.defaultError}:void 0===e.data?{message:f??d??e.defaultError}:"invalid_type"!==b.code?{message:e.defaultError}:{message:f??c??e.defaultError}},description:e}}class b2{get description(){return this._def.description}_getType(a){return bQ(a.data)}_getOrReturnCtx(a,b){return b||{common:a.parent.common,data:a.data,parsedType:bQ(a.data),schemaErrorMap:this._def.errorMap,path:a.path,parent:a.parent}}_processInputParams(a){return{status:new bW,ctx:{common:a.parent.common,data:a.data,parsedType:bQ(a.data),schemaErrorMap:this._def.errorMap,path:a.path,parent:a.parent}}}_parseSync(a){let b=this._parse(a);if(b$(b))throw Error("Synchronous parse encountered promise.");return b}_parseAsync(a){return Promise.resolve(this._parse(a))}parse(a,b){let c=this.safeParse(a,b);if(c.success)return c.data;throw c.error}safeParse(a,b){let c={common:{issues:[],async:b?.async??!1,contextualErrorMap:b?.errorMap},path:b?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:a,parsedType:bQ(a)},d=this._parseSync({data:a,path:c.path,parent:c});return b0(c,d)}"~validate"(a){let b={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:a,parsedType:bQ(a)};if(!this["~standard"].async)try{let c=this._parseSync({data:a,path:[],parent:b});return"valid"===c.status?{value:c.value}:{issues:b.common.issues}}catch(a){a?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),b.common={issues:[],async:!0}}return this._parseAsync({data:a,path:[],parent:b}).then(a=>"valid"===a.status?{value:a.value}:{issues:b.common.issues})}async parseAsync(a,b){let c=await this.safeParseAsync(a,b);if(c.success)return c.data;throw c.error}async safeParseAsync(a,b){let c={common:{issues:[],contextualErrorMap:b?.errorMap,async:!0},path:b?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:a,parsedType:bQ(a)},d=this._parse({data:a,path:c.path,parent:c});return b0(c,await (b$(d)?d:Promise.resolve(d)))}refine(a,b){return this._refinement((c,d)=>{let e=a(c),f=()=>d.addIssue({code:bR.custom,..."string"==typeof b||void 0===b?{message:b}:"function"==typeof b?b(c):b});return"undefined"!=typeof Promise&&e instanceof Promise?e.then(a=>!!a||(f(),!1)):!!e||(f(),!1)})}refinement(a,b){return this._refinement((c,d)=>!!a(c)||(d.addIssue("function"==typeof b?b(c,d):b),!1))}_refinement(a){return new cN({schema:this,typeName:u.ZodEffects,effect:{type:"refinement",refinement:a}})}superRefine(a){return this._refinement(a)}constructor(a){this.spa=this.safeParseAsync,this._def=a,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:a=>this["~validate"](a)}}optional(){return cO.create(this,this._def)}nullable(){return cP.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return cw.create(this)}promise(){return cM.create(this,this._def)}or(a){return cy.create([this,a],this._def)}and(a){return cB.create(this,a,this._def)}transform(a){return new cN({...b1(this._def),schema:this,typeName:u.ZodEffects,effect:{type:"transform",transform:a}})}default(a){return new cQ({...b1(this._def),innerType:this,defaultValue:"function"==typeof a?a:()=>a,typeName:u.ZodDefault})}brand(){return new cT({typeName:u.ZodBranded,type:this,...b1(this._def)})}catch(a){return new cR({...b1(this._def),innerType:this,catchValue:"function"==typeof a?a:()=>a,typeName:u.ZodCatch})}describe(a){return new this.constructor({...this._def,description:a})}pipe(a){return cU.create(this,a)}readonly(){return cV.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let b3=/^c[^\s-]{8,}$/i,b4=/^[0-9a-z]+$/,b5=/^[0-9A-HJKMNP-TV-Z]{26}$/i,b6=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,b7=/^[a-z0-9_-]{21}$/i,b8=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,b9=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,ca=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,cb=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,cc=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,cd=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,ce=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,cf=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,cg=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ch="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",ci=RegExp(`^${ch}$`);function cj(a){let b="[0-5]\\d";a.precision?b=`${b}\\.\\d{${a.precision}}`:null==a.precision&&(b=`${b}(\\.\\d+)?`);let c=a.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${b})${c}`}class ck extends b2{_parse(a){var b,c,e,f;let g;if(this._def.coerce&&(a.data=String(a.data)),this._getType(a)!==bP.string){let b=this._getOrReturnCtx(a);return bV(b,{code:bR.invalid_type,expected:bP.string,received:b.parsedType}),bX}let h=new bW;for(let i of this._def.checks)if("min"===i.kind)a.data.length<i.value&&(bV(g=this._getOrReturnCtx(a,g),{code:bR.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),h.dirty());else if("max"===i.kind)a.data.length>i.value&&(bV(g=this._getOrReturnCtx(a,g),{code:bR.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),h.dirty());else if("length"===i.kind){let b=a.data.length>i.value,c=a.data.length<i.value;(b||c)&&(g=this._getOrReturnCtx(a,g),b?bV(g,{code:bR.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&bV(g,{code:bR.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),h.dirty())}else if("email"===i.kind)ca.test(a.data)||(bV(g=this._getOrReturnCtx(a,g),{validation:"email",code:bR.invalid_string,message:i.message}),h.dirty());else if("emoji"===i.kind)d||(d=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),d.test(a.data)||(bV(g=this._getOrReturnCtx(a,g),{validation:"emoji",code:bR.invalid_string,message:i.message}),h.dirty());else if("uuid"===i.kind)b6.test(a.data)||(bV(g=this._getOrReturnCtx(a,g),{validation:"uuid",code:bR.invalid_string,message:i.message}),h.dirty());else if("nanoid"===i.kind)b7.test(a.data)||(bV(g=this._getOrReturnCtx(a,g),{validation:"nanoid",code:bR.invalid_string,message:i.message}),h.dirty());else if("cuid"===i.kind)b3.test(a.data)||(bV(g=this._getOrReturnCtx(a,g),{validation:"cuid",code:bR.invalid_string,message:i.message}),h.dirty());else if("cuid2"===i.kind)b4.test(a.data)||(bV(g=this._getOrReturnCtx(a,g),{validation:"cuid2",code:bR.invalid_string,message:i.message}),h.dirty());else if("ulid"===i.kind)b5.test(a.data)||(bV(g=this._getOrReturnCtx(a,g),{validation:"ulid",code:bR.invalid_string,message:i.message}),h.dirty());else if("url"===i.kind)try{new URL(a.data)}catch{bV(g=this._getOrReturnCtx(a,g),{validation:"url",code:bR.invalid_string,message:i.message}),h.dirty()}else"regex"===i.kind?(i.regex.lastIndex=0,i.regex.test(a.data)||(bV(g=this._getOrReturnCtx(a,g),{validation:"regex",code:bR.invalid_string,message:i.message}),h.dirty())):"trim"===i.kind?a.data=a.data.trim():"includes"===i.kind?a.data.includes(i.value,i.position)||(bV(g=this._getOrReturnCtx(a,g),{code:bR.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),h.dirty()):"toLowerCase"===i.kind?a.data=a.data.toLowerCase():"toUpperCase"===i.kind?a.data=a.data.toUpperCase():"startsWith"===i.kind?a.data.startsWith(i.value)||(bV(g=this._getOrReturnCtx(a,g),{code:bR.invalid_string,validation:{startsWith:i.value},message:i.message}),h.dirty()):"endsWith"===i.kind?a.data.endsWith(i.value)||(bV(g=this._getOrReturnCtx(a,g),{code:bR.invalid_string,validation:{endsWith:i.value},message:i.message}),h.dirty()):"datetime"===i.kind?(function(a){let b=`${ch}T${cj(a)}`,c=[];return c.push(a.local?"Z?":"Z"),a.offset&&c.push("([+-]\\d{2}:?\\d{2})"),b=`${b}(${c.join("|")})`,RegExp(`^${b}$`)})(i).test(a.data)||(bV(g=this._getOrReturnCtx(a,g),{code:bR.invalid_string,validation:"datetime",message:i.message}),h.dirty()):"date"===i.kind?ci.test(a.data)||(bV(g=this._getOrReturnCtx(a,g),{code:bR.invalid_string,validation:"date",message:i.message}),h.dirty()):"time"===i.kind?RegExp(`^${cj(i)}$`).test(a.data)||(bV(g=this._getOrReturnCtx(a,g),{code:bR.invalid_string,validation:"time",message:i.message}),h.dirty()):"duration"===i.kind?b9.test(a.data)||(bV(g=this._getOrReturnCtx(a,g),{validation:"duration",code:bR.invalid_string,message:i.message}),h.dirty()):"ip"===i.kind?(b=a.data,!(("v4"===(c=i.version)||!c)&&cb.test(b)||("v6"===c||!c)&&cd.test(b))&&1&&(bV(g=this._getOrReturnCtx(a,g),{validation:"ip",code:bR.invalid_string,message:i.message}),h.dirty())):"jwt"===i.kind?!function(a,b){if(!b8.test(a))return!1;try{let[c]=a.split(".");if(!c)return!1;let d=c.replace(/-/g,"+").replace(/_/g,"/").padEnd(c.length+(4-c.length%4)%4,"="),e=JSON.parse(atob(d));if("object"!=typeof e||null===e||"typ"in e&&e?.typ!=="JWT"||!e.alg||b&&e.alg!==b)return!1;return!0}catch{return!1}}(a.data,i.alg)&&(bV(g=this._getOrReturnCtx(a,g),{validation:"jwt",code:bR.invalid_string,message:i.message}),h.dirty()):"cidr"===i.kind?(e=a.data,!(("v4"===(f=i.version)||!f)&&cc.test(e)||("v6"===f||!f)&&ce.test(e))&&1&&(bV(g=this._getOrReturnCtx(a,g),{validation:"cidr",code:bR.invalid_string,message:i.message}),h.dirty())):"base64"===i.kind?cf.test(a.data)||(bV(g=this._getOrReturnCtx(a,g),{validation:"base64",code:bR.invalid_string,message:i.message}),h.dirty()):"base64url"===i.kind?cg.test(a.data)||(bV(g=this._getOrReturnCtx(a,g),{validation:"base64url",code:bR.invalid_string,message:i.message}),h.dirty()):r.assertNever(i);return{status:h.value,value:a.data}}_regex(a,b,c){return this.refinement(b=>a.test(b),{validation:b,code:bR.invalid_string,...t.errToObj(c)})}_addCheck(a){return new ck({...this._def,checks:[...this._def.checks,a]})}email(a){return this._addCheck({kind:"email",...t.errToObj(a)})}url(a){return this._addCheck({kind:"url",...t.errToObj(a)})}emoji(a){return this._addCheck({kind:"emoji",...t.errToObj(a)})}uuid(a){return this._addCheck({kind:"uuid",...t.errToObj(a)})}nanoid(a){return this._addCheck({kind:"nanoid",...t.errToObj(a)})}cuid(a){return this._addCheck({kind:"cuid",...t.errToObj(a)})}cuid2(a){return this._addCheck({kind:"cuid2",...t.errToObj(a)})}ulid(a){return this._addCheck({kind:"ulid",...t.errToObj(a)})}base64(a){return this._addCheck({kind:"base64",...t.errToObj(a)})}base64url(a){return this._addCheck({kind:"base64url",...t.errToObj(a)})}jwt(a){return this._addCheck({kind:"jwt",...t.errToObj(a)})}ip(a){return this._addCheck({kind:"ip",...t.errToObj(a)})}cidr(a){return this._addCheck({kind:"cidr",...t.errToObj(a)})}datetime(a){return"string"==typeof a?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:a}):this._addCheck({kind:"datetime",precision:void 0===a?.precision?null:a?.precision,offset:a?.offset??!1,local:a?.local??!1,...t.errToObj(a?.message)})}date(a){return this._addCheck({kind:"date",message:a})}time(a){return"string"==typeof a?this._addCheck({kind:"time",precision:null,message:a}):this._addCheck({kind:"time",precision:void 0===a?.precision?null:a?.precision,...t.errToObj(a?.message)})}duration(a){return this._addCheck({kind:"duration",...t.errToObj(a)})}regex(a,b){return this._addCheck({kind:"regex",regex:a,...t.errToObj(b)})}includes(a,b){return this._addCheck({kind:"includes",value:a,position:b?.position,...t.errToObj(b?.message)})}startsWith(a,b){return this._addCheck({kind:"startsWith",value:a,...t.errToObj(b)})}endsWith(a,b){return this._addCheck({kind:"endsWith",value:a,...t.errToObj(b)})}min(a,b){return this._addCheck({kind:"min",value:a,...t.errToObj(b)})}max(a,b){return this._addCheck({kind:"max",value:a,...t.errToObj(b)})}length(a,b){return this._addCheck({kind:"length",value:a,...t.errToObj(b)})}nonempty(a){return this.min(1,t.errToObj(a))}trim(){return new ck({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new ck({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new ck({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(a=>"datetime"===a.kind)}get isDate(){return!!this._def.checks.find(a=>"date"===a.kind)}get isTime(){return!!this._def.checks.find(a=>"time"===a.kind)}get isDuration(){return!!this._def.checks.find(a=>"duration"===a.kind)}get isEmail(){return!!this._def.checks.find(a=>"email"===a.kind)}get isURL(){return!!this._def.checks.find(a=>"url"===a.kind)}get isEmoji(){return!!this._def.checks.find(a=>"emoji"===a.kind)}get isUUID(){return!!this._def.checks.find(a=>"uuid"===a.kind)}get isNANOID(){return!!this._def.checks.find(a=>"nanoid"===a.kind)}get isCUID(){return!!this._def.checks.find(a=>"cuid"===a.kind)}get isCUID2(){return!!this._def.checks.find(a=>"cuid2"===a.kind)}get isULID(){return!!this._def.checks.find(a=>"ulid"===a.kind)}get isIP(){return!!this._def.checks.find(a=>"ip"===a.kind)}get isCIDR(){return!!this._def.checks.find(a=>"cidr"===a.kind)}get isBase64(){return!!this._def.checks.find(a=>"base64"===a.kind)}get isBase64url(){return!!this._def.checks.find(a=>"base64url"===a.kind)}get minLength(){let a=null;for(let b of this._def.checks)"min"===b.kind&&(null===a||b.value>a)&&(a=b.value);return a}get maxLength(){let a=null;for(let b of this._def.checks)"max"===b.kind&&(null===a||b.value<a)&&(a=b.value);return a}}ck.create=a=>new ck({checks:[],typeName:u.ZodString,coerce:a?.coerce??!1,...b1(a)});class cl extends b2{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(a){let b;if(this._def.coerce&&(a.data=Number(a.data)),this._getType(a)!==bP.number){let b=this._getOrReturnCtx(a);return bV(b,{code:bR.invalid_type,expected:bP.number,received:b.parsedType}),bX}let c=new bW;for(let d of this._def.checks)"int"===d.kind?r.isInteger(a.data)||(bV(b=this._getOrReturnCtx(a,b),{code:bR.invalid_type,expected:"integer",received:"float",message:d.message}),c.dirty()):"min"===d.kind?(d.inclusive?a.data<d.value:a.data<=d.value)&&(bV(b=this._getOrReturnCtx(a,b),{code:bR.too_small,minimum:d.value,type:"number",inclusive:d.inclusive,exact:!1,message:d.message}),c.dirty()):"max"===d.kind?(d.inclusive?a.data>d.value:a.data>=d.value)&&(bV(b=this._getOrReturnCtx(a,b),{code:bR.too_big,maximum:d.value,type:"number",inclusive:d.inclusive,exact:!1,message:d.message}),c.dirty()):"multipleOf"===d.kind?0!==function(a,b){let c=(a.toString().split(".")[1]||"").length,d=(b.toString().split(".")[1]||"").length,e=c>d?c:d;return Number.parseInt(a.toFixed(e).replace(".",""))%Number.parseInt(b.toFixed(e).replace(".",""))/10**e}(a.data,d.value)&&(bV(b=this._getOrReturnCtx(a,b),{code:bR.not_multiple_of,multipleOf:d.value,message:d.message}),c.dirty()):"finite"===d.kind?Number.isFinite(a.data)||(bV(b=this._getOrReturnCtx(a,b),{code:bR.not_finite,message:d.message}),c.dirty()):r.assertNever(d);return{status:c.value,value:a.data}}gte(a,b){return this.setLimit("min",a,!0,t.toString(b))}gt(a,b){return this.setLimit("min",a,!1,t.toString(b))}lte(a,b){return this.setLimit("max",a,!0,t.toString(b))}lt(a,b){return this.setLimit("max",a,!1,t.toString(b))}setLimit(a,b,c,d){return new cl({...this._def,checks:[...this._def.checks,{kind:a,value:b,inclusive:c,message:t.toString(d)}]})}_addCheck(a){return new cl({...this._def,checks:[...this._def.checks,a]})}int(a){return this._addCheck({kind:"int",message:t.toString(a)})}positive(a){return this._addCheck({kind:"min",value:0,inclusive:!1,message:t.toString(a)})}negative(a){return this._addCheck({kind:"max",value:0,inclusive:!1,message:t.toString(a)})}nonpositive(a){return this._addCheck({kind:"max",value:0,inclusive:!0,message:t.toString(a)})}nonnegative(a){return this._addCheck({kind:"min",value:0,inclusive:!0,message:t.toString(a)})}multipleOf(a,b){return this._addCheck({kind:"multipleOf",value:a,message:t.toString(b)})}finite(a){return this._addCheck({kind:"finite",message:t.toString(a)})}safe(a){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:t.toString(a)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:t.toString(a)})}get minValue(){let a=null;for(let b of this._def.checks)"min"===b.kind&&(null===a||b.value>a)&&(a=b.value);return a}get maxValue(){let a=null;for(let b of this._def.checks)"max"===b.kind&&(null===a||b.value<a)&&(a=b.value);return a}get isInt(){return!!this._def.checks.find(a=>"int"===a.kind||"multipleOf"===a.kind&&r.isInteger(a.value))}get isFinite(){let a=null,b=null;for(let c of this._def.checks)if("finite"===c.kind||"int"===c.kind||"multipleOf"===c.kind)return!0;else"min"===c.kind?(null===b||c.value>b)&&(b=c.value):"max"===c.kind&&(null===a||c.value<a)&&(a=c.value);return Number.isFinite(b)&&Number.isFinite(a)}}cl.create=a=>new cl({checks:[],typeName:u.ZodNumber,coerce:a?.coerce||!1,...b1(a)});class cm extends b2{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(a){let b;if(this._def.coerce)try{a.data=BigInt(a.data)}catch{return this._getInvalidInput(a)}if(this._getType(a)!==bP.bigint)return this._getInvalidInput(a);let c=new bW;for(let d of this._def.checks)"min"===d.kind?(d.inclusive?a.data<d.value:a.data<=d.value)&&(bV(b=this._getOrReturnCtx(a,b),{code:bR.too_small,type:"bigint",minimum:d.value,inclusive:d.inclusive,message:d.message}),c.dirty()):"max"===d.kind?(d.inclusive?a.data>d.value:a.data>=d.value)&&(bV(b=this._getOrReturnCtx(a,b),{code:bR.too_big,type:"bigint",maximum:d.value,inclusive:d.inclusive,message:d.message}),c.dirty()):"multipleOf"===d.kind?a.data%d.value!==BigInt(0)&&(bV(b=this._getOrReturnCtx(a,b),{code:bR.not_multiple_of,multipleOf:d.value,message:d.message}),c.dirty()):r.assertNever(d);return{status:c.value,value:a.data}}_getInvalidInput(a){let b=this._getOrReturnCtx(a);return bV(b,{code:bR.invalid_type,expected:bP.bigint,received:b.parsedType}),bX}gte(a,b){return this.setLimit("min",a,!0,t.toString(b))}gt(a,b){return this.setLimit("min",a,!1,t.toString(b))}lte(a,b){return this.setLimit("max",a,!0,t.toString(b))}lt(a,b){return this.setLimit("max",a,!1,t.toString(b))}setLimit(a,b,c,d){return new cm({...this._def,checks:[...this._def.checks,{kind:a,value:b,inclusive:c,message:t.toString(d)}]})}_addCheck(a){return new cm({...this._def,checks:[...this._def.checks,a]})}positive(a){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:t.toString(a)})}negative(a){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:t.toString(a)})}nonpositive(a){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:t.toString(a)})}nonnegative(a){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:t.toString(a)})}multipleOf(a,b){return this._addCheck({kind:"multipleOf",value:a,message:t.toString(b)})}get minValue(){let a=null;for(let b of this._def.checks)"min"===b.kind&&(null===a||b.value>a)&&(a=b.value);return a}get maxValue(){let a=null;for(let b of this._def.checks)"max"===b.kind&&(null===a||b.value<a)&&(a=b.value);return a}}cm.create=a=>new cm({checks:[],typeName:u.ZodBigInt,coerce:a?.coerce??!1,...b1(a)});class cn extends b2{_parse(a){if(this._def.coerce&&(a.data=!!a.data),this._getType(a)!==bP.boolean){let b=this._getOrReturnCtx(a);return bV(b,{code:bR.invalid_type,expected:bP.boolean,received:b.parsedType}),bX}return bZ(a.data)}}cn.create=a=>new cn({typeName:u.ZodBoolean,coerce:a?.coerce||!1,...b1(a)});class co extends b2{_parse(a){let b;if(this._def.coerce&&(a.data=new Date(a.data)),this._getType(a)!==bP.date){let b=this._getOrReturnCtx(a);return bV(b,{code:bR.invalid_type,expected:bP.date,received:b.parsedType}),bX}if(Number.isNaN(a.data.getTime()))return bV(this._getOrReturnCtx(a),{code:bR.invalid_date}),bX;let c=new bW;for(let d of this._def.checks)"min"===d.kind?a.data.getTime()<d.value&&(bV(b=this._getOrReturnCtx(a,b),{code:bR.too_small,message:d.message,inclusive:!0,exact:!1,minimum:d.value,type:"date"}),c.dirty()):"max"===d.kind?a.data.getTime()>d.value&&(bV(b=this._getOrReturnCtx(a,b),{code:bR.too_big,message:d.message,inclusive:!0,exact:!1,maximum:d.value,type:"date"}),c.dirty()):r.assertNever(d);return{status:c.value,value:new Date(a.data.getTime())}}_addCheck(a){return new co({...this._def,checks:[...this._def.checks,a]})}min(a,b){return this._addCheck({kind:"min",value:a.getTime(),message:t.toString(b)})}max(a,b){return this._addCheck({kind:"max",value:a.getTime(),message:t.toString(b)})}get minDate(){let a=null;for(let b of this._def.checks)"min"===b.kind&&(null===a||b.value>a)&&(a=b.value);return null!=a?new Date(a):null}get maxDate(){let a=null;for(let b of this._def.checks)"max"===b.kind&&(null===a||b.value<a)&&(a=b.value);return null!=a?new Date(a):null}}co.create=a=>new co({checks:[],coerce:a?.coerce||!1,typeName:u.ZodDate,...b1(a)});class cp extends b2{_parse(a){if(this._getType(a)!==bP.symbol){let b=this._getOrReturnCtx(a);return bV(b,{code:bR.invalid_type,expected:bP.symbol,received:b.parsedType}),bX}return bZ(a.data)}}cp.create=a=>new cp({typeName:u.ZodSymbol,...b1(a)});class cq extends b2{_parse(a){if(this._getType(a)!==bP.undefined){let b=this._getOrReturnCtx(a);return bV(b,{code:bR.invalid_type,expected:bP.undefined,received:b.parsedType}),bX}return bZ(a.data)}}cq.create=a=>new cq({typeName:u.ZodUndefined,...b1(a)});class cr extends b2{_parse(a){if(this._getType(a)!==bP.null){let b=this._getOrReturnCtx(a);return bV(b,{code:bR.invalid_type,expected:bP.null,received:b.parsedType}),bX}return bZ(a.data)}}cr.create=a=>new cr({typeName:u.ZodNull,...b1(a)});class cs extends b2{constructor(){super(...arguments),this._any=!0}_parse(a){return bZ(a.data)}}cs.create=a=>new cs({typeName:u.ZodAny,...b1(a)});class ct extends b2{constructor(){super(...arguments),this._unknown=!0}_parse(a){return bZ(a.data)}}ct.create=a=>new ct({typeName:u.ZodUnknown,...b1(a)});class cu extends b2{_parse(a){let b=this._getOrReturnCtx(a);return bV(b,{code:bR.invalid_type,expected:bP.never,received:b.parsedType}),bX}}cu.create=a=>new cu({typeName:u.ZodNever,...b1(a)});class cv extends b2{_parse(a){if(this._getType(a)!==bP.undefined){let b=this._getOrReturnCtx(a);return bV(b,{code:bR.invalid_type,expected:bP.void,received:b.parsedType}),bX}return bZ(a.data)}}cv.create=a=>new cv({typeName:u.ZodVoid,...b1(a)});class cw extends b2{_parse(a){let{ctx:b,status:c}=this._processInputParams(a),d=this._def;if(b.parsedType!==bP.array)return bV(b,{code:bR.invalid_type,expected:bP.array,received:b.parsedType}),bX;if(null!==d.exactLength){let a=b.data.length>d.exactLength.value,e=b.data.length<d.exactLength.value;(a||e)&&(bV(b,{code:a?bR.too_big:bR.too_small,minimum:e?d.exactLength.value:void 0,maximum:a?d.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:d.exactLength.message}),c.dirty())}if(null!==d.minLength&&b.data.length<d.minLength.value&&(bV(b,{code:bR.too_small,minimum:d.minLength.value,type:"array",inclusive:!0,exact:!1,message:d.minLength.message}),c.dirty()),null!==d.maxLength&&b.data.length>d.maxLength.value&&(bV(b,{code:bR.too_big,maximum:d.maxLength.value,type:"array",inclusive:!0,exact:!1,message:d.maxLength.message}),c.dirty()),b.common.async)return Promise.all([...b.data].map((a,c)=>d.type._parseAsync(new b_(b,a,b.path,c)))).then(a=>bW.mergeArray(c,a));let e=[...b.data].map((a,c)=>d.type._parseSync(new b_(b,a,b.path,c)));return bW.mergeArray(c,e)}get element(){return this._def.type}min(a,b){return new cw({...this._def,minLength:{value:a,message:t.toString(b)}})}max(a,b){return new cw({...this._def,maxLength:{value:a,message:t.toString(b)}})}length(a,b){return new cw({...this._def,exactLength:{value:a,message:t.toString(b)}})}nonempty(a){return this.min(1,a)}}cw.create=(a,b)=>new cw({type:a,minLength:null,maxLength:null,exactLength:null,typeName:u.ZodArray,...b1(b)});class cx extends b2{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let a=this._def.shape(),b=r.objectKeys(a);return this._cached={shape:a,keys:b},this._cached}_parse(a){if(this._getType(a)!==bP.object){let b=this._getOrReturnCtx(a);return bV(b,{code:bR.invalid_type,expected:bP.object,received:b.parsedType}),bX}let{status:b,ctx:c}=this._processInputParams(a),{shape:d,keys:e}=this._getCached(),f=[];if(!(this._def.catchall instanceof cu&&"strip"===this._def.unknownKeys))for(let a in c.data)e.includes(a)||f.push(a);let g=[];for(let a of e){let b=d[a],e=c.data[a];g.push({key:{status:"valid",value:a},value:b._parse(new b_(c,e,c.path,a)),alwaysSet:a in c.data})}if(this._def.catchall instanceof cu){let a=this._def.unknownKeys;if("passthrough"===a)for(let a of f)g.push({key:{status:"valid",value:a},value:{status:"valid",value:c.data[a]}});else if("strict"===a)f.length>0&&(bV(c,{code:bR.unrecognized_keys,keys:f}),b.dirty());else if("strip"===a);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let a=this._def.catchall;for(let b of f){let d=c.data[b];g.push({key:{status:"valid",value:b},value:a._parse(new b_(c,d,c.path,b)),alwaysSet:b in c.data})}}return c.common.async?Promise.resolve().then(async()=>{let a=[];for(let b of g){let c=await b.key,d=await b.value;a.push({key:c,value:d,alwaysSet:b.alwaysSet})}return a}).then(a=>bW.mergeObjectSync(b,a)):bW.mergeObjectSync(b,g)}get shape(){return this._def.shape()}strict(a){return t.errToObj,new cx({...this._def,unknownKeys:"strict",...void 0!==a?{errorMap:(b,c)=>{let d=this._def.errorMap?.(b,c).message??c.defaultError;return"unrecognized_keys"===b.code?{message:t.errToObj(a).message??d}:{message:d}}}:{}})}strip(){return new cx({...this._def,unknownKeys:"strip"})}passthrough(){return new cx({...this._def,unknownKeys:"passthrough"})}extend(a){return new cx({...this._def,shape:()=>({...this._def.shape(),...a})})}merge(a){return new cx({unknownKeys:a._def.unknownKeys,catchall:a._def.catchall,shape:()=>({...this._def.shape(),...a._def.shape()}),typeName:u.ZodObject})}setKey(a,b){return this.augment({[a]:b})}catchall(a){return new cx({...this._def,catchall:a})}pick(a){let b={};for(let c of r.objectKeys(a))a[c]&&this.shape[c]&&(b[c]=this.shape[c]);return new cx({...this._def,shape:()=>b})}omit(a){let b={};for(let c of r.objectKeys(this.shape))a[c]||(b[c]=this.shape[c]);return new cx({...this._def,shape:()=>b})}deepPartial(){return function a(b){if(b instanceof cx){let c={};for(let d in b.shape){let e=b.shape[d];c[d]=cO.create(a(e))}return new cx({...b._def,shape:()=>c})}if(b instanceof cw)return new cw({...b._def,type:a(b.element)});if(b instanceof cO)return cO.create(a(b.unwrap()));if(b instanceof cP)return cP.create(a(b.unwrap()));if(b instanceof cC)return cC.create(b.items.map(b=>a(b)));else return b}(this)}partial(a){let b={};for(let c of r.objectKeys(this.shape)){let d=this.shape[c];a&&!a[c]?b[c]=d:b[c]=d.optional()}return new cx({...this._def,shape:()=>b})}required(a){let b={};for(let c of r.objectKeys(this.shape))if(a&&!a[c])b[c]=this.shape[c];else{let a=this.shape[c];for(;a instanceof cO;)a=a._def.innerType;b[c]=a}return new cx({...this._def,shape:()=>b})}keyof(){return cJ(r.objectKeys(this.shape))}}cx.create=(a,b)=>new cx({shape:()=>a,unknownKeys:"strip",catchall:cu.create(),typeName:u.ZodObject,...b1(b)}),cx.strictCreate=(a,b)=>new cx({shape:()=>a,unknownKeys:"strict",catchall:cu.create(),typeName:u.ZodObject,...b1(b)}),cx.lazycreate=(a,b)=>new cx({shape:a,unknownKeys:"strip",catchall:cu.create(),typeName:u.ZodObject,...b1(b)});class cy extends b2{_parse(a){let{ctx:b}=this._processInputParams(a),c=this._def.options;if(b.common.async)return Promise.all(c.map(async a=>{let c={...b,common:{...b.common,issues:[]},parent:null};return{result:await a._parseAsync({data:b.data,path:b.path,parent:c}),ctx:c}})).then(function(a){for(let b of a)if("valid"===b.result.status)return b.result;for(let c of a)if("dirty"===c.result.status)return b.common.issues.push(...c.ctx.common.issues),c.result;let c=a.map(a=>new bS(a.ctx.common.issues));return bV(b,{code:bR.invalid_union,unionErrors:c}),bX});{let a,d=[];for(let e of c){let c={...b,common:{...b.common,issues:[]},parent:null},f=e._parseSync({data:b.data,path:b.path,parent:c});if("valid"===f.status)return f;"dirty"!==f.status||a||(a={result:f,ctx:c}),c.common.issues.length&&d.push(c.common.issues)}if(a)return b.common.issues.push(...a.ctx.common.issues),a.result;let e=d.map(a=>new bS(a));return bV(b,{code:bR.invalid_union,unionErrors:e}),bX}}get options(){return this._def.options}}cy.create=(a,b)=>new cy({options:a,typeName:u.ZodUnion,...b1(b)});let cz=a=>{if(a instanceof cH)return cz(a.schema);if(a instanceof cN)return cz(a.innerType());if(a instanceof cI)return[a.value];if(a instanceof cK)return a.options;if(a instanceof cL)return r.objectValues(a.enum);else if(a instanceof cQ)return cz(a._def.innerType);else if(a instanceof cq)return[void 0];else if(a instanceof cr)return[null];else if(a instanceof cO)return[void 0,...cz(a.unwrap())];else if(a instanceof cP)return[null,...cz(a.unwrap())];else if(a instanceof cT)return cz(a.unwrap());else if(a instanceof cV)return cz(a.unwrap());else if(a instanceof cR)return cz(a._def.innerType);else return[]};class cA extends b2{_parse(a){let{ctx:b}=this._processInputParams(a);if(b.parsedType!==bP.object)return bV(b,{code:bR.invalid_type,expected:bP.object,received:b.parsedType}),bX;let c=this.discriminator,d=b.data[c],e=this.optionsMap.get(d);return e?b.common.async?e._parseAsync({data:b.data,path:b.path,parent:b}):e._parseSync({data:b.data,path:b.path,parent:b}):(bV(b,{code:bR.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[c]}),bX)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(a,b,c){let d=new Map;for(let c of b){let b=cz(c.shape[a]);if(!b.length)throw Error(`A discriminator value for key \`${a}\` could not be extracted from all schema options`);for(let e of b){if(d.has(e))throw Error(`Discriminator property ${String(a)} has duplicate value ${String(e)}`);d.set(e,c)}}return new cA({typeName:u.ZodDiscriminatedUnion,discriminator:a,options:b,optionsMap:d,...b1(c)})}}class cB extends b2{_parse(a){let{status:b,ctx:c}=this._processInputParams(a),d=(a,d)=>{if("aborted"===a.status||"aborted"===d.status)return bX;let e=function a(b,c){let d=bQ(b),e=bQ(c);if(b===c)return{valid:!0,data:b};if(d===bP.object&&e===bP.object){let d=r.objectKeys(c),e=r.objectKeys(b).filter(a=>-1!==d.indexOf(a)),f={...b,...c};for(let d of e){let e=a(b[d],c[d]);if(!e.valid)return{valid:!1};f[d]=e.data}return{valid:!0,data:f}}if(d===bP.array&&e===bP.array){if(b.length!==c.length)return{valid:!1};let d=[];for(let e=0;e<b.length;e++){let f=a(b[e],c[e]);if(!f.valid)return{valid:!1};d.push(f.data)}return{valid:!0,data:d}}if(d===bP.date&&e===bP.date&&+b==+c)return{valid:!0,data:b};return{valid:!1}}(a.value,d.value);return e.valid?(("dirty"===a.status||"dirty"===d.status)&&b.dirty(),{status:b.value,value:e.data}):(bV(c,{code:bR.invalid_intersection_types}),bX)};return c.common.async?Promise.all([this._def.left._parseAsync({data:c.data,path:c.path,parent:c}),this._def.right._parseAsync({data:c.data,path:c.path,parent:c})]).then(([a,b])=>d(a,b)):d(this._def.left._parseSync({data:c.data,path:c.path,parent:c}),this._def.right._parseSync({data:c.data,path:c.path,parent:c}))}}cB.create=(a,b,c)=>new cB({left:a,right:b,typeName:u.ZodIntersection,...b1(c)});class cC extends b2{_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.parsedType!==bP.array)return bV(c,{code:bR.invalid_type,expected:bP.array,received:c.parsedType}),bX;if(c.data.length<this._def.items.length)return bV(c,{code:bR.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),bX;!this._def.rest&&c.data.length>this._def.items.length&&(bV(c,{code:bR.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),b.dirty());let d=[...c.data].map((a,b)=>{let d=this._def.items[b]||this._def.rest;return d?d._parse(new b_(c,a,c.path,b)):null}).filter(a=>!!a);return c.common.async?Promise.all(d).then(a=>bW.mergeArray(b,a)):bW.mergeArray(b,d)}get items(){return this._def.items}rest(a){return new cC({...this._def,rest:a})}}cC.create=(a,b)=>{if(!Array.isArray(a))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new cC({items:a,typeName:u.ZodTuple,rest:null,...b1(b)})};class cD extends b2{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.parsedType!==bP.object)return bV(c,{code:bR.invalid_type,expected:bP.object,received:c.parsedType}),bX;let d=[],e=this._def.keyType,f=this._def.valueType;for(let a in c.data)d.push({key:e._parse(new b_(c,a,c.path,a)),value:f._parse(new b_(c,c.data[a],c.path,a)),alwaysSet:a in c.data});return c.common.async?bW.mergeObjectAsync(b,d):bW.mergeObjectSync(b,d)}get element(){return this._def.valueType}static create(a,b,c){return new cD(b instanceof b2?{keyType:a,valueType:b,typeName:u.ZodRecord,...b1(c)}:{keyType:ck.create(),valueType:a,typeName:u.ZodRecord,...b1(b)})}}class cE extends b2{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.parsedType!==bP.map)return bV(c,{code:bR.invalid_type,expected:bP.map,received:c.parsedType}),bX;let d=this._def.keyType,e=this._def.valueType,f=[...c.data.entries()].map(([a,b],f)=>({key:d._parse(new b_(c,a,c.path,[f,"key"])),value:e._parse(new b_(c,b,c.path,[f,"value"]))}));if(c.common.async){let a=new Map;return Promise.resolve().then(async()=>{for(let c of f){let d=await c.key,e=await c.value;if("aborted"===d.status||"aborted"===e.status)return bX;("dirty"===d.status||"dirty"===e.status)&&b.dirty(),a.set(d.value,e.value)}return{status:b.value,value:a}})}{let a=new Map;for(let c of f){let d=c.key,e=c.value;if("aborted"===d.status||"aborted"===e.status)return bX;("dirty"===d.status||"dirty"===e.status)&&b.dirty(),a.set(d.value,e.value)}return{status:b.value,value:a}}}}cE.create=(a,b,c)=>new cE({valueType:b,keyType:a,typeName:u.ZodMap,...b1(c)});class cF extends b2{_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.parsedType!==bP.set)return bV(c,{code:bR.invalid_type,expected:bP.set,received:c.parsedType}),bX;let d=this._def;null!==d.minSize&&c.data.size<d.minSize.value&&(bV(c,{code:bR.too_small,minimum:d.minSize.value,type:"set",inclusive:!0,exact:!1,message:d.minSize.message}),b.dirty()),null!==d.maxSize&&c.data.size>d.maxSize.value&&(bV(c,{code:bR.too_big,maximum:d.maxSize.value,type:"set",inclusive:!0,exact:!1,message:d.maxSize.message}),b.dirty());let e=this._def.valueType;function f(a){let c=new Set;for(let d of a){if("aborted"===d.status)return bX;"dirty"===d.status&&b.dirty(),c.add(d.value)}return{status:b.value,value:c}}let g=[...c.data.values()].map((a,b)=>e._parse(new b_(c,a,c.path,b)));return c.common.async?Promise.all(g).then(a=>f(a)):f(g)}min(a,b){return new cF({...this._def,minSize:{value:a,message:t.toString(b)}})}max(a,b){return new cF({...this._def,maxSize:{value:a,message:t.toString(b)}})}size(a,b){return this.min(a,b).max(a,b)}nonempty(a){return this.min(1,a)}}cF.create=(a,b)=>new cF({valueType:a,minSize:null,maxSize:null,typeName:u.ZodSet,...b1(b)});class cG extends b2{constructor(){super(...arguments),this.validate=this.implement}_parse(a){let{ctx:b}=this._processInputParams(a);if(b.parsedType!==bP.function)return bV(b,{code:bR.invalid_type,expected:bP.function,received:b.parsedType}),bX;function c(a,c){return bU({data:a,path:b.path,errorMaps:[b.common.contextualErrorMap,b.schemaErrorMap,bT,bT].filter(a=>!!a),issueData:{code:bR.invalid_arguments,argumentsError:c}})}function d(a,c){return bU({data:a,path:b.path,errorMaps:[b.common.contextualErrorMap,b.schemaErrorMap,bT,bT].filter(a=>!!a),issueData:{code:bR.invalid_return_type,returnTypeError:c}})}let e={errorMap:b.common.contextualErrorMap},f=b.data;if(this._def.returns instanceof cM){let a=this;return bZ(async function(...b){let g=new bS([]),h=await a._def.args.parseAsync(b,e).catch(a=>{throw g.addIssue(c(b,a)),g}),i=await Reflect.apply(f,this,h);return await a._def.returns._def.type.parseAsync(i,e).catch(a=>{throw g.addIssue(d(i,a)),g})})}{let a=this;return bZ(function(...b){let g=a._def.args.safeParse(b,e);if(!g.success)throw new bS([c(b,g.error)]);let h=Reflect.apply(f,this,g.data),i=a._def.returns.safeParse(h,e);if(!i.success)throw new bS([d(h,i.error)]);return i.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...a){return new cG({...this._def,args:cC.create(a).rest(ct.create())})}returns(a){return new cG({...this._def,returns:a})}implement(a){return this.parse(a)}strictImplement(a){return this.parse(a)}static create(a,b,c){return new cG({args:a||cC.create([]).rest(ct.create()),returns:b||ct.create(),typeName:u.ZodFunction,...b1(c)})}}class cH extends b2{get schema(){return this._def.getter()}_parse(a){let{ctx:b}=this._processInputParams(a);return this._def.getter()._parse({data:b.data,path:b.path,parent:b})}}cH.create=(a,b)=>new cH({getter:a,typeName:u.ZodLazy,...b1(b)});class cI extends b2{_parse(a){if(a.data!==this._def.value){let b=this._getOrReturnCtx(a);return bV(b,{received:b.data,code:bR.invalid_literal,expected:this._def.value}),bX}return{status:"valid",value:a.data}}get value(){return this._def.value}}function cJ(a,b){return new cK({values:a,typeName:u.ZodEnum,...b1(b)})}cI.create=(a,b)=>new cI({value:a,typeName:u.ZodLiteral,...b1(b)});class cK extends b2{_parse(a){if("string"!=typeof a.data){let b=this._getOrReturnCtx(a),c=this._def.values;return bV(b,{expected:r.joinValues(c),received:b.parsedType,code:bR.invalid_type}),bX}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(a.data)){let b=this._getOrReturnCtx(a),c=this._def.values;return bV(b,{received:b.data,code:bR.invalid_enum_value,options:c}),bX}return bZ(a.data)}get options(){return this._def.values}get enum(){let a={};for(let b of this._def.values)a[b]=b;return a}get Values(){let a={};for(let b of this._def.values)a[b]=b;return a}get Enum(){let a={};for(let b of this._def.values)a[b]=b;return a}extract(a,b=this._def){return cK.create(a,{...this._def,...b})}exclude(a,b=this._def){return cK.create(this.options.filter(b=>!a.includes(b)),{...this._def,...b})}}cK.create=cJ;class cL extends b2{_parse(a){let b=r.getValidEnumValues(this._def.values),c=this._getOrReturnCtx(a);if(c.parsedType!==bP.string&&c.parsedType!==bP.number){let a=r.objectValues(b);return bV(c,{expected:r.joinValues(a),received:c.parsedType,code:bR.invalid_type}),bX}if(this._cache||(this._cache=new Set(r.getValidEnumValues(this._def.values))),!this._cache.has(a.data)){let a=r.objectValues(b);return bV(c,{received:c.data,code:bR.invalid_enum_value,options:a}),bX}return bZ(a.data)}get enum(){return this._def.values}}cL.create=(a,b)=>new cL({values:a,typeName:u.ZodNativeEnum,...b1(b)});class cM extends b2{unwrap(){return this._def.type}_parse(a){let{ctx:b}=this._processInputParams(a);return b.parsedType!==bP.promise&&!1===b.common.async?(bV(b,{code:bR.invalid_type,expected:bP.promise,received:b.parsedType}),bX):bZ((b.parsedType===bP.promise?b.data:Promise.resolve(b.data)).then(a=>this._def.type.parseAsync(a,{path:b.path,errorMap:b.common.contextualErrorMap})))}}cM.create=(a,b)=>new cM({type:a,typeName:u.ZodPromise,...b1(b)});class cN extends b2{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===u.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(a){let{status:b,ctx:c}=this._processInputParams(a),d=this._def.effect||null,e={addIssue:a=>{bV(c,a),a.fatal?b.abort():b.dirty()},get path(){return c.path}};if(e.addIssue=e.addIssue.bind(e),"preprocess"===d.type){let a=d.transform(c.data,e);if(c.common.async)return Promise.resolve(a).then(async a=>{if("aborted"===b.value)return bX;let d=await this._def.schema._parseAsync({data:a,path:c.path,parent:c});return"aborted"===d.status?bX:"dirty"===d.status||"dirty"===b.value?bY(d.value):d});{if("aborted"===b.value)return bX;let d=this._def.schema._parseSync({data:a,path:c.path,parent:c});return"aborted"===d.status?bX:"dirty"===d.status||"dirty"===b.value?bY(d.value):d}}if("refinement"===d.type){let a=a=>{let b=d.refinement(a,e);if(c.common.async)return Promise.resolve(b);if(b instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return a};if(!1!==c.common.async)return this._def.schema._parseAsync({data:c.data,path:c.path,parent:c}).then(c=>"aborted"===c.status?bX:("dirty"===c.status&&b.dirty(),a(c.value).then(()=>({status:b.value,value:c.value}))));{let d=this._def.schema._parseSync({data:c.data,path:c.path,parent:c});return"aborted"===d.status?bX:("dirty"===d.status&&b.dirty(),a(d.value),{status:b.value,value:d.value})}}if("transform"===d.type)if(!1!==c.common.async)return this._def.schema._parseAsync({data:c.data,path:c.path,parent:c}).then(a=>"valid"!==a.status?bX:Promise.resolve(d.transform(a.value,e)).then(a=>({status:b.value,value:a})));else{let a=this._def.schema._parseSync({data:c.data,path:c.path,parent:c});if("valid"!==a.status)return bX;let f=d.transform(a.value,e);if(f instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:b.value,value:f}}r.assertNever(d)}}cN.create=(a,b,c)=>new cN({schema:a,typeName:u.ZodEffects,effect:b,...b1(c)}),cN.createWithPreprocess=(a,b,c)=>new cN({schema:b,effect:{type:"preprocess",transform:a},typeName:u.ZodEffects,...b1(c)});class cO extends b2{_parse(a){return this._getType(a)===bP.undefined?bZ(void 0):this._def.innerType._parse(a)}unwrap(){return this._def.innerType}}cO.create=(a,b)=>new cO({innerType:a,typeName:u.ZodOptional,...b1(b)});class cP extends b2{_parse(a){return this._getType(a)===bP.null?bZ(null):this._def.innerType._parse(a)}unwrap(){return this._def.innerType}}cP.create=(a,b)=>new cP({innerType:a,typeName:u.ZodNullable,...b1(b)});class cQ extends b2{_parse(a){let{ctx:b}=this._processInputParams(a),c=b.data;return b.parsedType===bP.undefined&&(c=this._def.defaultValue()),this._def.innerType._parse({data:c,path:b.path,parent:b})}removeDefault(){return this._def.innerType}}cQ.create=(a,b)=>new cQ({innerType:a,typeName:u.ZodDefault,defaultValue:"function"==typeof b.default?b.default:()=>b.default,...b1(b)});class cR extends b2{_parse(a){let{ctx:b}=this._processInputParams(a),c={...b,common:{...b.common,issues:[]}},d=this._def.innerType._parse({data:c.data,path:c.path,parent:{...c}});return b$(d)?d.then(a=>({status:"valid",value:"valid"===a.status?a.value:this._def.catchValue({get error(){return new bS(c.common.issues)},input:c.data})})):{status:"valid",value:"valid"===d.status?d.value:this._def.catchValue({get error(){return new bS(c.common.issues)},input:c.data})}}removeCatch(){return this._def.innerType}}cR.create=(a,b)=>new cR({innerType:a,typeName:u.ZodCatch,catchValue:"function"==typeof b.catch?b.catch:()=>b.catch,...b1(b)});class cS extends b2{_parse(a){if(this._getType(a)!==bP.nan){let b=this._getOrReturnCtx(a);return bV(b,{code:bR.invalid_type,expected:bP.nan,received:b.parsedType}),bX}return{status:"valid",value:a.data}}}cS.create=a=>new cS({typeName:u.ZodNaN,...b1(a)}),Symbol("zod_brand");class cT extends b2{_parse(a){let{ctx:b}=this._processInputParams(a),c=b.data;return this._def.type._parse({data:c,path:b.path,parent:b})}unwrap(){return this._def.type}}class cU extends b2{_parse(a){let{status:b,ctx:c}=this._processInputParams(a);if(c.common.async)return(async()=>{let a=await this._def.in._parseAsync({data:c.data,path:c.path,parent:c});return"aborted"===a.status?bX:"dirty"===a.status?(b.dirty(),bY(a.value)):this._def.out._parseAsync({data:a.value,path:c.path,parent:c})})();{let a=this._def.in._parseSync({data:c.data,path:c.path,parent:c});return"aborted"===a.status?bX:"dirty"===a.status?(b.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:c.path,parent:c})}}static create(a,b){return new cU({in:a,out:b,typeName:u.ZodPipeline})}}class cV extends b2{_parse(a){let b=this._def.innerType._parse(a),c=a=>("valid"===a.status&&(a.value=Object.freeze(a.value)),a);return b$(b)?b.then(a=>c(a)):c(b)}unwrap(){return this._def.innerType}}cV.create=(a,b)=>new cV({innerType:a,typeName:u.ZodReadonly,...b1(b)}),cx.lazycreate,function(a){a.ZodString="ZodString",a.ZodNumber="ZodNumber",a.ZodNaN="ZodNaN",a.ZodBigInt="ZodBigInt",a.ZodBoolean="ZodBoolean",a.ZodDate="ZodDate",a.ZodSymbol="ZodSymbol",a.ZodUndefined="ZodUndefined",a.ZodNull="ZodNull",a.ZodAny="ZodAny",a.ZodUnknown="ZodUnknown",a.ZodNever="ZodNever",a.ZodVoid="ZodVoid",a.ZodArray="ZodArray",a.ZodObject="ZodObject",a.ZodUnion="ZodUnion",a.ZodDiscriminatedUnion="ZodDiscriminatedUnion",a.ZodIntersection="ZodIntersection",a.ZodTuple="ZodTuple",a.ZodRecord="ZodRecord",a.ZodMap="ZodMap",a.ZodSet="ZodSet",a.ZodFunction="ZodFunction",a.ZodLazy="ZodLazy",a.ZodLiteral="ZodLiteral",a.ZodEnum="ZodEnum",a.ZodEffects="ZodEffects",a.ZodNativeEnum="ZodNativeEnum",a.ZodOptional="ZodOptional",a.ZodNullable="ZodNullable",a.ZodDefault="ZodDefault",a.ZodCatch="ZodCatch",a.ZodPromise="ZodPromise",a.ZodBranded="ZodBranded",a.ZodPipeline="ZodPipeline",a.ZodReadonly="ZodReadonly"}(u||(u={})),ck.create,cl.create,cS.create,cm.create,cn.create,co.create,cp.create,cq.create,cr.create,cs.create,ct.create,cu.create,cv.create,cw.create,cx.create,cx.strictCreate,cy.create,cA.create,cB.create,cC.create,cD.create,cE.create,cF.create,cG.create,cH.create,cI.create,cK.create,cL.create,cM.create,cN.create,cO.create,cP.create,cN.createWithPreprocess,cU.create;let cW=(a,b)=>{let c=0;for(;c<a.length&&c<b.length&&a[c]===b[c];c++);return[(a.length-c).toString(),...b.slice(c)].join("/")};function cX(a){if("openAi"!==a.target)return{};let b=[...a.basePath,a.definitionPath,a.openAiAnyTypeName];return a.flags.hasReferencedOpenAiAnyType=!0,{$ref:"relative"===a.$refStrategy?cW(b,a.currentPath):b.join("/")}}function cY(a,b,c,d){d?.errorMessages&&c&&(a.errorMessage={...a.errorMessage,[b]:c})}function cZ(a,b,c,d,e){a[b]=c,cY(a,b,d,e)}function c$(a,b){return c9(a.type._def,b)}let c_={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(void 0===e&&(e=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),e),ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function c0(a,b){let c={type:"string"};if(a.checks)for(let d of a.checks)switch(d.kind){case"min":cZ(c,"minLength","number"==typeof c.minLength?Math.max(c.minLength,d.value):d.value,d.message,b);break;case"max":cZ(c,"maxLength","number"==typeof c.maxLength?Math.min(c.maxLength,d.value):d.value,d.message,b);break;case"email":switch(b.emailStrategy){case"format:email":c3(c,"email",d.message,b);break;case"format:idn-email":c3(c,"idn-email",d.message,b);break;case"pattern:zod":c4(c,c_.email,d.message,b)}break;case"url":c3(c,"uri",d.message,b);break;case"uuid":c3(c,"uuid",d.message,b);break;case"regex":c4(c,d.regex,d.message,b);break;case"cuid":c4(c,c_.cuid,d.message,b);break;case"cuid2":c4(c,c_.cuid2,d.message,b);break;case"startsWith":c4(c,RegExp(`^${c1(d.value,b)}`),d.message,b);break;case"endsWith":c4(c,RegExp(`${c1(d.value,b)}$`),d.message,b);break;case"datetime":c3(c,"date-time",d.message,b);break;case"date":c3(c,"date",d.message,b);break;case"time":c3(c,"time",d.message,b);break;case"duration":c3(c,"duration",d.message,b);break;case"length":cZ(c,"minLength","number"==typeof c.minLength?Math.max(c.minLength,d.value):d.value,d.message,b),cZ(c,"maxLength","number"==typeof c.maxLength?Math.min(c.maxLength,d.value):d.value,d.message,b);break;case"includes":c4(c,RegExp(c1(d.value,b)),d.message,b);break;case"ip":"v6"!==d.version&&c3(c,"ipv4",d.message,b),"v4"!==d.version&&c3(c,"ipv6",d.message,b);break;case"base64url":c4(c,c_.base64url,d.message,b);break;case"jwt":c4(c,c_.jwt,d.message,b);break;case"cidr":"v6"!==d.version&&c4(c,c_.ipv4Cidr,d.message,b),"v4"!==d.version&&c4(c,c_.ipv6Cidr,d.message,b);break;case"emoji":c4(c,c_.emoji(),d.message,b);break;case"ulid":c4(c,c_.ulid,d.message,b);break;case"base64":switch(b.base64Strategy){case"format:binary":c3(c,"binary",d.message,b);break;case"contentEncoding:base64":cZ(c,"contentEncoding","base64",d.message,b);break;case"pattern:zod":c4(c,c_.base64,d.message,b)}break;case"nanoid":c4(c,c_.nanoid,d.message,b)}return c}function c1(a,b){return"escape"===b.patternStrategy?function(a){let b="";for(let c=0;c<a.length;c++)c2.has(a[c])||(b+="\\"),b+=a[c];return b}(a):a}let c2=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function c3(a,b,c,d){a.format||a.anyOf?.some(a=>a.format)?(a.anyOf||(a.anyOf=[]),a.format&&(a.anyOf.push({format:a.format,...a.errorMessage&&d.errorMessages&&{errorMessage:{format:a.errorMessage.format}}}),delete a.format,a.errorMessage&&(delete a.errorMessage.format,0===Object.keys(a.errorMessage).length&&delete a.errorMessage)),a.anyOf.push({format:b,...c&&d.errorMessages&&{errorMessage:{format:c}}})):cZ(a,"format",b,c,d)}function c4(a,b,c,d){a.pattern||a.allOf?.some(a=>a.pattern)?(a.allOf||(a.allOf=[]),a.pattern&&(a.allOf.push({pattern:a.pattern,...a.errorMessage&&d.errorMessages&&{errorMessage:{pattern:a.errorMessage.pattern}}}),delete a.pattern,a.errorMessage&&(delete a.errorMessage.pattern,0===Object.keys(a.errorMessage).length&&delete a.errorMessage)),a.allOf.push({pattern:c5(b,d),...c&&d.errorMessages&&{errorMessage:{pattern:c}}})):cZ(a,"pattern",c5(b,d),c,d)}function c5(a,b){if(!b.applyRegexFlags||!a.flags)return a.source;let c={i:a.flags.includes("i"),m:a.flags.includes("m"),s:a.flags.includes("s")},d=c.i?a.source.toLowerCase():a.source,e="",f=!1,g=!1,h=!1;for(let a=0;a<d.length;a++){if(f){e+=d[a],f=!1;continue}if(c.i){if(g){if(d[a].match(/[a-z]/)){h?(e+=d[a],e+=`${d[a-2]}-${d[a]}`.toUpperCase(),h=!1):"-"===d[a+1]&&d[a+2]?.match(/[a-z]/)?(e+=d[a],h=!0):e+=`${d[a]}${d[a].toUpperCase()}`;continue}}else if(d[a].match(/[a-z]/)){e+=`[${d[a]}${d[a].toUpperCase()}]`;continue}}if(c.m){if("^"===d[a]){e+=`(^|(?<=[\r
]))`;continue}else if("$"===d[a]){e+=`($|(?=[\r
]))`;continue}}if(c.s&&"."===d[a]){e+=g?`${d[a]}\r
`:`[${d[a]}\r
]`;continue}e+=d[a],"\\"===d[a]?f=!0:g&&"]"===d[a]?g=!1:g||"["!==d[a]||(g=!0)}try{new RegExp(e)}catch{return console.warn(`Could not convert regex pattern at ${b.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),a.source}return e}function c6(a,b){if("openAi"===b.target&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),"openApi3"===b.target&&a.keyType?._def.typeName===u.ZodEnum)return{type:"object",required:a.keyType._def.values,properties:a.keyType._def.values.reduce((c,d)=>({...c,[d]:c9(a.valueType._def,{...b,currentPath:[...b.currentPath,"properties",d]})??cX(b)}),{}),additionalProperties:b.rejectedAdditionalProperties};let c={type:"object",additionalProperties:c9(a.valueType._def,{...b,currentPath:[...b.currentPath,"additionalProperties"]})??b.allowedAdditionalProperties};if("openApi3"===b.target)return c;if(a.keyType?._def.typeName===u.ZodString&&a.keyType._def.checks?.length){let{type:d,...e}=c0(a.keyType._def,b);return{...c,propertyNames:e}}if(a.keyType?._def.typeName===u.ZodEnum)return{...c,propertyNames:{enum:a.keyType._def.values}};if(a.keyType?._def.typeName===u.ZodBranded&&a.keyType._def.type._def.typeName===u.ZodString&&a.keyType._def.type._def.checks?.length){let{type:d,...e}=c$(a.keyType._def,b);return{...c,propertyNames:e}}return c}let c7={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"},c8=(a,b)=>{let c=(a.options instanceof Map?Array.from(a.options.values()):a.options).map((a,c)=>c9(a._def,{...b,currentPath:[...b.currentPath,"anyOf",`${c}`]})).filter(a=>!!a&&(!b.strictUnions||"object"==typeof a&&Object.keys(a).length>0));return c.length?{anyOf:c}:void 0};function c9(a,b,c=!1){let d=b.seen.get(a);if(b.override){let e=b.override?.(a,b,d,c);if(e!==bN)return e}if(d&&!c){let a=da(d,b);if(void 0!==a)return a}let e={def:a,path:b.currentPath,jsonSchema:void 0};b.seen.set(a,e);let f=((a,b,c)=>{switch(b){case u.ZodString:return c0(a,c);case u.ZodNumber:var d,e,f,g,h,i,j,k,l,m=a,n=c;let o={type:"number"};if(!m.checks)return o;for(let a of m.checks)switch(a.kind){case"int":o.type="integer",cY(o,"type",a.message,n);break;case"min":"jsonSchema7"===n.target?a.inclusive?cZ(o,"minimum",a.value,a.message,n):cZ(o,"exclusiveMinimum",a.value,a.message,n):(a.inclusive||(o.exclusiveMinimum=!0),cZ(o,"minimum",a.value,a.message,n));break;case"max":"jsonSchema7"===n.target?a.inclusive?cZ(o,"maximum",a.value,a.message,n):cZ(o,"exclusiveMaximum",a.value,a.message,n):(a.inclusive||(o.exclusiveMaximum=!0),cZ(o,"maximum",a.value,a.message,n));break;case"multipleOf":cZ(o,"multipleOf",a.value,a.message,n)}return o;case u.ZodObject:return function(a,b){let c="openAi"===b.target,d={type:"object",properties:{}},e=[],f=a.shape();for(let a in f){let g=f[a];if(void 0===g||void 0===g._def)continue;let h=function(a){try{return a.isOptional()}catch{return!0}}(g);h&&c&&("ZodOptional"===g._def.typeName&&(g=g._def.innerType),g.isNullable()||(g=g.nullable()),h=!1);let i=c9(g._def,{...b,currentPath:[...b.currentPath,"properties",a],propertyPath:[...b.currentPath,"properties",a]});void 0!==i&&(d.properties[a]=i,h||e.push(a))}e.length&&(d.required=e);let g=function(a,b){if("ZodNever"!==a.catchall._def.typeName)return c9(a.catchall._def,{...b,currentPath:[...b.currentPath,"additionalProperties"]});switch(a.unknownKeys){case"passthrough":return b.allowedAdditionalProperties;case"strict":return b.rejectedAdditionalProperties;case"strip":return"strict"===b.removeAdditionalStrategy?b.allowedAdditionalProperties:b.rejectedAdditionalProperties}}(a,b);return void 0!==g&&(d.additionalProperties=g),d}(a,c);case u.ZodBigInt:var p=a,q=c;let r={type:"integer",format:"int64"};if(!p.checks)return r;for(let a of p.checks)switch(a.kind){case"min":"jsonSchema7"===q.target?a.inclusive?cZ(r,"minimum",a.value,a.message,q):cZ(r,"exclusiveMinimum",a.value,a.message,q):(a.inclusive||(r.exclusiveMinimum=!0),cZ(r,"minimum",a.value,a.message,q));break;case"max":"jsonSchema7"===q.target?a.inclusive?cZ(r,"maximum",a.value,a.message,q):cZ(r,"exclusiveMaximum",a.value,a.message,q):(a.inclusive||(r.exclusiveMaximum=!0),cZ(r,"maximum",a.value,a.message,q));break;case"multipleOf":cZ(r,"multipleOf",a.value,a.message,q)}return r;case u.ZodBoolean:return{type:"boolean"};case u.ZodDate:return function a(b,c,d){let e=d??c.dateStrategy;if(Array.isArray(e))return{anyOf:e.map((d,e)=>a(b,c,d))};switch(e){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":var f=b,g=c;let h={type:"integer",format:"unix-time"};if("openApi3"===g.target)return h;for(let a of f.checks)switch(a.kind){case"min":cZ(h,"minimum",a.value,a.message,g);break;case"max":cZ(h,"maximum",a.value,a.message,g)}return h}}(a,c);case u.ZodUndefined:return{not:cX(c)};case u.ZodNull:return"openApi3"===c.target?{enum:["null"],nullable:!0}:{type:"null"};case u.ZodArray:var s=a,t=c;let v={type:"array"};return s.type?._def&&s.type?._def?.typeName!==u.ZodAny&&(v.items=c9(s.type._def,{...t,currentPath:[...t.currentPath,"items"]})),s.minLength&&cZ(v,"minItems",s.minLength.value,s.minLength.message,t),s.maxLength&&cZ(v,"maxItems",s.maxLength.value,s.maxLength.message,t),s.exactLength&&(cZ(v,"minItems",s.exactLength.value,s.exactLength.message,t),cZ(v,"maxItems",s.exactLength.value,s.exactLength.message,t)),v;case u.ZodUnion:case u.ZodDiscriminatedUnion:var w=a,x=c;if("openApi3"===x.target)return c8(w,x);let y=w.options instanceof Map?Array.from(w.options.values()):w.options;if(y.every(a=>a._def.typeName in c7&&(!a._def.checks||!a._def.checks.length))){let a=y.reduce((a,b)=>{let c=c7[b._def.typeName];return c&&!a.includes(c)?[...a,c]:a},[]);return{type:a.length>1?a:a[0]}}if(y.every(a=>"ZodLiteral"===a._def.typeName&&!a.description)){let a=y.reduce((a,b)=>{let c=typeof b._def.value;switch(c){case"string":case"number":case"boolean":return[...a,c];case"bigint":return[...a,"integer"];case"object":if(null===b._def.value)return[...a,"null"];default:return a}},[]);if(a.length===y.length){let b=a.filter((a,b,c)=>c.indexOf(a)===b);return{type:b.length>1?b:b[0],enum:y.reduce((a,b)=>a.includes(b._def.value)?a:[...a,b._def.value],[])}}}else if(y.every(a=>"ZodEnum"===a._def.typeName))return{type:"string",enum:y.reduce((a,b)=>[...a,...b._def.values.filter(b=>!a.includes(b))],[])};return c8(w,x);case u.ZodIntersection:var z=a,A=c;let B=[c9(z.left._def,{...A,currentPath:[...A.currentPath,"allOf","0"]}),c9(z.right._def,{...A,currentPath:[...A.currentPath,"allOf","1"]})].filter(a=>!!a),C="jsonSchema2019-09"===A.target?{unevaluatedProperties:!1}:void 0,D=[];return B.forEach(a=>{if((!("type"in a)||"string"!==a.type)&&"allOf"in a)D.push(...a.allOf),void 0===a.unevaluatedProperties&&(C=void 0);else{let b=a;if("additionalProperties"in a&&!1===a.additionalProperties){let{additionalProperties:c,...d}=a;b=d}else C=void 0;D.push(b)}}),D.length?{allOf:D,...C}:void 0;case u.ZodTuple:return d=a,e=c,d.rest?{type:"array",minItems:d.items.length,items:d.items.map((a,b)=>c9(a._def,{...e,currentPath:[...e.currentPath,"items",`${b}`]})).reduce((a,b)=>void 0===b?a:[...a,b],[]),additionalItems:c9(d.rest._def,{...e,currentPath:[...e.currentPath,"additionalItems"]})}:{type:"array",minItems:d.items.length,maxItems:d.items.length,items:d.items.map((a,b)=>c9(a._def,{...e,currentPath:[...e.currentPath,"items",`${b}`]})).reduce((a,b)=>void 0===b?a:[...a,b],[])};case u.ZodRecord:return c6(a,c);case u.ZodLiteral:var E=a,F=c;let G=typeof E.value;return"bigint"!==G&&"number"!==G&&"boolean"!==G&&"string"!==G?{type:Array.isArray(E.value)?"array":"object"}:"openApi3"===F.target?{type:"bigint"===G?"integer":G,enum:[E.value]}:{type:"bigint"===G?"integer":G,const:E.value};case u.ZodEnum:return{type:"string",enum:Array.from(a.values)};case u.ZodNativeEnum:var H=a;let I=H.values,J=Object.keys(H.values).filter(a=>"number"!=typeof I[I[a]]).map(a=>I[a]),K=Array.from(new Set(J.map(a=>typeof a)));return{type:1===K.length?"string"===K[0]?"string":"number":["string","number"],enum:J};case u.ZodNullable:var L=a,M=c;if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(L.innerType._def.typeName)&&(!L.innerType._def.checks||!L.innerType._def.checks.length))return"openApi3"===M.target?{type:c7[L.innerType._def.typeName],nullable:!0}:{type:[c7[L.innerType._def.typeName],"null"]};if("openApi3"===M.target){let a=c9(L.innerType._def,{...M,currentPath:[...M.currentPath]});return a&&"$ref"in a?{allOf:[a],nullable:!0}:a&&{...a,nullable:!0}}let N=c9(L.innerType._def,{...M,currentPath:[...M.currentPath,"anyOf","0"]});return N&&{anyOf:[N,{type:"null"}]};case u.ZodOptional:var O=a,P=c;if(P.currentPath.toString()===P.propertyPath?.toString())return c9(O.innerType._def,P);let Q=c9(O.innerType._def,{...P,currentPath:[...P.currentPath,"anyOf","1"]});return Q?{anyOf:[{not:cX(P)},Q]}:cX(P);case u.ZodMap:return f=a,"record"===(g=c).mapStrategy?c6(f,g):{type:"array",maxItems:125,items:{type:"array",items:[c9(f.keyType._def,{...g,currentPath:[...g.currentPath,"items","items","0"]})||cX(g),c9(f.valueType._def,{...g,currentPath:[...g.currentPath,"items","items","1"]})||cX(g)],minItems:2,maxItems:2}};case u.ZodSet:var R=a,S=c;let T={type:"array",uniqueItems:!0,items:c9(R.valueType._def,{...S,currentPath:[...S.currentPath,"items"]})};return R.minSize&&cZ(T,"minItems",R.minSize.value,R.minSize.message,S),R.maxSize&&cZ(T,"maxItems",R.maxSize.value,R.maxSize.message,S),T;case u.ZodLazy:return()=>a.getter()._def;case u.ZodPromise:return c9(a.type._def,c);case u.ZodNaN:case u.ZodNever:return"openAi"===(h=c).target?void 0:{not:cX({...h,currentPath:[...h.currentPath,"not"]})};case u.ZodEffects:return i=a,"input"===(j=c).effectStrategy?c9(i.schema._def,j):cX(j);case u.ZodAny:case u.ZodUnknown:return cX(c);case u.ZodDefault:return k=a,l=c,{...c9(k.innerType._def,l),default:k.defaultValue()};case u.ZodBranded:return c$(a,c);case u.ZodReadonly:case u.ZodCatch:return c9(a.innerType._def,c);case u.ZodPipeline:var U=a,V=c;if("input"===V.pipeStrategy)return c9(U.in._def,V);if("output"===V.pipeStrategy)return c9(U.out._def,V);let W=c9(U.in._def,{...V,currentPath:[...V.currentPath,"allOf","0"]}),X=c9(U.out._def,{...V,currentPath:[...V.currentPath,"allOf",W?"1":"0"]});return{allOf:[W,X].filter(a=>void 0!==a)};case u.ZodFunction:case u.ZodVoid:case u.ZodSymbol:default:return}})(a,a.typeName,b),g="function"==typeof f?c9(f(),b):f;if(g&&db(a,b,g),b.postProcess){let c=b.postProcess(g,a,b);return e.jsonSchema=g,c}return e.jsonSchema=g,g}let da=(a,b)=>{switch(b.$refStrategy){case"root":return{$ref:a.path.join("/")};case"relative":return{$ref:cW(b.currentPath,a.path)};case"none":case"seen":if(a.path.length<b.currentPath.length&&a.path.every((a,c)=>b.currentPath[c]===a))return console.warn(`Recursive reference detected at ${b.currentPath.join("/")}! Defaulting to any`),cX(b);return"seen"===b.$refStrategy?cX(b):void 0}},db=(a,b,c)=>(a.description&&(c.description=a.description,b.markdownDescription&&(c.markdownDescription=a.description)),c);function dc(...a){return a.reduce((a,b)=>({...a,...null!=b?b:{}}),{})}async function dd(a,b){if(null==a)return Promise.resolve();let c=null==b?void 0:b.abortSignal;return new Promise((b,d)=>{if(null==c?void 0:c.aborted)return void d(de());let e=setTimeout(()=>{f(),b()},a),f=()=>{clearTimeout(e),null==c||c.removeEventListener("abort",g)},g=()=>{f(),d(de())};null==c||c.addEventListener("abort",g)})}function de(){return new DOMException("Delay was aborted","AbortError")}function df(a){return Object.fromEntries([...a.headers])}var dg=({prefix:a,size:b=16,alphabet:c="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:d="-"}={})=>{let e=()=>{let a=c.length,d=Array(b);for(let e=0;e<b;e++)d[e]=c[Math.random()*a|0];return d.join("")};if(null==a)return e;if(c.includes(d))throw new az({argument:"separator",message:`The separator "${d}" must not be part of the alphabet "${c}".`});return()=>`${a}${d}${e()}`},dh=dg();function di(a){return null==a?"unknown error":"string"==typeof a?a:a instanceof Error?a.message:JSON.stringify(a)}function dj(a){return(a instanceof Error||a instanceof DOMException)&&("AbortError"===a.name||"ResponseAborted"===a.name||"TimeoutError"===a.name)}var dk=["fetch failed","failed to fetch"];function dl({error:a,url:b,requestBodyValues:c}){if(dj(a))return a;if(a instanceof TypeError&&dk.includes(a.message.toLowerCase())){let d=a.cause;if(null!=d)return new aq({message:`Cannot connect to API: ${d.message}`,cause:d,url:b,requestBodyValues:c,isRetryable:!0})}return a}function dm(a){return Object.fromEntries(Object.entries(a).filter(([a,b])=>null!=b))}var dn=()=>globalThis.fetch,dp=async({url:a,headers:b={},successfulResponseHandler:c,failedResponseHandler:d,abortSignal:e,fetch:f=dn()})=>{try{let g=await f(a,{method:"GET",headers:dm(b),signal:e}),h=df(g);if(!g.ok){let b;try{b=await d({response:g,url:a,requestBodyValues:{}})}catch(b){if(dj(b)||aq.isInstance(b))throw b;throw new aq({message:"Failed to process error response",cause:b,statusCode:g.status,url:a,responseHeaders:h,requestBodyValues:{}})}throw b.value}try{return await c({response:g,url:a,requestBodyValues:{}})}catch(b){if(b instanceof Error&&(dj(b)||aq.isInstance(b)))throw b;throw new aq({message:"Failed to process successful response",cause:b,statusCode:g.status,url:a,responseHeaders:h,requestBodyValues:{}})}}catch(b){throw dl({error:b,url:a,requestBodyValues:{}})}};function dq({settingValue:a,environmentVariableName:b}){return"string"==typeof a?a:null!=a||"undefined"==typeof process?void 0:null!=(a=process.env[b])&&"string"==typeof a?a:void 0}var dr=/"__proto__"\s*:/,ds=/"constructor"\s*:/;function dt(a){let{stackTraceLimit:b}=Error;Error.stackTraceLimit=0;try{let b=JSON.parse(a);return null===b||"object"!=typeof b||!1===dr.test(a)&&!1===ds.test(a)?b:function(a){let b=[a];for(;b.length;){let a=b;for(let c of(b=[],a)){if(Object.prototype.hasOwnProperty.call(c,"__proto__")||Object.prototype.hasOwnProperty.call(c,"constructor")&&Object.prototype.hasOwnProperty.call(c.constructor,"prototype"))throw SyntaxError("Object contains forbidden prototype property");for(let a in c){let d=c[a];d&&"object"==typeof d&&b.push(d)}}}return a}(b)}finally{Error.stackTraceLimit=b}}var du=Symbol.for("vercel.ai.validator");async function dv({value:a,schema:b}){let c=await dw({value:a,schema:b});if(!c.success)throw a_.wrap({value:a,cause:c.error});return c.value}async function dw({value:a,schema:b}){var c;let d="object"==typeof b&&null!==b&&du in b&&!0===b[du]&&"validate"in b?b:(c=b,{[du]:!0,validate:async a=>{let b=await c["~standard"].validate(a);return null==b.issues?{success:!0,value:b.value}:{success:!1,error:new a_({value:a,cause:b.issues})}}});try{if(null==d.validate)return{success:!0,value:a,rawValue:a};let b=await d.validate(a);if(b.success)return{success:!0,value:b.value,rawValue:a};return{success:!1,error:a_.wrap({value:a,cause:b.error}),rawValue:a}}catch(b){return{success:!1,error:a_.wrap({value:a,cause:b}),rawValue:a}}}async function dx({text:a,schema:b}){try{let c=dt(a);if(null==b)return c;return dv({value:c,schema:b})}catch(b){if(aL.isInstance(b)||a_.isInstance(b))throw b;throw new aL({text:a,cause:b})}}async function dy({text:a,schema:b}){try{let c=dt(a);if(null==b)return{success:!0,value:c,rawValue:c};return await dw({value:c,schema:b})}catch(b){return{success:!1,error:aL.isInstance(b)?b:new aL({text:a,cause:b}),rawValue:void 0}}}function dz(a){try{return dt(a),!0}catch(a){return!1}}async function dA({provider:a,providerOptions:b,schema:c}){if((null==b?void 0:b[a])==null)return;let d=await dw({value:b[a],schema:c});if(!d.success)throw new az({argument:"providerOptions",message:`invalid ${a} provider options`,cause:d.error});return d.value}var dB=()=>globalThis.fetch,dC=async({url:a,headers:b,body:c,failedResponseHandler:d,successfulResponseHandler:e,abortSignal:f,fetch:g})=>dE({url:a,headers:{"Content-Type":"application/json",...b},body:{content:JSON.stringify(c),values:c},failedResponseHandler:d,successfulResponseHandler:e,abortSignal:f,fetch:g}),dD=async({url:a,headers:b,formData:c,failedResponseHandler:d,successfulResponseHandler:e,abortSignal:f,fetch:g})=>dE({url:a,headers:b,body:{content:c,values:Object.fromEntries(c.entries())},failedResponseHandler:d,successfulResponseHandler:e,abortSignal:f,fetch:g}),dE=async({url:a,headers:b={},body:c,successfulResponseHandler:d,failedResponseHandler:e,abortSignal:f,fetch:g=dB()})=>{try{let h=await g(a,{method:"POST",headers:dm(b),body:c.content,signal:f}),i=df(h);if(!h.ok){let b;try{b=await e({response:h,url:a,requestBodyValues:c.values})}catch(b){if(dj(b)||aq.isInstance(b))throw b;throw new aq({message:"Failed to process error response",cause:b,statusCode:h.status,url:a,responseHeaders:i,requestBodyValues:c.values})}throw b.value}try{return await d({response:h,url:a,requestBodyValues:c.values})}catch(b){if(b instanceof Error&&(dj(b)||aq.isInstance(b)))throw b;throw new aq({message:"Failed to process successful response",cause:b,statusCode:h.status,url:a,responseHeaders:i,requestBodyValues:c.values})}}catch(b){throw dl({error:b,url:a,requestBodyValues:c.values})}};function dF({id:a,name:b,inputSchema:c}){return({execute:d,outputSchema:e,toModelOutput:f,onInputStart:g,onInputDelta:h,onInputAvailable:i,...j})=>({type:"provider-defined",id:a,name:b,args:j,inputSchema:c,outputSchema:e,execute:d,toModelOutput:f,onInputStart:g,onInputDelta:h,onInputAvailable:i})}async function dG(a){return"function"==typeof a&&(a=a()),Promise.resolve(a)}var dH=({errorSchema:a,errorToMessage:b,isRetryable:c})=>async({response:d,url:e,requestBodyValues:f})=>{let g=await d.text(),h=df(d);if(""===g.trim())return{responseHeaders:h,value:new aq({message:d.statusText,url:e,requestBodyValues:f,statusCode:d.status,responseHeaders:h,responseBody:g,isRetryable:null==c?void 0:c(d)})};try{let i=await dx({text:g,schema:a});return{responseHeaders:h,value:new aq({message:b(i),url:e,requestBodyValues:f,statusCode:d.status,responseHeaders:h,responseBody:g,data:i,isRetryable:null==c?void 0:c(d,i)})}}catch(a){return{responseHeaders:h,value:new aq({message:d.statusText,url:e,requestBodyValues:f,statusCode:d.status,responseHeaders:h,responseBody:g,isRetryable:null==c?void 0:c(d)})}}},dI=a=>async({response:b})=>{let c=df(b);if(null==b.body)throw new au({});return{responseHeaders:c,value:function({stream:a,schema:b}){return a.pipeThrough(new TextDecoderStream).pipeThrough(new a6).pipeThrough(new TransformStream({async transform({data:a},c){"[DONE]"!==a&&c.enqueue(await dy({text:a,schema:b}))}}))}({stream:b.body,schema:a})}},dJ=a=>async({response:b,url:c,requestBodyValues:d})=>{let e=await b.text(),f=await dy({text:e,schema:a}),g=df(b);if(!f.success)throw new aq({message:"Invalid JSON response",cause:f.error,statusCode:b.status,responseHeaders:g,responseBody:e,url:c,requestBodyValues:d});return{responseHeaders:g,value:f.value,rawValue:f.rawValue}},dK=Symbol.for("vercel.ai.schema");function dL(a,{validate:b}={}){return{[dK]:!0,_type:void 0,[du]:!0,jsonSchema:a,validate:b}}function dM(a){return null==a?dL({properties:{},additionalProperties:!1}):"object"==typeof a&&null!==a&&dK in a&&!0===a[dK]&&"jsonSchema"in a&&"validate"in a?a:function(a,b){var c;return"_zod"in a?dL(function(a,b){if(a instanceof a7){let c=new bv(b),d={};for(let b of a._idmap.entries()){let[a,d]=b;c.process(d)}let e={},f={registry:a,uri:b?.uri,defs:d};for(let d of a._idmap.entries()){let[a,g]=d;e[a]=c.emit(g,{...b,external:f})}return Object.keys(d).length>0&&(e.__shared={["draft-2020-12"===c.target?"$defs":"definitions"]:d}),{schemas:e}}let c=new bv(b);return c.process(a),c.emit(a,b)}(a,{target:"draft-7",io:"output",reused:"inline"}),{validate:async b=>{let c=await bM(a,b);return c.success?{success:!0,value:c.data}:{success:!1,error:c.error}}}):dL(((a,b)=>{let c=(a=>{let b,c="string"==typeof(b=a)?{...bO,name:b}:{...bO,...b},d=void 0!==c.name?[...c.basePath,c.definitionPath,c.name]:c.basePath;return{...c,flags:{hasReferencedOpenAiAnyType:!1},currentPath:d,propertyPath:void 0,seen:new Map(Object.entries(c.definitions).map(([a,b])=>[b._def,{def:b._def,path:[...c.basePath,c.definitionPath,a],jsonSchema:void 0}]))}})(b),d="object"==typeof b&&b.definitions?Object.entries(b.definitions).reduce((a,[b,d])=>({...a,[b]:c9(d._def,{...c,currentPath:[...c.basePath,c.definitionPath,b]},!0)??cX(c)}),{}):void 0,e="string"==typeof b?b:b?.nameStrategy==="title"?void 0:b?.name,f=c9(a._def,void 0===e?c:{...c,currentPath:[...c.basePath,c.definitionPath,e]},!1)??cX(c),g="object"==typeof b&&void 0!==b.name&&"title"===b.nameStrategy?b.name:void 0;void 0!==g&&(f.title=g),c.flags.hasReferencedOpenAiAnyType&&(d||(d={}),d[c.openAiAnyTypeName]||(d[c.openAiAnyTypeName]={type:["string","number","integer","boolean","array","null"],items:{$ref:"relative"===c.$refStrategy?"1":[...c.basePath,c.definitionPath,c.openAiAnyTypeName].join("/")}}));let h=void 0===e?d?{...f,[c.definitionPath]:d}:f:{$ref:[..."relative"===c.$refStrategy?[]:c.basePath,c.definitionPath,e].join("/"),[c.definitionPath]:{...d,[e]:f}};return"jsonSchema7"===c.target?h.$schema="http://json-schema.org/draft-07/schema#":("jsonSchema2019-09"===c.target||"openAi"===c.target)&&(h.$schema="https://json-schema.org/draft/2019-09/schema#"),"openAi"===c.target&&("anyOf"in h||"oneOf"in h||"allOf"in h||"type"in h&&Array.isArray(h.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),h})(a,{$refStrategy:(c=void 0,"none"),target:"jsonSchema7"}),{validate:async b=>{let c=await a.safeParseAsync(b);return c.success?{success:!0,value:c.data}:{success:!1,error:c.error}}})}(a)}var{btoa:dN,atob:dO}=globalThis;function dP(a){let b=dO(a.replace(/-/g,"+").replace(/_/g,"/"));return Uint8Array.from(b,a=>a.codePointAt(0))}function dQ(a){let b="";for(let c=0;c<a.length;c++)b+=String.fromCodePoint(a[c]);return dN(b)}function dR(a){return a instanceof Uint8Array?dQ(a):a}function dS(a){return null==a?void 0:a.replace(/\/$/,"")}async function*dT({execute:a,input:b,options:c}){let d=a(b,c);if(null!=d&&"function"==typeof d[Symbol.asyncIterator]){let a;for await(let b of d)a=b,yield{type:"preliminary",output:b};yield{type:"final",output:a}}else yield{type:"final",output:await d}}let dU=/^[cC][^\s-]{8,}$/,dV=/^[0-9a-z]+$/,dW=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,dX=/^[0-9a-vA-V]{20}$/,dY=/^[A-Za-z0-9]{27}$/,dZ=/^[a-zA-Z0-9_-]{21}$/,d$=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,d_=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,d0=a=>a?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${a}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,d1=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,d2=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,d3=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,d4=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,d5=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,d6=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,d7=/^[A-Za-z0-9_-]*$/,d8=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,d9=/^\+(?:[0-9]){6,14}[0-9]$/,ea="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",eb=RegExp(`^${ea}$`);function ec(a){let b="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof a.precision?-1===a.precision?`${b}`:0===a.precision?`${b}:[0-5]\\d`:`${b}:[0-5]\\d\\.\\d{${a.precision}}`:`${b}(?::[0-5]\\d(?:\\.\\d+)?)?`}let ed=/^\d+$/,ee=/^-?\d+(?:\.\d+)?/i,ef=/true|false/i,eg=/null/i,eh=/^[^A-Z]*$/,ei=/^[^a-z]*$/,ej=bw("$ZodCheck",(a,b)=>{var c;a._zod??(a._zod={}),a._zod.def=b,(c=a._zod).onattach??(c.onattach=[])}),ek={number:"number",bigint:"bigint",object:"date"},el=bw("$ZodCheckLessThan",(a,b)=>{ej.init(a,b);let c=ek[typeof b.value];a._zod.onattach.push(a=>{let c=a._zod.bag,d=(b.inclusive?c.maximum:c.exclusiveMaximum)??1/0;b.value<d&&(b.inclusive?c.maximum=b.value:c.exclusiveMaximum=b.value)}),a._zod.check=d=>{(b.inclusive?d.value<=b.value:d.value<b.value)||d.issues.push({origin:c,code:"too_big",maximum:b.value,input:d.value,inclusive:b.inclusive,inst:a,continue:!b.abort})}}),em=bw("$ZodCheckGreaterThan",(a,b)=>{ej.init(a,b);let c=ek[typeof b.value];a._zod.onattach.push(a=>{let c=a._zod.bag,d=(b.inclusive?c.minimum:c.exclusiveMinimum)??-1/0;b.value>d&&(b.inclusive?c.minimum=b.value:c.exclusiveMinimum=b.value)}),a._zod.check=d=>{(b.inclusive?d.value>=b.value:d.value>b.value)||d.issues.push({origin:c,code:"too_small",minimum:b.value,input:d.value,inclusive:b.inclusive,inst:a,continue:!b.abort})}}),en=bw("$ZodCheckMultipleOf",(a,b)=>{ej.init(a,b),a._zod.onattach.push(a=>{var c;(c=a._zod.bag).multipleOf??(c.multipleOf=b.value)}),a._zod.check=c=>{if(typeof c.value!=typeof b.value)throw Error("Cannot mix number and bigint in multiple_of check.");("bigint"==typeof c.value?c.value%b.value===BigInt(0):0===function(a,b){let c=(a.toString().split(".")[1]||"").length,d=(b.toString().split(".")[1]||"").length,e=c>d?c:d;return Number.parseInt(a.toFixed(e).replace(".",""))%Number.parseInt(b.toFixed(e).replace(".",""))/10**e}(c.value,b.value))||c.issues.push({origin:typeof c.value,code:"not_multiple_of",divisor:b.value,input:c.value,inst:a,continue:!b.abort})}}),eo=bw("$ZodCheckNumberFormat",(a,b)=>{ej.init(a,b),b.format=b.format||"float64";let c=b.format?.includes("int"),d=c?"int":"number",[e,f]=bo[b.format];a._zod.onattach.push(a=>{let d=a._zod.bag;d.format=b.format,d.minimum=e,d.maximum=f,c&&(d.pattern=ed)}),a._zod.check=g=>{let h=g.value;if(c){if(!Number.isInteger(h))return void g.issues.push({expected:d,format:b.format,code:"invalid_type",input:h,inst:a});if(!Number.isSafeInteger(h))return void(h>0?g.issues.push({input:h,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:a,origin:d,continue:!b.abort}):g.issues.push({input:h,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:a,origin:d,continue:!b.abort}))}h<e&&g.issues.push({origin:"number",input:h,code:"too_small",minimum:e,inclusive:!0,inst:a,continue:!b.abort}),h>f&&g.issues.push({origin:"number",input:h,code:"too_big",maximum:f,inst:a})}}),ep=bw("$ZodCheckMaxLength",(a,b)=>{var c;ej.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return null!=b&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.maximum??1/0;b.maximum<c&&(a._zod.bag.maximum=b.maximum)}),a._zod.check=c=>{let d=c.value;if(d.length<=b.maximum)return;let e=bt(d);c.issues.push({origin:e,code:"too_big",maximum:b.maximum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),eq=bw("$ZodCheckMinLength",(a,b)=>{var c;ej.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return null!=b&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.minimum??-1/0;b.minimum>c&&(a._zod.bag.minimum=b.minimum)}),a._zod.check=c=>{let d=c.value;if(d.length>=b.minimum)return;let e=bt(d);c.issues.push({origin:e,code:"too_small",minimum:b.minimum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),er=bw("$ZodCheckLengthEquals",(a,b)=>{var c;ej.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return null!=b&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag;c.minimum=b.length,c.maximum=b.length,c.length=b.length}),a._zod.check=c=>{let d=c.value,e=d.length;if(e===b.length)return;let f=bt(d),g=e>b.length;c.issues.push({origin:f,...g?{code:"too_big",maximum:b.length}:{code:"too_small",minimum:b.length},inclusive:!0,exact:!0,input:c.value,inst:a,continue:!b.abort})}}),es=bw("$ZodCheckStringFormat",(a,b)=>{var c,d;ej.init(a,b),a._zod.onattach.push(a=>{let c=a._zod.bag;c.format=b.format,b.pattern&&(c.patterns??(c.patterns=new Set),c.patterns.add(b.pattern))}),b.pattern?(c=a._zod).check??(c.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:b.format,input:c.value,...b.pattern?{pattern:b.pattern.toString()}:{},inst:a,continue:!b.abort})}):(d=a._zod).check??(d.check=()=>{})}),et=bw("$ZodCheckRegex",(a,b)=>{es.init(a,b),a._zod.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:"regex",input:c.value,pattern:b.pattern.toString(),inst:a,continue:!b.abort})}}),eu=bw("$ZodCheckLowerCase",(a,b)=>{b.pattern??(b.pattern=eh),es.init(a,b)}),ev=bw("$ZodCheckUpperCase",(a,b)=>{b.pattern??(b.pattern=ei),es.init(a,b)}),ew=bw("$ZodCheckIncludes",(a,b)=>{ej.init(a,b);let c=bl(b.includes),d=new RegExp("number"==typeof b.position?`^.{${b.position}}${c}`:c);b.pattern=d,a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(d)}),a._zod.check=c=>{c.value.includes(b.includes,b.position)||c.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:b.includes,input:c.value,inst:a,continue:!b.abort})}}),ex=bw("$ZodCheckStartsWith",(a,b)=>{ej.init(a,b);let c=RegExp(`^${bl(b.prefix)}.*`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.startsWith(b.prefix)||c.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:b.prefix,input:c.value,inst:a,continue:!b.abort})}}),ey=bw("$ZodCheckEndsWith",(a,b)=>{ej.init(a,b);let c=RegExp(`.*${bl(b.suffix)}$`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.endsWith(b.suffix)||c.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:b.suffix,input:c.value,inst:a,continue:!b.abort})}}),ez=bw("$ZodCheckOverwrite",(a,b)=>{ej.init(a,b),a._zod.check=a=>{a.value=b.tx(a.value)}});class eA{constructor(a=[]){this.content=[],this.indent=0,this&&(this.args=a)}indented(a){this.indent+=1,a(this),this.indent-=1}write(a){if("function"==typeof a){a(this,{execution:"sync"}),a(this,{execution:"async"});return}let b=a.split("\n").filter(a=>a),c=Math.min(...b.map(a=>a.length-a.trimStart().length));for(let a of b.map(a=>a.slice(c)).map(a=>" ".repeat(2*this.indent)+a))this.content.push(a)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(a=>`  ${a}`)].join("\n"))}}let eB={major:4,minor:0,patch:0},eC=bw("$ZodType",(a,b)=>{var c;a??(a={}),a._zod.def=b,a._zod.bag=a._zod.bag||{},a._zod.version=eB;let d=[...a._zod.def.checks??[]];for(let b of(a._zod.traits.has("$ZodCheck")&&d.unshift(a),d))for(let c of b._zod.onattach)c(a);if(0===d.length)(c=a._zod).deferred??(c.deferred=[]),a._zod.deferred?.push(()=>{a._zod.run=a._zod.parse});else{let b=(a,b,c)=>{let d,e=bp(a);for(let f of b){if(f._zod.def.when){if(!f._zod.def.when(a))continue}else if(e)continue;let b=a.issues.length,g=f._zod.check(a);if(g instanceof Promise&&c?.async===!1)throw new bx;if(d||g instanceof Promise)d=(d??Promise.resolve()).then(async()=>{await g,a.issues.length!==b&&(e||(e=bp(a,b)))});else{if(a.issues.length===b)continue;e||(e=bp(a,b))}}return d?d.then(()=>a):a};a._zod.run=(c,e)=>{let f=a._zod.parse(c,e);if(f instanceof Promise){if(!1===e.async)throw new bx;return f.then(a=>b(a,d,e))}return b(f,d,e)}}a["~standard"]={validate:b=>{try{let c=bE(a,b);return c.success?{value:c.data}:{issues:c.error?.issues}}catch(c){return bG(a,b).then(a=>a.success?{value:a.data}:{issues:a.error?.issues})}},vendor:"zod",version:1}}),eD=bw("$ZodString",(a,b)=>{eC.init(a,b),a._zod.pattern=[...a?._zod.bag?.patterns??[]].pop()??(a=>{let b=a?`[\\s\\S]{${a?.minimum??0},${a?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${b}$`)})(a._zod.bag),a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=String(c.value)}catch(a){}return"string"==typeof c.value||c.issues.push({expected:"string",code:"invalid_type",input:c.value,inst:a}),c}}),eE=bw("$ZodStringFormat",(a,b)=>{es.init(a,b),eD.init(a,b)}),eF=bw("$ZodGUID",(a,b)=>{b.pattern??(b.pattern=d_),eE.init(a,b)}),eG=bw("$ZodUUID",(a,b)=>{if(b.version){let a={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[b.version];if(void 0===a)throw Error(`Invalid UUID version: "${b.version}"`);b.pattern??(b.pattern=d0(a))}else b.pattern??(b.pattern=d0());eE.init(a,b)}),eH=bw("$ZodEmail",(a,b)=>{b.pattern??(b.pattern=d1),eE.init(a,b)}),eI=bw("$ZodURL",(a,b)=>{eE.init(a,b),a._zod.check=c=>{try{let d=c.value,e=new URL(d),f=e.href;b.hostname&&(b.hostname.lastIndex=0,b.hostname.test(e.hostname)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:d8.source,input:c.value,inst:a,continue:!b.abort})),b.protocol&&(b.protocol.lastIndex=0,b.protocol.test(e.protocol.endsWith(":")?e.protocol.slice(0,-1):e.protocol)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:b.protocol.source,input:c.value,inst:a,continue:!b.abort})),!d.endsWith("/")&&f.endsWith("/")?c.value=f.slice(0,-1):c.value=f;return}catch(d){c.issues.push({code:"invalid_format",format:"url",input:c.value,inst:a,continue:!b.abort})}}}),eJ=bw("$ZodEmoji",(a,b)=>{b.pattern??(b.pattern=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),eE.init(a,b)}),eK=bw("$ZodNanoID",(a,b)=>{b.pattern??(b.pattern=dZ),eE.init(a,b)}),eL=bw("$ZodCUID",(a,b)=>{b.pattern??(b.pattern=dU),eE.init(a,b)}),eM=bw("$ZodCUID2",(a,b)=>{b.pattern??(b.pattern=dV),eE.init(a,b)}),eN=bw("$ZodULID",(a,b)=>{b.pattern??(b.pattern=dW),eE.init(a,b)}),eO=bw("$ZodXID",(a,b)=>{b.pattern??(b.pattern=dX),eE.init(a,b)}),eP=bw("$ZodKSUID",(a,b)=>{b.pattern??(b.pattern=dY),eE.init(a,b)}),eQ=bw("$ZodISODateTime",(a,b)=>{b.pattern??(b.pattern=function(a){let b=ec({precision:a.precision}),c=["Z"];a.local&&c.push(""),a.offset&&c.push("([+-]\\d{2}:\\d{2})");let d=`${b}(?:${c.join("|")})`;return RegExp(`^${ea}T(?:${d})$`)}(b)),eE.init(a,b)}),eR=bw("$ZodISODate",(a,b)=>{b.pattern??(b.pattern=eb),eE.init(a,b)}),eS=bw("$ZodISOTime",(a,b)=>{b.pattern??(b.pattern=RegExp(`^${ec(b)}$`)),eE.init(a,b)}),eT=bw("$ZodISODuration",(a,b)=>{b.pattern??(b.pattern=d$),eE.init(a,b)}),eU=bw("$ZodIPv4",(a,b)=>{b.pattern??(b.pattern=d2),eE.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv4"})}),eV=bw("$ZodIPv6",(a,b)=>{b.pattern??(b.pattern=d3),eE.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv6"}),a._zod.check=c=>{try{new URL(`http://[${c.value}]`)}catch{c.issues.push({code:"invalid_format",format:"ipv6",input:c.value,inst:a,continue:!b.abort})}}}),eW=bw("$ZodCIDRv4",(a,b)=>{b.pattern??(b.pattern=d4),eE.init(a,b)}),eX=bw("$ZodCIDRv6",(a,b)=>{b.pattern??(b.pattern=d5),eE.init(a,b),a._zod.check=c=>{let[d,e]=c.value.split("/");try{if(!e)throw Error();let a=Number(e);if(`${a}`!==e||a<0||a>128)throw Error();new URL(`http://[${d}]`)}catch{c.issues.push({code:"invalid_format",format:"cidrv6",input:c.value,inst:a,continue:!b.abort})}}});function eY(a){if(""===a)return!0;if(a.length%4!=0)return!1;try{return atob(a),!0}catch{return!1}}let eZ=bw("$ZodBase64",(a,b)=>{b.pattern??(b.pattern=d6),eE.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64"}),a._zod.check=c=>{eY(c.value)||c.issues.push({code:"invalid_format",format:"base64",input:c.value,inst:a,continue:!b.abort})}}),e$=bw("$ZodBase64URL",(a,b)=>{b.pattern??(b.pattern=d7),eE.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64url"}),a._zod.check=c=>{!function(a){if(!d7.test(a))return!1;let b=a.replace(/[-_]/g,a=>"-"===a?"+":"/");return eY(b.padEnd(4*Math.ceil(b.length/4),"="))}(c.value)&&c.issues.push({code:"invalid_format",format:"base64url",input:c.value,inst:a,continue:!b.abort})}}),e_=bw("$ZodE164",(a,b)=>{b.pattern??(b.pattern=d9),eE.init(a,b)}),e0=bw("$ZodJWT",(a,b)=>{eE.init(a,b),a._zod.check=c=>{!function(a,b=null){try{let c=a.split(".");if(3!==c.length)return!1;let[d]=c;if(!d)return!1;let e=JSON.parse(atob(d));if("typ"in e&&e?.typ!=="JWT"||!e.alg||b&&(!("alg"in e)||e.alg!==b))return!1;return!0}catch{return!1}}(c.value,b.alg)&&c.issues.push({code:"invalid_format",format:"jwt",input:c.value,inst:a,continue:!b.abort})}}),e1=bw("$ZodNumber",(a,b)=>{eC.init(a,b),a._zod.pattern=a._zod.bag.pattern??ee,a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=Number(c.value)}catch(a){}let e=c.value;if("number"==typeof e&&!Number.isNaN(e)&&Number.isFinite(e))return c;let f="number"==typeof e?Number.isNaN(e)?"NaN":Number.isFinite(e)?void 0:"Infinity":void 0;return c.issues.push({expected:"number",code:"invalid_type",input:e,inst:a,...f?{received:f}:{}}),c}}),e2=bw("$ZodNumber",(a,b)=>{eo.init(a,b),e1.init(a,b)}),e3=bw("$ZodBoolean",(a,b)=>{eC.init(a,b),a._zod.pattern=ef,a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=!!c.value}catch(a){}let e=c.value;return"boolean"==typeof e||c.issues.push({expected:"boolean",code:"invalid_type",input:e,inst:a}),c}}),e4=bw("$ZodNull",(a,b)=>{eC.init(a,b),a._zod.pattern=eg,a._zod.values=new Set([null]),a._zod.parse=(b,c)=>{let d=b.value;return null===d||b.issues.push({expected:"null",code:"invalid_type",input:d,inst:a}),b}}),e5=bw("$ZodAny",(a,b)=>{eC.init(a,b),a._zod.parse=a=>a}),e6=bw("$ZodUnknown",(a,b)=>{eC.init(a,b),a._zod.parse=a=>a}),e7=bw("$ZodNever",(a,b)=>{eC.init(a,b),a._zod.parse=(b,c)=>(b.issues.push({expected:"never",code:"invalid_type",input:b.value,inst:a}),b)});function e8(a,b,c){a.issues.length&&b.issues.push(...bq(c,a.issues)),b.value[c]=a.value}let e9=bw("$ZodArray",(a,b)=>{eC.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!Array.isArray(e))return c.issues.push({expected:"array",code:"invalid_type",input:e,inst:a}),c;c.value=Array(e.length);let f=[];for(let a=0;a<e.length;a++){let g=e[a],h=b.element._zod.run({value:g,issues:[]},d);h instanceof Promise?f.push(h.then(b=>e8(b,c,a))):e8(h,c,a)}return f.length?Promise.all(f).then(()=>c):c}});function fa(a,b,c){a.issues.length&&b.issues.push(...bq(c,a.issues)),b.value[c]=a.value}function fb(a,b,c,d){a.issues.length?void 0===d[c]?c in d?b.value[c]=void 0:b.value[c]=a.value:b.issues.push(...bq(c,a.issues)):void 0===a.value?c in d&&(b.value[c]=void 0):b.value[c]=a.value}let fc=bw("$ZodObject",(a,b)=>{let c,d;eC.init(a,b);let e=bb(()=>{let a=Object.keys(b.shape);for(let c of a)if(!(b.shape[c]instanceof eC))throw Error(`Invalid element at key "${c}": expected a Zod schema`);let c=function(a){return Object.keys(a).filter(b=>"optional"===a[b]._zod.optin&&"optional"===a[b]._zod.optout)}(b.shape);return{shape:b.shape,keys:a,keySet:new Set(a),numKeys:a.length,optionalKeys:new Set(c)}});bd(a._zod,"propValues",()=>{let a=b.shape,c={};for(let b in a){let d=a[b]._zod;if(d.values)for(let a of(c[b]??(c[b]=new Set),d.values))c[b].add(a)}return c});let f=!by.jitless,g=f&&bi.value,h=b.catchall;a._zod.parse=(i,j)=>{d??(d=e.value);let k=i.value;if(!bh(k))return i.issues.push({expected:"object",code:"invalid_type",input:k,inst:a}),i;let l=[];if(f&&g&&j?.async===!1&&!0!==j.jitless)c||(c=(a=>{let b=new eA(["shape","payload","ctx"]),c=e.value,d=a=>{let b=bf(a);return`shape[${b}]._zod.run({ value: input[${b}], issues: [] }, ctx)`};b.write("const input = payload.value;");let f=Object.create(null),g=0;for(let a of c.keys)f[a]=`key_${g++}`;for(let a of(b.write("const newResult = {}"),c.keys))if(c.optionalKeys.has(a)){let c=f[a];b.write(`const ${c} = ${d(a)};`);let e=bf(a);b.write(`
        if (${c}.issues.length) {
          if (input[${e}] === undefined) {
            if (${e} in input) {
              newResult[${e}] = undefined;
            }
          } else {
            payload.issues = payload.issues.concat(
              ${c}.issues.map((iss) => ({
                ...iss,
                path: iss.path ? [${e}, ...iss.path] : [${e}],
              }))
            );
          }
        } else if (${c}.value === undefined) {
          if (${e} in input) newResult[${e}] = undefined;
        } else {
          newResult[${e}] = ${c}.value;
        }
        `)}else{let c=f[a];b.write(`const ${c} = ${d(a)};`),b.write(`
          if (${c}.issues.length) payload.issues = payload.issues.concat(${c}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${bf(a)}, ...iss.path] : [${bf(a)}]
          })));`),b.write(`newResult[${bf(a)}] = ${c}.value`)}b.write("payload.value = newResult;"),b.write("return payload;");let h=b.compile();return(b,c)=>h(a,b,c)})(b.shape)),i=c(i,j);else{i.value={};let a=d.shape;for(let b of d.keys){let c=a[b],d=c._zod.run({value:k[b],issues:[]},j),e="optional"===c._zod.optin&&"optional"===c._zod.optout;d instanceof Promise?l.push(d.then(a=>e?fb(a,i,b,k):fa(a,i,b))):e?fb(d,i,b,k):fa(d,i,b)}}if(!h)return l.length?Promise.all(l).then(()=>i):i;let m=[],n=d.keySet,o=h._zod,p=o.def.type;for(let a of Object.keys(k)){if(n.has(a))continue;if("never"===p){m.push(a);continue}let b=o.run({value:k[a],issues:[]},j);b instanceof Promise?l.push(b.then(b=>fa(b,i,a))):fa(b,i,a)}return(m.length&&i.issues.push({code:"unrecognized_keys",keys:m,input:k,inst:a}),l.length)?Promise.all(l).then(()=>i):i}});function fd(a,b,c,d){for(let c of a)if(0===c.issues.length)return b.value=c.value,b;return b.issues.push({code:"invalid_union",input:b.value,inst:c,errors:a.map(a=>a.issues.map(a=>bs(a,d,bz())))}),b}let fe=bw("$ZodUnion",(a,b)=>{eC.init(a,b),bd(a._zod,"optin",()=>b.options.some(a=>"optional"===a._zod.optin)?"optional":void 0),bd(a._zod,"optout",()=>b.options.some(a=>"optional"===a._zod.optout)?"optional":void 0),bd(a._zod,"values",()=>{if(b.options.every(a=>a._zod.values))return new Set(b.options.flatMap(a=>Array.from(a._zod.values)))}),bd(a._zod,"pattern",()=>{if(b.options.every(a=>a._zod.pattern)){let a=b.options.map(a=>a._zod.pattern);return RegExp(`^(${a.map(a=>bc(a.source)).join("|")})$`)}}),a._zod.parse=(c,d)=>{let e=!1,f=[];for(let a of b.options){let b=a._zod.run({value:c.value,issues:[]},d);if(b instanceof Promise)f.push(b),e=!0;else{if(0===b.issues.length)return b;f.push(b)}}return e?Promise.all(f).then(b=>fd(b,c,a,d)):fd(f,c,a,d)}}),ff=bw("$ZodDiscriminatedUnion",(a,b)=>{fe.init(a,b);let c=a._zod.parse;bd(a._zod,"propValues",()=>{let a={};for(let c of b.options){let d=c._zod.propValues;if(!d||0===Object.keys(d).length)throw Error(`Invalid discriminated union option at index "${b.options.indexOf(c)}"`);for(let[b,c]of Object.entries(d))for(let d of(a[b]||(a[b]=new Set),c))a[b].add(d)}return a});let d=bb(()=>{let a=b.options,c=new Map;for(let d of a){let a=d._zod.propValues[b.discriminator];if(!a||0===a.size)throw Error(`Invalid discriminated union option at index "${b.options.indexOf(d)}"`);for(let b of a){if(c.has(b))throw Error(`Duplicate discriminator value "${String(b)}"`);c.set(b,d)}}return c});a._zod.parse=(e,f)=>{let g=e.value;if(!bh(g))return e.issues.push({code:"invalid_type",expected:"object",input:g,inst:a}),e;let h=d.value.get(g?.[b.discriminator]);return h?h._zod.run(e,f):b.unionFallback?c(e,f):(e.issues.push({code:"invalid_union",errors:[],note:"No matching discriminator",input:g,path:[b.discriminator],inst:a}),e)}}),fg=bw("$ZodIntersection",(a,b)=>{eC.init(a,b),a._zod.parse=(a,c)=>{let d=a.value,e=b.left._zod.run({value:d,issues:[]},c),f=b.right._zod.run({value:d,issues:[]},c);return e instanceof Promise||f instanceof Promise?Promise.all([e,f]).then(([b,c])=>fh(a,b,c)):fh(a,e,f)}});function fh(a,b,c){if(b.issues.length&&a.issues.push(...b.issues),c.issues.length&&a.issues.push(...c.issues),bp(a))return a;let d=function a(b,c){if(b===c||b instanceof Date&&c instanceof Date&&+b==+c)return{valid:!0,data:b};if(bj(b)&&bj(c)){let d=Object.keys(c),e=Object.keys(b).filter(a=>-1!==d.indexOf(a)),f={...b,...c};for(let d of e){let e=a(b[d],c[d]);if(!e.valid)return{valid:!1,mergeErrorPath:[d,...e.mergeErrorPath]};f[d]=e.data}return{valid:!0,data:f}}if(Array.isArray(b)&&Array.isArray(c)){if(b.length!==c.length)return{valid:!1,mergeErrorPath:[]};let d=[];for(let e=0;e<b.length;e++){let f=a(b[e],c[e]);if(!f.valid)return{valid:!1,mergeErrorPath:[e,...f.mergeErrorPath]};d.push(f.data)}return{valid:!0,data:d}}return{valid:!1,mergeErrorPath:[]}}(b.value,c.value);if(!d.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(d.mergeErrorPath)}`);return a.value=d.data,a}let fi=bw("$ZodRecord",(a,b)=>{eC.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!bj(e))return c.issues.push({expected:"record",code:"invalid_type",input:e,inst:a}),c;let f=[];if(b.keyType._zod.values){let g,h=b.keyType._zod.values;for(let a of(c.value={},h))if("string"==typeof a||"number"==typeof a||"symbol"==typeof a){let g=b.valueType._zod.run({value:e[a],issues:[]},d);g instanceof Promise?f.push(g.then(b=>{b.issues.length&&c.issues.push(...bq(a,b.issues)),c.value[a]=b.value})):(g.issues.length&&c.issues.push(...bq(a,g.issues)),c.value[a]=g.value)}for(let a in e)h.has(a)||(g=g??[]).push(a);g&&g.length>0&&c.issues.push({code:"unrecognized_keys",input:e,inst:a,keys:g})}else for(let g of(c.value={},Reflect.ownKeys(e))){if("__proto__"===g)continue;let h=b.keyType._zod.run({value:g,issues:[]},d);if(h instanceof Promise)throw Error("Async schemas not supported in object keys currently");if(h.issues.length){c.issues.push({origin:"record",code:"invalid_key",issues:h.issues.map(a=>bs(a,d,bz())),input:g,path:[g],inst:a}),c.value[h.value]=h.value;continue}let i=b.valueType._zod.run({value:e[g],issues:[]},d);i instanceof Promise?f.push(i.then(a=>{a.issues.length&&c.issues.push(...bq(g,a.issues)),c.value[h.value]=a.value})):(i.issues.length&&c.issues.push(...bq(g,i.issues)),c.value[h.value]=i.value)}return f.length?Promise.all(f).then(()=>c):c}}),fj=bw("$ZodEnum",(a,b)=>{eC.init(a,b);let c=a9(b.entries);a._zod.values=new Set(c),a._zod.pattern=RegExp(`^(${c.filter(a=>bk.has(typeof a)).map(a=>"string"==typeof a?bl(a):a.toString()).join("|")})$`),a._zod.parse=(b,d)=>{let e=b.value;return a._zod.values.has(e)||b.issues.push({code:"invalid_value",values:c,input:e,inst:a}),b}}),fk=bw("$ZodLiteral",(a,b)=>{eC.init(a,b),a._zod.values=new Set(b.values),a._zod.pattern=RegExp(`^(${b.values.map(a=>"string"==typeof a?bl(a):a?a.toString():String(a)).join("|")})$`),a._zod.parse=(c,d)=>{let e=c.value;return a._zod.values.has(e)||c.issues.push({code:"invalid_value",values:b.values,input:e,inst:a}),c}}),fl=bw("$ZodTransform",(a,b)=>{eC.init(a,b),a._zod.parse=(a,c)=>{let d=b.transform(a.value,a);if(c.async)return(d instanceof Promise?d:Promise.resolve(d)).then(b=>(a.value=b,a));if(d instanceof Promise)throw new bx;return a.value=d,a}}),fm=bw("$ZodOptional",(a,b)=>{eC.init(a,b),a._zod.optin="optional",a._zod.optout="optional",bd(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,void 0]):void 0),bd(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${bc(a.source)})?$`):void 0}),a._zod.parse=(a,c)=>"optional"===b.innerType._zod.optin?b.innerType._zod.run(a,c):void 0===a.value?a:b.innerType._zod.run(a,c)}),fn=bw("$ZodNullable",(a,b)=>{eC.init(a,b),bd(a._zod,"optin",()=>b.innerType._zod.optin),bd(a._zod,"optout",()=>b.innerType._zod.optout),bd(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${bc(a.source)}|null)$`):void 0}),bd(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,null]):void 0),a._zod.parse=(a,c)=>null===a.value?a:b.innerType._zod.run(a,c)}),fo=bw("$ZodDefault",(a,b)=>{eC.init(a,b),a._zod.optin="optional",bd(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{if(void 0===a.value)return a.value=b.defaultValue,a;let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(a=>fp(a,b)):fp(d,b)}});function fp(a,b){return void 0===a.value&&(a.value=b.defaultValue),a}let fq=bw("$ZodPrefault",(a,b)=>{eC.init(a,b),a._zod.optin="optional",bd(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>(void 0===a.value&&(a.value=b.defaultValue),b.innerType._zod.run(a,c))}),fr=bw("$ZodNonOptional",(a,b)=>{eC.init(a,b),bd(a._zod,"values",()=>{let a=b.innerType._zod.values;return a?new Set([...a].filter(a=>void 0!==a)):void 0}),a._zod.parse=(c,d)=>{let e=b.innerType._zod.run(c,d);return e instanceof Promise?e.then(b=>fs(b,a)):fs(e,a)}});function fs(a,b){return a.issues.length||void 0!==a.value||a.issues.push({code:"invalid_type",expected:"nonoptional",input:a.value,inst:b}),a}let ft=bw("$ZodCatch",(a,b)=>{eC.init(a,b),a._zod.optin="optional",bd(a._zod,"optout",()=>b.innerType._zod.optout),bd(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(d=>(a.value=d.value,d.issues.length&&(a.value=b.catchValue({...a,error:{issues:d.issues.map(a=>bs(a,c,bz()))},input:a.value}),a.issues=[]),a)):(a.value=d.value,d.issues.length&&(a.value=b.catchValue({...a,error:{issues:d.issues.map(a=>bs(a,c,bz()))},input:a.value}),a.issues=[]),a)}}),fu=bw("$ZodPipe",(a,b)=>{eC.init(a,b),bd(a._zod,"values",()=>b.in._zod.values),bd(a._zod,"optin",()=>b.in._zod.optin),bd(a._zod,"optout",()=>b.out._zod.optout),a._zod.parse=(a,c)=>{let d=b.in._zod.run(a,c);return d instanceof Promise?d.then(a=>fv(a,b,c)):fv(d,b,c)}});function fv(a,b,c){return bp(a)?a:b.out._zod.run({value:a.value,issues:a.issues},c)}let fw=bw("$ZodReadonly",(a,b)=>{eC.init(a,b),bd(a._zod,"propValues",()=>b.innerType._zod.propValues),bd(a._zod,"values",()=>b.innerType._zod.values),bd(a._zod,"optin",()=>b.innerType._zod.optin),bd(a._zod,"optout",()=>b.innerType._zod.optout),a._zod.parse=(a,c)=>{let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(fx):fx(d)}});function fx(a){return a.value=Object.freeze(a.value),a}let fy=bw("$ZodLazy",(a,b)=>{eC.init(a,b),bd(a._zod,"innerType",()=>b.getter()),bd(a._zod,"pattern",()=>a._zod.innerType._zod.pattern),bd(a._zod,"propValues",()=>a._zod.innerType._zod.propValues),bd(a._zod,"optin",()=>a._zod.innerType._zod.optin),bd(a._zod,"optout",()=>a._zod.innerType._zod.optout),a._zod.parse=(b,c)=>a._zod.innerType._zod.run(b,c)}),fz=bw("$ZodCustom",(a,b)=>{ej.init(a,b),eC.init(a,b),a._zod.parse=(a,b)=>a,a._zod.check=c=>{let d=c.value,e=b.fn(d);if(e instanceof Promise)return e.then(b=>fA(b,c,d,a));fA(e,c,d,a)}});function fA(a,b,c,d){if(!a){let a={code:"custom",input:c,inst:d,path:[...d._zod.def.path??[]],continue:!d._zod.def.abort};d._zod.def.params&&(a.params=d._zod.def.params),b.issues.push(bu(a))}}function fB(a,b){return new a({type:"string",format:"guid",check:"string_format",abort:!1,...bn(b)})}function fC(a,b){return new a({type:"string",format:"base64",check:"string_format",abort:!1,...bn(b)})}function fD(a,b){return new el({check:"less_than",...bn(b),value:a,inclusive:!1})}function fE(a,b){return new el({check:"less_than",...bn(b),value:a,inclusive:!0})}function fF(a,b){return new em({check:"greater_than",...bn(b),value:a,inclusive:!1})}function fG(a,b){return new em({check:"greater_than",...bn(b),value:a,inclusive:!0})}function fH(a,b){return new en({check:"multiple_of",...bn(b),value:a})}function fI(a,b){return new ep({check:"max_length",...bn(b),maximum:a})}function fJ(a,b){return new eq({check:"min_length",...bn(b),minimum:a})}function fK(a,b){return new er({check:"length_equals",...bn(b),length:a})}function fL(a){return new ez({check:"overwrite",tx:a})}let fM=bw("ZodISODateTime",(a,b)=>{eQ.init(a,b),fU.init(a,b)}),fN=bw("ZodISODate",(a,b)=>{eR.init(a,b),fU.init(a,b)}),fO=bw("ZodISOTime",(a,b)=>{eS.init(a,b),fU.init(a,b)}),fP=bw("ZodISODuration",(a,b)=>{eT.init(a,b),fU.init(a,b)}),fQ=bw("ZodType",(a,b)=>(eC.init(a,b),a.def=b,Object.defineProperty(a,"_def",{value:b}),a.check=(...c)=>a.clone({...b,checks:[...b.checks??[],...c.map(a=>"function"==typeof a?{_zod:{check:a,def:{check:"custom"},onattach:[]}}:a)]}),a.clone=(b,c)=>bm(a,b,c),a.brand=()=>a,a.register=(b,c)=>(b.add(a,c),a),a.parse=(b,c)=>bJ(a,b,c,{callee:a.parse}),a.safeParse=(b,c)=>bL(a,b,c),a.parseAsync=async(b,c)=>bK(a,b,c,{callee:a.parseAsync}),a.safeParseAsync=async(b,c)=>bM(a,b,c),a.spa=a.safeParseAsync,a.refine=(b,c)=>a.check(function(a,b={}){return new gU({type:"custom",check:"custom",fn:a,...bn(b)})}(b,c)),a.superRefine=b=>a.check(function(a){let b=function(a){let b=new ej({check:"custom"});return b._zod.check=a,b}(c=>(c.addIssue=a=>{"string"==typeof a?c.issues.push(bu(a,c.value,b._zod.def)):(a.fatal&&(a.continue=!1),a.code??(a.code="custom"),a.input??(a.input=c.value),a.inst??(a.inst=b),a.continue??(a.continue=!b._zod.def.abort),c.issues.push(bu(a)))},a(c.value,c)));return b}(b)),a.overwrite=b=>a.check(fL(b)),a.optional=()=>gI(a),a.nullable=()=>gK(a),a.nullish=()=>gI(gK(a)),a.nonoptional=b=>new gN({type:"nonoptional",innerType:a,...bn(b)}),a.array=()=>gq(a),a.or=b=>gw([a,b]),a.and=b=>new gz({type:"intersection",left:a,right:b}),a.transform=b=>gQ(a,new gG({type:"transform",transform:b})),a.default=b=>(function(a,b){return new gL({type:"default",innerType:a,get defaultValue(){return"function"==typeof b?b():b}})})(a,b),a.prefault=b=>(function(a,b){return new gM({type:"prefault",innerType:a,get defaultValue(){return"function"==typeof b?b():b}})})(a,b),a.catch=b=>(function(a,b){return new gO({type:"catch",innerType:a,catchValue:"function"==typeof b?b:()=>b})})(a,b),a.pipe=b=>gQ(a,b),a.readonly=()=>new gR({type:"readonly",innerType:a}),a.describe=b=>{let c=a.clone();return a8.add(c,{description:b}),c},Object.defineProperty(a,"description",{get:()=>a8.get(a)?.description,configurable:!0}),a.meta=(...b)=>{if(0===b.length)return a8.get(a);let c=a.clone();return a8.add(c,b[0]),c},a.isOptional=()=>a.safeParse(void 0).success,a.isNullable=()=>a.safeParse(null).success,a)),fR=bw("_ZodString",(a,b)=>{eD.init(a,b),fQ.init(a,b);let c=a._zod.bag;a.format=c.format??null,a.minLength=c.minimum??null,a.maxLength=c.maximum??null,a.regex=(...b)=>a.check(function(a,b){return new et({check:"string_format",format:"regex",...bn(b),pattern:a})}(...b)),a.includes=(...b)=>a.check(function(a,b){return new ew({check:"string_format",format:"includes",...bn(b),includes:a})}(...b)),a.startsWith=(...b)=>a.check(function(a,b){return new ex({check:"string_format",format:"starts_with",...bn(b),prefix:a})}(...b)),a.endsWith=(...b)=>a.check(function(a,b){return new ey({check:"string_format",format:"ends_with",...bn(b),suffix:a})}(...b)),a.min=(...b)=>a.check(fJ(...b)),a.max=(...b)=>a.check(fI(...b)),a.length=(...b)=>a.check(fK(...b)),a.nonempty=(...b)=>a.check(fJ(1,...b)),a.lowercase=b=>a.check(new eu({check:"string_format",format:"lowercase",...bn(b)})),a.uppercase=b=>a.check(new ev({check:"string_format",format:"uppercase",...bn(b)})),a.trim=()=>a.check(fL(a=>a.trim())),a.normalize=(...b)=>a.check(function(a){return fL(b=>b.normalize(a))}(...b)),a.toLowerCase=()=>a.check(fL(a=>a.toLowerCase())),a.toUpperCase=()=>a.check(fL(a=>a.toUpperCase()))}),fS=bw("ZodString",(a,b)=>{eD.init(a,b),fR.init(a,b),a.email=b=>a.check(new fV({type:"string",format:"email",check:"string_format",abort:!1,...bn(b)})),a.url=b=>a.check(new fY({type:"string",format:"url",check:"string_format",abort:!1,...bn(b)})),a.jwt=b=>a.check(new gb({type:"string",format:"jwt",check:"string_format",abort:!1,...bn(b)})),a.emoji=b=>a.check(new fZ({type:"string",format:"emoji",check:"string_format",abort:!1,...bn(b)})),a.guid=b=>a.check(fB(fW,b)),a.uuid=b=>a.check(new fX({type:"string",format:"uuid",check:"string_format",abort:!1,...bn(b)})),a.uuidv4=b=>a.check(new fX({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...bn(b)})),a.uuidv6=b=>a.check(new fX({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...bn(b)})),a.uuidv7=b=>a.check(new fX({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...bn(b)})),a.nanoid=b=>a.check(new f$({type:"string",format:"nanoid",check:"string_format",abort:!1,...bn(b)})),a.guid=b=>a.check(fB(fW,b)),a.cuid=b=>a.check(new f_({type:"string",format:"cuid",check:"string_format",abort:!1,...bn(b)})),a.cuid2=b=>a.check(new f0({type:"string",format:"cuid2",check:"string_format",abort:!1,...bn(b)})),a.ulid=b=>a.check(new f1({type:"string",format:"ulid",check:"string_format",abort:!1,...bn(b)})),a.base64=b=>a.check(fC(f8,b)),a.base64url=b=>a.check(new f9({type:"string",format:"base64url",check:"string_format",abort:!1,...bn(b)})),a.xid=b=>a.check(new f2({type:"string",format:"xid",check:"string_format",abort:!1,...bn(b)})),a.ksuid=b=>a.check(new f3({type:"string",format:"ksuid",check:"string_format",abort:!1,...bn(b)})),a.ipv4=b=>a.check(new f4({type:"string",format:"ipv4",check:"string_format",abort:!1,...bn(b)})),a.ipv6=b=>a.check(new f5({type:"string",format:"ipv6",check:"string_format",abort:!1,...bn(b)})),a.cidrv4=b=>a.check(new f6({type:"string",format:"cidrv4",check:"string_format",abort:!1,...bn(b)})),a.cidrv6=b=>a.check(new f7({type:"string",format:"cidrv6",check:"string_format",abort:!1,...bn(b)})),a.e164=b=>a.check(new ga({type:"string",format:"e164",check:"string_format",abort:!1,...bn(b)})),a.datetime=b=>a.check(new fM({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...bn(b)})),a.date=b=>a.check(new fN({type:"string",format:"date",check:"string_format",...bn(b)})),a.time=b=>a.check(new fO({type:"string",format:"time",check:"string_format",precision:null,...bn(b)})),a.duration=b=>a.check(new fP({type:"string",format:"duration",check:"string_format",...bn(b)}))});function fT(a){return new fS({type:"string",...bn(a)})}let fU=bw("ZodStringFormat",(a,b)=>{eE.init(a,b),fR.init(a,b)}),fV=bw("ZodEmail",(a,b)=>{eH.init(a,b),fU.init(a,b)}),fW=bw("ZodGUID",(a,b)=>{eF.init(a,b),fU.init(a,b)}),fX=bw("ZodUUID",(a,b)=>{eG.init(a,b),fU.init(a,b)}),fY=bw("ZodURL",(a,b)=>{eI.init(a,b),fU.init(a,b)}),fZ=bw("ZodEmoji",(a,b)=>{eJ.init(a,b),fU.init(a,b)}),f$=bw("ZodNanoID",(a,b)=>{eK.init(a,b),fU.init(a,b)}),f_=bw("ZodCUID",(a,b)=>{eL.init(a,b),fU.init(a,b)}),f0=bw("ZodCUID2",(a,b)=>{eM.init(a,b),fU.init(a,b)}),f1=bw("ZodULID",(a,b)=>{eN.init(a,b),fU.init(a,b)}),f2=bw("ZodXID",(a,b)=>{eO.init(a,b),fU.init(a,b)}),f3=bw("ZodKSUID",(a,b)=>{eP.init(a,b),fU.init(a,b)}),f4=bw("ZodIPv4",(a,b)=>{eU.init(a,b),fU.init(a,b)}),f5=bw("ZodIPv6",(a,b)=>{eV.init(a,b),fU.init(a,b)}),f6=bw("ZodCIDRv4",(a,b)=>{eW.init(a,b),fU.init(a,b)}),f7=bw("ZodCIDRv6",(a,b)=>{eX.init(a,b),fU.init(a,b)}),f8=bw("ZodBase64",(a,b)=>{eZ.init(a,b),fU.init(a,b)}),f9=bw("ZodBase64URL",(a,b)=>{e$.init(a,b),fU.init(a,b)}),ga=bw("ZodE164",(a,b)=>{e_.init(a,b),fU.init(a,b)}),gb=bw("ZodJWT",(a,b)=>{e0.init(a,b),fU.init(a,b)}),gc=bw("ZodNumber",(a,b)=>{e1.init(a,b),fQ.init(a,b),a.gt=(b,c)=>a.check(fF(b,c)),a.gte=(b,c)=>a.check(fG(b,c)),a.min=(b,c)=>a.check(fG(b,c)),a.lt=(b,c)=>a.check(fD(b,c)),a.lte=(b,c)=>a.check(fE(b,c)),a.max=(b,c)=>a.check(fE(b,c)),a.int=b=>a.check(gf(b)),a.safe=b=>a.check(gf(b)),a.positive=b=>a.check(fF(0,b)),a.nonnegative=b=>a.check(fG(0,b)),a.negative=b=>a.check(fD(0,b)),a.nonpositive=b=>a.check(fE(0,b)),a.multipleOf=(b,c)=>a.check(fH(b,c)),a.step=(b,c)=>a.check(fH(b,c)),a.finite=()=>a;let c=a._zod.bag;a.minValue=Math.max(c.minimum??-1/0,c.exclusiveMinimum??-1/0)??null,a.maxValue=Math.min(c.maximum??1/0,c.exclusiveMaximum??1/0)??null,a.isInt=(c.format??"").includes("int")||Number.isSafeInteger(c.multipleOf??.5),a.isFinite=!0,a.format=c.format??null});function gd(a){return new gc({type:"number",checks:[],...bn(a)})}let ge=bw("ZodNumberFormat",(a,b)=>{e2.init(a,b),gc.init(a,b)});function gf(a){return new ge({type:"number",check:"number_format",abort:!1,format:"safeint",...bn(a)})}let gg=bw("ZodBoolean",(a,b)=>{e3.init(a,b),fQ.init(a,b)});function gh(a){return new gg({type:"boolean",...bn(a)})}let gi=bw("ZodNull",(a,b)=>{e4.init(a,b),fQ.init(a,b)}),gj=bw("ZodAny",(a,b)=>{e5.init(a,b),fQ.init(a,b)});function gk(){return new gj({type:"any"})}let gl=bw("ZodUnknown",(a,b)=>{e6.init(a,b),fQ.init(a,b)});function gm(){return new gl({type:"unknown"})}let gn=bw("ZodNever",(a,b)=>{e7.init(a,b),fQ.init(a,b)});function go(a){return new gn({type:"never",...bn(a)})}let gp=bw("ZodArray",(a,b)=>{e9.init(a,b),fQ.init(a,b),a.element=b.element,a.min=(b,c)=>a.check(fJ(b,c)),a.nonempty=b=>a.check(fJ(1,b)),a.max=(b,c)=>a.check(fI(b,c)),a.length=(b,c)=>a.check(fK(b,c)),a.unwrap=()=>a.element});function gq(a,b){return new gp({type:"array",element:a,...bn(b)})}let gr=bw("ZodObject",(a,b)=>{fc.init(a,b),fQ.init(a,b),bd(a,"shape",()=>b.shape),a.keyof=()=>gD(Object.keys(a._zod.def.shape)),a.catchall=b=>a.clone({...a._zod.def,catchall:b}),a.passthrough=()=>a.clone({...a._zod.def,catchall:gm()}),a.loose=()=>a.clone({...a._zod.def,catchall:gm()}),a.strict=()=>a.clone({...a._zod.def,catchall:go()}),a.strip=()=>a.clone({...a._zod.def,catchall:void 0}),a.extend=b=>(function(a,b){if(!bj(b))throw Error("Invalid input to extend: expected a plain object");let c={...a._zod.def,get shape(){let c={...a._zod.def.shape,...b};return be(this,"shape",c),c},checks:[]};return bm(a,c)})(a,b),a.merge=b=>(function(a,b){return bm(a,{...a._zod.def,get shape(){let c={...a._zod.def.shape,...b._zod.def.shape};return be(this,"shape",c),c},catchall:b._zod.def.catchall,checks:[]})})(a,b),a.pick=b=>(function(a,b){let c={},d=a._zod.def;for(let a in b){if(!(a in d.shape))throw Error(`Unrecognized key: "${a}"`);b[a]&&(c[a]=d.shape[a])}return bm(a,{...a._zod.def,shape:c,checks:[]})})(a,b),a.omit=b=>(function(a,b){let c={...a._zod.def.shape},d=a._zod.def;for(let a in b){if(!(a in d.shape))throw Error(`Unrecognized key: "${a}"`);b[a]&&delete c[a]}return bm(a,{...a._zod.def,shape:c,checks:[]})})(a,b),a.partial=(...b)=>(function(a,b,c){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in d))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=a?new a({type:"optional",innerType:d[b]}):d[b])}else for(let b in d)e[b]=a?new a({type:"optional",innerType:d[b]}):d[b];return bm(b,{...b._zod.def,shape:e,checks:[]})})(gH,a,b[0]),a.required=(...b)=>(function(a,b,c){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in e))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=new a({type:"nonoptional",innerType:d[b]}))}else for(let b in d)e[b]=new a({type:"nonoptional",innerType:d[b]});return bm(b,{...b._zod.def,shape:e,checks:[]})})(gN,a,b[0])});function gs(a,b){return new gr({type:"object",get shape(){return be(this,"shape",{...a}),this.shape},...bn(b)})}function gt(a,b){return new gr({type:"object",get shape(){return be(this,"shape",{...a}),this.shape},catchall:go(),...bn(b)})}function gu(a,b){return new gr({type:"object",get shape(){return be(this,"shape",{...a}),this.shape},catchall:gm(),...bn(b)})}let gv=bw("ZodUnion",(a,b)=>{fe.init(a,b),fQ.init(a,b),a.options=b.options});function gw(a,b){return new gv({type:"union",options:a,...bn(b)})}let gx=bw("ZodDiscriminatedUnion",(a,b)=>{gv.init(a,b),ff.init(a,b)});function gy(a,b,c){return new gx({type:"union",options:b,discriminator:a,...bn(c)})}let gz=bw("ZodIntersection",(a,b)=>{fg.init(a,b),fQ.init(a,b)}),gA=bw("ZodRecord",(a,b)=>{fi.init(a,b),fQ.init(a,b),a.keyType=b.keyType,a.valueType=b.valueType});function gB(a,b,c){return new gA({type:"record",keyType:a,valueType:b,...bn(c)})}let gC=bw("ZodEnum",(a,b)=>{fj.init(a,b),fQ.init(a,b),a.enum=b.entries,a.options=Object.values(b.entries);let c=new Set(Object.keys(b.entries));a.extract=(a,d)=>{let e={};for(let d of a)if(c.has(d))e[d]=b.entries[d];else throw Error(`Key ${d} not found in enum`);return new gC({...b,checks:[],...bn(d),entries:e})},a.exclude=(a,d)=>{let e={...b.entries};for(let b of a)if(c.has(b))delete e[b];else throw Error(`Key ${b} not found in enum`);return new gC({...b,checks:[],...bn(d),entries:e})}});function gD(a,b){return new gC({type:"enum",entries:Array.isArray(a)?Object.fromEntries(a.map(a=>[a,a])):a,...bn(b)})}let gE=bw("ZodLiteral",(a,b)=>{fk.init(a,b),fQ.init(a,b),a.values=new Set(b.values),Object.defineProperty(a,"value",{get(){if(b.values.length>1)throw Error("This schema contains multiple valid literal values. Use `.values` instead.");return b.values[0]}})});function gF(a,b){return new gE({type:"literal",values:Array.isArray(a)?a:[a],...bn(b)})}let gG=bw("ZodTransform",(a,b)=>{fl.init(a,b),fQ.init(a,b),a._zod.parse=(c,d)=>{c.addIssue=d=>{"string"==typeof d?c.issues.push(bu(d,c.value,b)):(d.fatal&&(d.continue=!1),d.code??(d.code="custom"),d.input??(d.input=c.value),d.inst??(d.inst=a),d.continue??(d.continue=!0),c.issues.push(bu(d)))};let e=b.transform(c.value,c);return e instanceof Promise?e.then(a=>(c.value=a,c)):(c.value=e,c)}}),gH=bw("ZodOptional",(a,b)=>{fm.init(a,b),fQ.init(a,b),a.unwrap=()=>a._zod.def.innerType});function gI(a){return new gH({type:"optional",innerType:a})}let gJ=bw("ZodNullable",(a,b)=>{fn.init(a,b),fQ.init(a,b),a.unwrap=()=>a._zod.def.innerType});function gK(a){return new gJ({type:"nullable",innerType:a})}let gL=bw("ZodDefault",(a,b)=>{fo.init(a,b),fQ.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeDefault=a.unwrap}),gM=bw("ZodPrefault",(a,b)=>{fq.init(a,b),fQ.init(a,b),a.unwrap=()=>a._zod.def.innerType}),gN=bw("ZodNonOptional",(a,b)=>{fr.init(a,b),fQ.init(a,b),a.unwrap=()=>a._zod.def.innerType}),gO=bw("ZodCatch",(a,b)=>{ft.init(a,b),fQ.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeCatch=a.unwrap}),gP=bw("ZodPipe",(a,b)=>{fu.init(a,b),fQ.init(a,b),a.in=b.in,a.out=b.out});function gQ(a,b){return new gP({type:"pipe",in:a,out:b})}let gR=bw("ZodReadonly",(a,b)=>{fw.init(a,b),fQ.init(a,b)}),gS=bw("ZodLazy",(a,b)=>{fy.init(a,b),fQ.init(a,b),a.unwrap=()=>a._zod.def.getter()});function gT(a){return new gS({type:"lazy",getter:a})}let gU=bw("ZodCustom",(a,b)=>{fz.init(a,b),fQ.init(a,b)});function gV(a,b={error:`Input not instance of ${a.name}`}){let c=new gU({type:"custom",check:"custom",fn:b=>b instanceof a,abort:!0,...bn(b)});return c._zod.bag.Class=a,c}var gW=gs({error:gs({message:fT(),type:fT().nullish(),param:gk().nullish(),code:gw([fT(),gd()]).nullish()})}),gX=dH({errorSchema:gW,errorToMessage:a=>a.error.message});function gY({id:a,model:b,created:c}){return{id:null!=a?a:void 0,modelId:null!=b?b:void 0,timestamp:null!=c?new Date(1e3*c):void 0}}function gZ(a){switch(a){case"stop":return"stop";case"length":return"length";case"content_filter":return"content-filter";case"function_call":case"tool_calls":return"tool-calls";default:return"unknown"}}var g$=gs({logitBias:gB(new gc({type:"number",coerce:!0,checks:[],...bn(void 0)}),gd()).optional(),logprobs:gw([gh(),gd()]).optional(),parallelToolCalls:gh().optional(),user:fT().optional(),reasoningEffort:gD(["minimal","low","medium","high"]).optional(),maxCompletionTokens:gd().optional(),store:gh().optional(),metadata:gB(fT().max(64),fT().max(512)).optional(),prediction:gB(fT(),gk()).optional(),structuredOutputs:gh().optional(),serviceTier:gD(["auto","flex","priority"]).optional(),strictJsonSchema:gh().optional(),textVerbosity:gD(["low","medium","high"]).optional(),promptCacheKey:fT().optional(),safetyIdentifier:fT().optional()}),g_=gs({key:fT(),type:gD(["eq","ne","gt","gte","lt","lte"]),value:gw([fT(),gd(),gh()])}),g0=gs({type:gD(["and","or"]),filters:gq(gw([g_,gT(()=>g0)]))}),g1=gw([g_,g0]),g2=gs({vectorStoreIds:gq(fT()).optional(),maxNumResults:gd().optional(),ranking:gs({ranker:gD(["auto","default-2024-08-21"]).optional()}).optional(),filters:g1.optional()}),g3=dF({id:"openai.file_search",name:"file_search",inputSchema:gs({query:fT()})}),g4=gs({searchContextSize:gD(["low","medium","high"]).optional(),userLocation:gs({type:gF("approximate"),country:fT().optional(),city:fT().optional(),region:fT().optional(),timezone:fT().optional()}).optional()}),g5=dF({id:"openai.web_search_preview",name:"web_search_preview",inputSchema:gs({action:gy("type",[gs({type:gF("search"),query:fT()}),gs({type:gF("open_page"),url:fT()}),gs({type:gF("find"),url:fT(),pattern:fT()})]).nullish()})}),g6=class{constructor(a,b){this.specificationVersion="v2",this.supportedUrls={"image/*":[/^https?:\/\/.*$/]},this.modelId=a,this.config=b}get provider(){return this.config.provider}async getArgs({prompt:a,maxOutputTokens:b,temperature:c,topP:d,topK:e,frequencyPenalty:f,presencePenalty:g,stopSequences:h,responseFormat:i,seed:j,tools:k,toolChoice:l,providerOptions:m}){var n,o,p,q,r,s,t,u,v;let w=[],x=null!=(n=await dA({provider:"openai",providerOptions:m,schema:g$}))?n:{},y=null==(o=x.structuredOutputs)||o;null!=e&&w.push({type:"unsupported-setting",setting:"topK"}),(null==i?void 0:i.type)!=="json"||null==i.schema||y||w.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format schema is only supported with structuredOutputs"});let{messages:z,warnings:A}=function({prompt:a,systemMessageMode:b="system"}){let c=[],d=[];for(let{role:e,content:f}of a)switch(e){case"system":switch(b){case"system":c.push({role:"system",content:f});break;case"developer":c.push({role:"developer",content:f});break;case"remove":d.push({type:"other",message:"system messages are removed for this model"});break;default:throw Error(`Unsupported system message mode: ${b}`)}break;case"user":if(1===f.length&&"text"===f[0].type){c.push({role:"user",content:f[0].text});break}c.push({role:"user",content:f.map((a,b)=>{var c,d,e;switch(a.type){case"text":return{type:"text",text:a.text};case"file":if(a.mediaType.startsWith("image/")){let b="image/*"===a.mediaType?"image/jpeg":a.mediaType;return{type:"image_url",image_url:{url:a.data instanceof URL?a.data.toString():`data:${b};base64,${dR(a.data)}`,detail:null==(d=null==(c=a.providerOptions)?void 0:c.openai)?void 0:d.imageDetail}}}if(a.mediaType.startsWith("audio/")){if(a.data instanceof URL)throw new a3({functionality:"audio file parts with URLs"});switch(a.mediaType){case"audio/wav":return{type:"input_audio",input_audio:{data:dR(a.data),format:"wav"}};case"audio/mp3":case"audio/mpeg":return{type:"input_audio",input_audio:{data:dR(a.data),format:"mp3"}};default:throw new a3({functionality:`audio content parts with media type ${a.mediaType}`})}}if("application/pdf"===a.mediaType){if(a.data instanceof URL)throw new a3({functionality:"PDF file parts with URLs"});return{type:"file",file:"string"==typeof a.data&&a.data.startsWith("file-")?{file_id:a.data}:{filename:null!=(e=a.filename)?e:`part-${b}.pdf`,file_data:`data:application/pdf;base64,${dR(a.data)}`}}}else throw new a3({functionality:`file part media type ${a.mediaType}`})}})});break;case"assistant":{let a="",b=[];for(let c of f)switch(c.type){case"text":a+=c.text;break;case"tool-call":b.push({id:c.toolCallId,type:"function",function:{name:c.toolName,arguments:JSON.stringify(c.input)}})}c.push({role:"assistant",content:a,tool_calls:b.length>0?b:void 0});break}case"tool":for(let a of f){let b,d=a.output;switch(d.type){case"text":case"error-text":b=d.value;break;case"content":case"json":case"error-json":b=JSON.stringify(d.value)}c.push({role:"tool",tool_call_id:a.toolCallId,content:b})}break;default:throw Error(`Unsupported role: ${e}`)}return{messages:c,warnings:d}}({prompt:a,systemMessageMode:ha(r=this.modelId)?null!=(t=null==(s=hb[r])?void 0:s.systemMessageMode)?t:"developer":"system"});w.push(...A);let B=null!=(p=x.strictJsonSchema)&&p,C={model:this.modelId,logit_bias:x.logitBias,logprobs:!0===x.logprobs||"number"==typeof x.logprobs||void 0,top_logprobs:"number"==typeof x.logprobs?x.logprobs:"boolean"==typeof x.logprobs&&x.logprobs?0:void 0,user:x.user,parallel_tool_calls:x.parallelToolCalls,max_tokens:b,temperature:c,top_p:d,frequency_penalty:f,presence_penalty:g,response_format:(null==i?void 0:i.type)==="json"?y&&null!=i.schema?{type:"json_schema",json_schema:{schema:i.schema,strict:B,name:null!=(q=i.name)?q:"response",description:i.description}}:{type:"json_object"}:void 0,stop:h,seed:j,verbosity:x.textVerbosity,max_completion_tokens:x.maxCompletionTokens,store:x.store,metadata:x.metadata,prediction:x.prediction,reasoning_effort:x.reasoningEffort,service_tier:x.serviceTier,prompt_cache_key:x.promptCacheKey,safety_identifier:x.safetyIdentifier,messages:z};ha(this.modelId)?(null!=C.temperature&&(C.temperature=void 0,w.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for reasoning models"})),null!=C.top_p&&(C.top_p=void 0,w.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported for reasoning models"})),null!=C.frequency_penalty&&(C.frequency_penalty=void 0,w.push({type:"unsupported-setting",setting:"frequencyPenalty",details:"frequencyPenalty is not supported for reasoning models"})),null!=C.presence_penalty&&(C.presence_penalty=void 0,w.push({type:"unsupported-setting",setting:"presencePenalty",details:"presencePenalty is not supported for reasoning models"})),null!=C.logit_bias&&(C.logit_bias=void 0,w.push({type:"other",message:"logitBias is not supported for reasoning models"})),null!=C.logprobs&&(C.logprobs=void 0,w.push({type:"other",message:"logprobs is not supported for reasoning models"})),null!=C.top_logprobs&&(C.top_logprobs=void 0,w.push({type:"other",message:"topLogprobs is not supported for reasoning models"})),null!=C.max_tokens&&(null==C.max_completion_tokens&&(C.max_completion_tokens=C.max_tokens),C.max_tokens=void 0)):(this.modelId.startsWith("gpt-4o-search-preview")||this.modelId.startsWith("gpt-4o-mini-search-preview"))&&null!=C.temperature&&(C.temperature=void 0,w.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for the search preview models and has been removed."})),"flex"!==x.serviceTier||(u=this.modelId).startsWith("o3")||u.startsWith("o4-mini")||u.startsWith("gpt-5")&&!u.startsWith("gpt-5-chat")||(w.push({type:"unsupported-setting",setting:"serviceTier",details:"flex processing is only available for o3, o4-mini, and gpt-5 models"}),C.service_tier=void 0),"priority"!==x.serviceTier||(v=this.modelId).startsWith("gpt-4")||v.startsWith("gpt-5-mini")||v.startsWith("gpt-5")&&!v.startsWith("gpt-5-nano")&&!v.startsWith("gpt-5-chat")||v.startsWith("o3")||v.startsWith("o4-mini")||(w.push({type:"unsupported-setting",setting:"serviceTier",details:"priority processing is only available for supported models (gpt-4, gpt-5, gpt-5-mini, o3, o4-mini) and requires Enterprise access. gpt-5-nano is not supported"}),C.service_tier=void 0);let{tools:D,toolChoice:E,toolWarnings:F}=function({tools:a,toolChoice:b,structuredOutputs:c,strictJsonSchema:d}){a=(null==a?void 0:a.length)?a:void 0;let e=[];if(null==a)return{tools:void 0,toolChoice:void 0,toolWarnings:e};let f=[];for(let b of a)switch(b.type){case"function":f.push({type:"function",function:{name:b.name,description:b.description,parameters:b.inputSchema,strict:c?d:void 0}});break;case"provider-defined":switch(b.id){case"openai.file_search":{let a=g2.parse(b.args);f.push({type:"file_search",vector_store_ids:a.vectorStoreIds,max_num_results:a.maxNumResults,ranking_options:a.ranking?{ranker:a.ranking.ranker}:void 0,filters:a.filters});break}case"openai.web_search_preview":{let a=g4.parse(b.args);f.push({type:"web_search_preview",search_context_size:a.searchContextSize,user_location:a.userLocation});break}default:e.push({type:"unsupported-tool",tool:b})}break;default:e.push({type:"unsupported-tool",tool:b})}if(null==b)return{tools:f,toolChoice:void 0,toolWarnings:e};let g=b.type;switch(g){case"auto":case"none":case"required":return{tools:f,toolChoice:g,toolWarnings:e};case"tool":return{tools:f,toolChoice:{type:"function",function:{name:b.toolName}},toolWarnings:e};default:throw new a3({functionality:`tool choice type: ${g}`})}}({tools:k,toolChoice:l,structuredOutputs:y,strictJsonSchema:B});return{args:{...C,tools:D,tool_choice:E},warnings:[...w,...F]}}async doGenerate(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o;let{args:p,warnings:q}=await this.getArgs(a),{responseHeaders:r,value:s,rawValue:t}=await dC({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:dc(this.config.headers(),a.headers),body:p,failedResponseHandler:gX,successfulResponseHandler:dJ(g8),abortSignal:a.abortSignal,fetch:this.config.fetch}),u=s.choices[0],v=[],w=u.message.content;for(let a of(null!=w&&w.length>0&&v.push({type:"text",text:w}),null!=(b=u.message.tool_calls)?b:[]))v.push({type:"tool-call",toolCallId:null!=(c=a.id)?c:dh(),toolName:a.function.name,input:a.function.arguments});for(let a of null!=(d=u.message.annotations)?d:[])v.push({type:"source",sourceType:"url",id:dh(),url:a.url,title:a.title});let x=null==(e=s.usage)?void 0:e.completion_tokens_details,y=null==(f=s.usage)?void 0:f.prompt_tokens_details,z={openai:{}};return(null==x?void 0:x.accepted_prediction_tokens)!=null&&(z.openai.acceptedPredictionTokens=null==x?void 0:x.accepted_prediction_tokens),(null==x?void 0:x.rejected_prediction_tokens)!=null&&(z.openai.rejectedPredictionTokens=null==x?void 0:x.rejected_prediction_tokens),(null==(g=u.logprobs)?void 0:g.content)!=null&&(z.openai.logprobs=u.logprobs.content),{content:v,finishReason:gZ(u.finish_reason),usage:{inputTokens:null!=(i=null==(h=s.usage)?void 0:h.prompt_tokens)?i:void 0,outputTokens:null!=(k=null==(j=s.usage)?void 0:j.completion_tokens)?k:void 0,totalTokens:null!=(m=null==(l=s.usage)?void 0:l.total_tokens)?m:void 0,reasoningTokens:null!=(n=null==x?void 0:x.reasoning_tokens)?n:void 0,cachedInputTokens:null!=(o=null==y?void 0:y.cached_tokens)?o:void 0},request:{body:p},response:{...gY(s),headers:r,body:t},warnings:q,providerMetadata:z}}async doStream(a){let{args:b,warnings:c}=await this.getArgs(a),d={...b,stream:!0,stream_options:{include_usage:!0}},{responseHeaders:e,value:f}=await dC({url:this.config.url({path:"/chat/completions",modelId:this.modelId}),headers:dc(this.config.headers(),a.headers),body:d,failedResponseHandler:gX,successfulResponseHandler:dI(g9),abortSignal:a.abortSignal,fetch:this.config.fetch}),g=[],h="unknown",i={inputTokens:void 0,outputTokens:void 0,totalTokens:void 0},j=!0,k=!1,l={openai:{}};return{stream:f.pipeThrough(new TransformStream({start(a){a.enqueue({type:"stream-start",warnings:c})},transform(b,c){var d,e,f,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G;if(a.includeRawChunks&&c.enqueue({type:"raw",rawValue:b.rawValue}),!b.success){h="error",c.enqueue({type:"error",error:b.error});return}let H=b.value;if("error"in H){h="error",c.enqueue({type:"error",error:H.error});return}j&&(j=!1,c.enqueue({type:"response-metadata",...gY(H)})),null!=H.usage&&(i.inputTokens=null!=(d=H.usage.prompt_tokens)?d:void 0,i.outputTokens=null!=(e=H.usage.completion_tokens)?e:void 0,i.totalTokens=null!=(f=H.usage.total_tokens)?f:void 0,i.reasoningTokens=null!=(n=null==(m=H.usage.completion_tokens_details)?void 0:m.reasoning_tokens)?n:void 0,i.cachedInputTokens=null!=(p=null==(o=H.usage.prompt_tokens_details)?void 0:o.cached_tokens)?p:void 0,(null==(q=H.usage.completion_tokens_details)?void 0:q.accepted_prediction_tokens)!=null&&(l.openai.acceptedPredictionTokens=null==(r=H.usage.completion_tokens_details)?void 0:r.accepted_prediction_tokens),(null==(s=H.usage.completion_tokens_details)?void 0:s.rejected_prediction_tokens)!=null&&(l.openai.rejectedPredictionTokens=null==(t=H.usage.completion_tokens_details)?void 0:t.rejected_prediction_tokens));let I=H.choices[0];if((null==I?void 0:I.finish_reason)!=null&&(h=gZ(I.finish_reason)),(null==(u=null==I?void 0:I.logprobs)?void 0:u.content)!=null&&(l.openai.logprobs=I.logprobs.content),(null==I?void 0:I.delta)==null)return;let J=I.delta;if(null!=J.content&&(k||(c.enqueue({type:"text-start",id:"0"}),k=!0),c.enqueue({type:"text-delta",id:"0",delta:J.content})),null!=J.tool_calls)for(let a of J.tool_calls){let b=a.index;if(null==g[b]){if("function"!==a.type)throw new aH({data:a,message:"Expected 'function' type."});if(null==a.id)throw new aH({data:a,message:"Expected 'id' to be a string."});if((null==(v=a.function)?void 0:v.name)==null)throw new aH({data:a,message:"Expected 'function.name' to be a string."});c.enqueue({type:"tool-input-start",id:a.id,toolName:a.function.name}),g[b]={id:a.id,type:"function",function:{name:a.function.name,arguments:null!=(w=a.function.arguments)?w:""},hasFinished:!1};let d=g[b];(null==(x=d.function)?void 0:x.name)!=null&&(null==(y=d.function)?void 0:y.arguments)!=null&&(d.function.arguments.length>0&&c.enqueue({type:"tool-input-delta",id:d.id,delta:d.function.arguments}),dz(d.function.arguments)&&(c.enqueue({type:"tool-input-end",id:d.id}),c.enqueue({type:"tool-call",toolCallId:null!=(z=d.id)?z:dh(),toolName:d.function.name,input:d.function.arguments}),d.hasFinished=!0));continue}let d=g[b];!d.hasFinished&&((null==(A=a.function)?void 0:A.arguments)!=null&&(d.function.arguments+=null!=(C=null==(B=a.function)?void 0:B.arguments)?C:""),c.enqueue({type:"tool-input-delta",id:d.id,delta:null!=(D=a.function.arguments)?D:""}),(null==(E=d.function)?void 0:E.name)!=null&&(null==(F=d.function)?void 0:F.arguments)!=null&&dz(d.function.arguments)&&(c.enqueue({type:"tool-input-end",id:d.id}),c.enqueue({type:"tool-call",toolCallId:null!=(G=d.id)?G:dh(),toolName:d.function.name,input:d.function.arguments}),d.hasFinished=!0))}if(null!=J.annotations)for(let a of J.annotations)c.enqueue({type:"source",sourceType:"url",id:dh(),url:a.url,title:a.title})},flush(a){k&&a.enqueue({type:"text-end",id:"0"}),a.enqueue({type:"finish",finishReason:h,usage:i,...null!=l?{providerMetadata:l}:{}})}})),request:{body:d},response:{headers:e}}}},g7=gs({prompt_tokens:gd().nullish(),completion_tokens:gd().nullish(),total_tokens:gd().nullish(),prompt_tokens_details:gs({cached_tokens:gd().nullish()}).nullish(),completion_tokens_details:gs({reasoning_tokens:gd().nullish(),accepted_prediction_tokens:gd().nullish(),rejected_prediction_tokens:gd().nullish()}).nullish()}).nullish(),g8=gs({id:fT().nullish(),created:gd().nullish(),model:fT().nullish(),choices:gq(gs({message:gs({role:gF("assistant").nullish(),content:fT().nullish(),tool_calls:gq(gs({id:fT().nullish(),type:gF("function"),function:gs({name:fT(),arguments:fT()})})).nullish(),annotations:gq(gs({type:gF("url_citation"),start_index:gd(),end_index:gd(),url:fT(),title:fT()})).nullish()}),index:gd(),logprobs:gs({content:gq(gs({token:fT(),logprob:gd(),top_logprobs:gq(gs({token:fT(),logprob:gd()}))})).nullish()}).nullish(),finish_reason:fT().nullish()})),usage:g7}),g9=gw([gs({id:fT().nullish(),created:gd().nullish(),model:fT().nullish(),choices:gq(gs({delta:gs({role:gD(["assistant"]).nullish(),content:fT().nullish(),tool_calls:gq(gs({index:gd(),id:fT().nullish(),type:gF("function").nullish(),function:gs({name:fT().nullish(),arguments:fT().nullish()})})).nullish(),annotations:gq(gs({type:gF("url_citation"),start_index:gd(),end_index:gd(),url:fT(),title:fT()})).nullish()}).nullish(),logprobs:gs({content:gq(gs({token:fT(),logprob:gd(),top_logprobs:gq(gs({token:fT(),logprob:gd()}))})).nullish()}).nullish(),finish_reason:fT().nullish(),index:gd()})),usage:g7}),gW]);function ha(a){return(a.startsWith("o")||a.startsWith("gpt-5"))&&!a.startsWith("gpt-5-chat")}var hb={"o1-mini":{systemMessageMode:"remove"},"o1-mini-2024-09-12":{systemMessageMode:"remove"},"o1-preview":{systemMessageMode:"remove"},"o1-preview-2024-09-12":{systemMessageMode:"remove"},o3:{systemMessageMode:"developer"},"o3-2025-04-16":{systemMessageMode:"developer"},"o3-mini":{systemMessageMode:"developer"},"o3-mini-2025-01-31":{systemMessageMode:"developer"},"o4-mini":{systemMessageMode:"developer"},"o4-mini-2025-04-16":{systemMessageMode:"developer"}};function hc({id:a,model:b,created:c}){return{id:null!=a?a:void 0,modelId:null!=b?b:void 0,timestamp:null!=c?new Date(1e3*c):void 0}}function hd(a){switch(a){case"stop":return"stop";case"length":return"length";case"content_filter":return"content-filter";case"function_call":case"tool_calls":return"tool-calls";default:return"unknown"}}var he=gs({echo:gh().optional(),logitBias:gB(fT(),gd()).optional(),suffix:fT().optional(),user:fT().optional(),logprobs:gw([gh(),gd()]).optional()}),hf=class{constructor(a,b){this.specificationVersion="v2",this.supportedUrls={},this.modelId=a,this.config=b}get providerOptionsName(){return this.config.provider.split(".")[0].trim()}get provider(){return this.config.provider}async getArgs({prompt:a,maxOutputTokens:b,temperature:c,topP:d,topK:e,frequencyPenalty:f,presencePenalty:g,stopSequences:h,responseFormat:i,tools:j,toolChoice:k,seed:l,providerOptions:m}){let n=[],o={...await dA({provider:"openai",providerOptions:m,schema:he}),...await dA({provider:this.providerOptionsName,providerOptions:m,schema:he})};null!=e&&n.push({type:"unsupported-setting",setting:"topK"}),(null==j?void 0:j.length)&&n.push({type:"unsupported-setting",setting:"tools"}),null!=k&&n.push({type:"unsupported-setting",setting:"toolChoice"}),null!=i&&"text"!==i.type&&n.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format is not supported."});let{prompt:p,stopSequences:q}=function({prompt:a,user:b="user",assistant:c="assistant"}){let d="";for(let{role:e,content:f}of("system"===a[0].role&&(d+=`${a[0].content}

`,a=a.slice(1)),a))switch(e){case"system":throw new aD({message:"Unexpected system message in prompt: ${content}",prompt:a});case"user":{let a=f.map(a=>{if("text"===a.type)return a.text}).filter(Boolean).join("");d+=`${b}:
${a}

`;break}case"assistant":{let a=f.map(a=>{switch(a.type){case"text":return a.text;case"tool-call":throw new a3({functionality:"tool-call messages"})}}).join("");d+=`${c}:
${a}

`;break}case"tool":throw new a3({functionality:"tool messages"});default:throw Error(`Unsupported role: ${e}`)}return{prompt:d+=`${c}:
`,stopSequences:[`
${b}:`]}}({prompt:a}),r=[...null!=q?q:[],...null!=h?h:[]];return{args:{model:this.modelId,echo:o.echo,logit_bias:o.logitBias,logprobs:(null==o?void 0:o.logprobs)===!0?0:(null==o?void 0:o.logprobs)===!1||null==o?void 0:o.logprobs,suffix:o.suffix,user:o.user,max_tokens:b,temperature:c,top_p:d,frequency_penalty:f,presence_penalty:g,seed:l,prompt:p,stop:r.length>0?r:void 0},warnings:n}}async doGenerate(a){var b,c,d;let{args:e,warnings:f}=await this.getArgs(a),{responseHeaders:g,value:h,rawValue:i}=await dC({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:dc(this.config.headers(),a.headers),body:e,failedResponseHandler:gX,successfulResponseHandler:dJ(hh),abortSignal:a.abortSignal,fetch:this.config.fetch}),j=h.choices[0],k={openai:{}};return null!=j.logprobs&&(k.openai.logprobs=j.logprobs),{content:[{type:"text",text:j.text}],usage:{inputTokens:null==(b=h.usage)?void 0:b.prompt_tokens,outputTokens:null==(c=h.usage)?void 0:c.completion_tokens,totalTokens:null==(d=h.usage)?void 0:d.total_tokens},finishReason:hd(j.finish_reason),request:{body:e},response:{...hc(h),headers:g,body:i},providerMetadata:k,warnings:f}}async doStream(a){let{args:b,warnings:c}=await this.getArgs(a),d={...b,stream:!0,stream_options:{include_usage:!0}},{responseHeaders:e,value:f}=await dC({url:this.config.url({path:"/completions",modelId:this.modelId}),headers:dc(this.config.headers(),a.headers),body:d,failedResponseHandler:gX,successfulResponseHandler:dI(hi),abortSignal:a.abortSignal,fetch:this.config.fetch}),g="unknown",h={openai:{}},i={inputTokens:void 0,outputTokens:void 0,totalTokens:void 0},j=!0;return{stream:f.pipeThrough(new TransformStream({start(a){a.enqueue({type:"stream-start",warnings:c})},transform(b,c){if(a.includeRawChunks&&c.enqueue({type:"raw",rawValue:b.rawValue}),!b.success){g="error",c.enqueue({type:"error",error:b.error});return}let d=b.value;if("error"in d){g="error",c.enqueue({type:"error",error:d.error});return}j&&(j=!1,c.enqueue({type:"response-metadata",...hc(d)}),c.enqueue({type:"text-start",id:"0"})),null!=d.usage&&(i.inputTokens=d.usage.prompt_tokens,i.outputTokens=d.usage.completion_tokens,i.totalTokens=d.usage.total_tokens);let e=d.choices[0];(null==e?void 0:e.finish_reason)!=null&&(g=hd(e.finish_reason)),(null==e?void 0:e.logprobs)!=null&&(h.openai.logprobs=e.logprobs),(null==e?void 0:e.text)!=null&&e.text.length>0&&c.enqueue({type:"text-delta",id:"0",delta:e.text})},flush(a){j||a.enqueue({type:"text-end",id:"0"}),a.enqueue({type:"finish",finishReason:g,providerMetadata:h,usage:i})}})),request:{body:d},response:{headers:e}}}},hg=gs({prompt_tokens:gd(),completion_tokens:gd(),total_tokens:gd()}),hh=gs({id:fT().nullish(),created:gd().nullish(),model:fT().nullish(),choices:gq(gs({text:fT(),finish_reason:fT(),logprobs:gs({tokens:gq(fT()),token_logprobs:gq(gd()),top_logprobs:gq(gB(fT(),gd())).nullish()}).nullish()})),usage:hg.nullish()}),hi=gw([gs({id:fT().nullish(),created:gd().nullish(),model:fT().nullish(),choices:gq(gs({text:fT(),finish_reason:fT().nullish(),index:gd(),logprobs:gs({tokens:gq(fT()),token_logprobs:gq(gd()),top_logprobs:gq(gB(fT(),gd())).nullish()}).nullish()})),usage:hg.nullish()}),gW]),hj=gs({dimensions:gd().optional(),user:fT().optional()}),hk=class{constructor(a,b){this.specificationVersion="v2",this.maxEmbeddingsPerCall=2048,this.supportsParallelCalls=!0,this.modelId=a,this.config=b}get provider(){return this.config.provider}async doEmbed({values:a,headers:b,abortSignal:c,providerOptions:d}){var e;if(a.length>this.maxEmbeddingsPerCall)throw new aX({provider:this.provider,modelId:this.modelId,maxEmbeddingsPerCall:this.maxEmbeddingsPerCall,values:a});let f=null!=(e=await dA({provider:"openai",providerOptions:d,schema:hj}))?e:{},{responseHeaders:g,value:h,rawValue:i}=await dC({url:this.config.url({path:"/embeddings",modelId:this.modelId}),headers:dc(this.config.headers(),b),body:{model:this.modelId,input:a,encoding_format:"float",dimensions:f.dimensions,user:f.user},failedResponseHandler:gX,successfulResponseHandler:dJ(hl),abortSignal:c,fetch:this.config.fetch});return{embeddings:h.data.map(a=>a.embedding),usage:h.usage?{tokens:h.usage.prompt_tokens}:void 0,response:{headers:g,body:i}}}},hl=gs({data:gq(gs({embedding:gq(gd())})),usage:gs({prompt_tokens:gd()}).nullish()}),hm={"dall-e-3":1,"dall-e-2":10,"gpt-image-1":10},hn=new Set(["gpt-image-1"]),ho=class{constructor(a,b){this.modelId=a,this.config=b,this.specificationVersion="v2"}get maxImagesPerCall(){var a;return null!=(a=hm[this.modelId])?a:1}get provider(){return this.config.provider}async doGenerate({prompt:a,n:b,size:c,aspectRatio:d,seed:e,providerOptions:f,headers:g,abortSignal:h}){var i,j,k,l;let m=[];null!=d&&m.push({type:"unsupported-setting",setting:"aspectRatio",details:"This model does not support aspect ratio. Use `size` instead."}),null!=e&&m.push({type:"unsupported-setting",setting:"seed"});let n=null!=(k=null==(j=null==(i=this.config._internal)?void 0:i.currentDate)?void 0:j.call(i))?k:new Date,{value:o,responseHeaders:p}=await dC({url:this.config.url({path:"/images/generations",modelId:this.modelId}),headers:dc(this.config.headers(),g),body:{model:this.modelId,prompt:a,n:b,size:c,...null!=(l=f.openai)?l:{},...!hn.has(this.modelId)?{response_format:"b64_json"}:{}},failedResponseHandler:gX,successfulResponseHandler:dJ(hp),abortSignal:h,fetch:this.config.fetch});return{images:o.data.map(a=>a.b64_json),warnings:m,response:{timestamp:n,modelId:this.modelId,headers:p},providerMetadata:{openai:{images:o.data.map(a=>a.revised_prompt?{revisedPrompt:a.revised_prompt}:null)}}}}},hp=gs({data:gq(gs({b64_json:fT(),revised_prompt:fT().optional()}))}),hq=gs({container:gw([fT(),gs({fileIds:gq(fT()).optional()})]).optional()}),hr={codeInterpreter:dF({id:"openai.code_interpreter",name:"code_interpreter",inputSchema:gs({})}),fileSearch:g3,webSearchPreview:g5};function hs(a,b){return!!b&&b.some(b=>a.startsWith(b))}async function ht({prompt:a,systemMessageMode:b,fileIdPrefixes:c}){var d,e,f,g,h,i;let j=[],k=[];for(let{role:l,content:m}of a)switch(l){case"system":switch(b){case"system":j.push({role:"system",content:m});break;case"developer":j.push({role:"developer",content:m});break;case"remove":k.push({type:"other",message:"system messages are removed for this model"});break;default:throw Error(`Unsupported system message mode: ${b}`)}break;case"user":j.push({role:"user",content:m.map((a,b)=>{var d,e,f;switch(a.type){case"text":return{type:"input_text",text:a.text};case"file":if(a.mediaType.startsWith("image/")){let b="image/*"===a.mediaType?"image/jpeg":a.mediaType;return{type:"input_image",...a.data instanceof URL?{image_url:a.data.toString()}:"string"==typeof a.data&&hs(a.data,c)?{file_id:a.data}:{image_url:`data:${b};base64,${dR(a.data)}`},detail:null==(e=null==(d=a.providerOptions)?void 0:d.openai)?void 0:e.imageDetail}}if("application/pdf"===a.mediaType){if(a.data instanceof URL)throw new a3({functionality:"PDF file parts with URLs"});return{type:"input_file",..."string"==typeof a.data&&hs(a.data,c)?{file_id:a.data}:{filename:null!=(f=a.filename)?f:`part-${b}.pdf`,file_data:`data:application/pdf;base64,${dR(a.data)}`}}}throw new a3({functionality:`file part media type ${a.mediaType}`})}})});break;case"assistant":{let a={};for(let b of m)switch(b.type){case"text":j.push({role:"assistant",content:[{type:"output_text",text:b.text}],id:null!=(f=null==(e=null==(d=b.providerOptions)?void 0:d.openai)?void 0:e.itemId)?f:void 0});break;case"tool-call":if(b.providerExecuted)break;j.push({type:"function_call",call_id:b.toolCallId,name:b.toolName,arguments:JSON.stringify(b.input),id:null!=(i=null==(h=null==(g=b.providerOptions)?void 0:g.openai)?void 0:h.itemId)?i:void 0});break;case"tool-result":k.push({type:"other",message:"tool result parts in assistant messages are not supported for OpenAI responses"});break;case"reasoning":{let c=await dA({provider:"openai",providerOptions:b.providerOptions,schema:hu}),d=null==c?void 0:c.itemId;if(null!=d){let e=a[d],f=[];b.text.length>0?f.push({type:"summary_text",text:b.text}):void 0!==e&&k.push({type:"other",message:`Cannot append empty reasoning part to existing reasoning sequence. Skipping reasoning part: ${JSON.stringify(b)}.`}),void 0===e?(a[d]={type:"reasoning",id:d,encrypted_content:null==c?void 0:c.reasoningEncryptedContent,summary:f},j.push(a[d])):e.summary.push(...f)}else k.push({type:"other",message:`Non-OpenAI reasoning parts are not supported. Skipping reasoning part: ${JSON.stringify(b)}.`})}}break}case"tool":for(let a of m){let b,c=a.output;switch(c.type){case"text":case"error-text":b=c.value;break;case"content":case"json":case"error-json":b=JSON.stringify(c.value)}j.push({type:"function_call_output",call_id:a.toolCallId,output:b})}break;default:throw Error(`Unsupported role: ${l}`)}return{messages:j,warnings:k}}var hu=gs({itemId:fT().nullish(),reasoningEncryptedContent:fT().nullish()});function hv({finishReason:a,hasToolCalls:b}){switch(a){case void 0:case null:return b?"tool-calls":"stop";case"max_output_tokens":return"length";case"content_filter":return"content-filter";default:return b?"tool-calls":"unknown"}}var hw=gs({type:gF("web_search_call"),id:fT(),status:fT(),action:gy("type",[gs({type:gF("search"),query:fT()}),gs({type:gF("open_page"),url:fT()}),gs({type:gF("find"),url:fT(),pattern:fT()})]).nullish()}),hx=gq(gs({token:fT(),logprob:gd(),top_logprobs:gq(gs({token:fT(),logprob:gd()}))})),hy=class{constructor(a,b){this.specificationVersion="v2",this.supportedUrls={"image/*":[/^https?:\/\/.*$/]},this.modelId=a,this.config=b}get provider(){return this.config.provider}async getArgs({maxOutputTokens:a,temperature:b,stopSequences:c,topP:d,topK:e,presencePenalty:f,frequencyPenalty:g,seed:h,prompt:i,providerOptions:j,tools:k,toolChoice:l,responseFormat:m}){var n,o;let p=[],q=function(a){let b={requiredAutoTruncation:!1,systemMessageMode:"system",supportsFlexProcessing:a.startsWith("o3")||a.startsWith("o4-mini")||a.startsWith("gpt-5")&&!a.startsWith("gpt-5-chat"),supportsPriorityProcessing:a.startsWith("gpt-4")||a.startsWith("gpt-5-mini")||a.startsWith("gpt-5")&&!a.startsWith("gpt-5-nano")&&!a.startsWith("gpt-5-chat")||a.startsWith("o3")||a.startsWith("o4-mini")};return a.startsWith("gpt-5-chat")?{...b,isReasoningModel:!1}:a.startsWith("o")||a.startsWith("gpt-5")||a.startsWith("codex-")||a.startsWith("computer-use")?a.startsWith("o1-mini")||a.startsWith("o1-preview")?{...b,isReasoningModel:!0,systemMessageMode:"remove"}:{...b,isReasoningModel:!0,systemMessageMode:"developer"}:{...b,isReasoningModel:!1}}(this.modelId);null!=e&&p.push({type:"unsupported-setting",setting:"topK"}),null!=h&&p.push({type:"unsupported-setting",setting:"seed"}),null!=f&&p.push({type:"unsupported-setting",setting:"presencePenalty"}),null!=g&&p.push({type:"unsupported-setting",setting:"frequencyPenalty"}),null!=c&&p.push({type:"unsupported-setting",setting:"stopSequences"});let{messages:r,warnings:s}=await ht({prompt:i,systemMessageMode:q.systemMessageMode,fileIdPrefixes:this.config.fileIdPrefixes});p.push(...s);let t=await dA({provider:"openai",providerOptions:j,schema:hF}),u=null!=(n=null==t?void 0:t.strictJsonSchema)&&n,v="number"==typeof(null==t?void 0:t.logprobs)?null==t?void 0:t.logprobs:(null==t?void 0:t.logprobs)===!0?20:void 0,w=v?Array.isArray(null==t?void 0:t.include)?[...null==t?void 0:t.include,"message.output_text.logprobs"]:["message.output_text.logprobs"]:null==t?void 0:t.include,x={model:this.modelId,input:r,temperature:b,top_p:d,max_output_tokens:a,...((null==m?void 0:m.type)==="json"||(null==t?void 0:t.textVerbosity))&&{text:{...(null==m?void 0:m.type)==="json"&&{format:null!=m.schema?{type:"json_schema",strict:u,name:null!=(o=m.name)?o:"response",description:m.description,schema:m.schema}:{type:"json_object"}},...(null==t?void 0:t.textVerbosity)&&{verbosity:t.textVerbosity}}},metadata:null==t?void 0:t.metadata,parallel_tool_calls:null==t?void 0:t.parallelToolCalls,previous_response_id:null==t?void 0:t.previousResponseId,store:null==t?void 0:t.store,user:null==t?void 0:t.user,instructions:null==t?void 0:t.instructions,service_tier:null==t?void 0:t.serviceTier,include:w,prompt_cache_key:null==t?void 0:t.promptCacheKey,safety_identifier:null==t?void 0:t.safetyIdentifier,top_logprobs:v,...q.isReasoningModel&&((null==t?void 0:t.reasoningEffort)!=null||(null==t?void 0:t.reasoningSummary)!=null)&&{reasoning:{...(null==t?void 0:t.reasoningEffort)!=null&&{effort:t.reasoningEffort},...(null==t?void 0:t.reasoningSummary)!=null&&{summary:t.reasoningSummary}}},...q.requiredAutoTruncation&&{truncation:"auto"}};q.isReasoningModel?(null!=x.temperature&&(x.temperature=void 0,p.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported for reasoning models"})),null!=x.top_p&&(x.top_p=void 0,p.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported for reasoning models"}))):((null==t?void 0:t.reasoningEffort)!=null&&p.push({type:"unsupported-setting",setting:"reasoningEffort",details:"reasoningEffort is not supported for non-reasoning models"}),(null==t?void 0:t.reasoningSummary)!=null&&p.push({type:"unsupported-setting",setting:"reasoningSummary",details:"reasoningSummary is not supported for non-reasoning models"})),(null==t?void 0:t.serviceTier)!=="flex"||q.supportsFlexProcessing||(p.push({type:"unsupported-setting",setting:"serviceTier",details:"flex processing is only available for o3, o4-mini, and gpt-5 models"}),delete x.service_tier),(null==t?void 0:t.serviceTier)!=="priority"||q.supportsPriorityProcessing||(p.push({type:"unsupported-setting",setting:"serviceTier",details:"priority processing is only available for supported models (gpt-4, gpt-5, gpt-5-mini, o3, o4-mini) and requires Enterprise access. gpt-5-nano is not supported"}),delete x.service_tier);let{tools:y,toolChoice:z,toolWarnings:A}=function({tools:a,toolChoice:b,strictJsonSchema:c}){a=(null==a?void 0:a.length)?a:void 0;let d=[];if(null==a)return{tools:void 0,toolChoice:void 0,toolWarnings:d};let e=[];for(let b of a)switch(b.type){case"function":e.push({type:"function",name:b.name,description:b.description,parameters:b.inputSchema,strict:c});break;case"provider-defined":switch(b.id){case"openai.file_search":{let a=g2.parse(b.args);e.push({type:"file_search",vector_store_ids:a.vectorStoreIds,max_num_results:a.maxNumResults,ranking_options:a.ranking?{ranker:a.ranking.ranker}:void 0,filters:a.filters});break}case"openai.web_search_preview":{let a=g4.parse(b.args);e.push({type:"web_search_preview",search_context_size:a.searchContextSize,user_location:a.userLocation});break}case"openai.code_interpreter":{let a=hq.parse(b.args);e.push({type:"code_interpreter",container:null==a.container?{type:"auto",file_ids:void 0}:"string"==typeof a.container?a.container:{type:"auto",file_ids:a.container.fileIds}});break}default:d.push({type:"unsupported-tool",tool:b})}break;default:d.push({type:"unsupported-tool",tool:b})}if(null==b)return{tools:e,toolChoice:void 0,toolWarnings:d};let f=b.type;switch(f){case"auto":case"none":case"required":return{tools:e,toolChoice:f,toolWarnings:d};case"tool":return{tools:e,toolChoice:"code_interpreter"===b.toolName||"file_search"===b.toolName||"web_search_preview"===b.toolName?{type:b.toolName}:{type:"function",name:b.toolName},toolWarnings:d};default:throw new a3({functionality:`tool choice type: ${f}`})}}({tools:k,toolChoice:l,strictJsonSchema:u});return{args:{...x,tools:y,tool_choice:z},warnings:[...p,...A]}}async doGenerate(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o;let{args:p,warnings:q}=await this.getArgs(a),r=this.config.url({path:"/responses",modelId:this.modelId}),{responseHeaders:s,value:t,rawValue:u}=await dC({url:r,headers:dc(this.config.headers(),a.headers),body:p,failedResponseHandler:gX,successfulResponseHandler:dJ(gs({id:fT(),created_at:gd(),error:gs({code:fT(),message:fT()}).nullish(),model:fT(),output:gq(gy("type",[gs({type:gF("message"),role:gF("assistant"),id:fT(),content:gq(gs({type:gF("output_text"),text:fT(),logprobs:hx.nullish(),annotations:gq(gy("type",[gs({type:gF("url_citation"),start_index:gd(),end_index:gd(),url:fT(),title:fT()}),gs({type:gF("file_citation"),start_index:gd(),end_index:gd(),file_id:fT(),quote:fT()})]))}))}),gs({type:gF("function_call"),call_id:fT(),name:fT(),arguments:fT(),id:fT()}),hw,gs({type:gF("computer_call"),id:fT(),status:fT().optional()}),gs({type:gF("file_search_call"),id:fT(),status:fT().optional(),queries:gq(fT()).nullish(),results:gq(gs({attributes:gs({file_id:fT(),filename:fT(),score:gd(),text:fT()})})).nullish()}),gs({type:gF("reasoning"),id:fT(),encrypted_content:fT().nullish(),summary:gq(gs({type:gF("summary_text"),text:fT()}))})])),incomplete_details:gs({reason:fT()}).nullable(),usage:hz})),abortSignal:a.abortSignal,fetch:this.config.fetch});if(t.error)throw new aq({message:t.error.message,url:r,requestBodyValues:p,statusCode:400,responseHeaders:s,responseBody:u,isRetryable:!1});let v=[],w=[];for(let k of t.output)switch(k.type){case"reasoning":for(let a of(0===k.summary.length&&k.summary.push({type:"summary_text",text:""}),k.summary))v.push({type:"reasoning",text:a.text,providerMetadata:{openai:{itemId:k.id,reasoningEncryptedContent:null!=(b=k.encrypted_content)?b:null}}});break;case"message":for(let b of k.content)for(let l of((null==(d=null==(c=a.providerOptions)?void 0:c.openai)?void 0:d.logprobs)&&b.logprobs&&w.push(b.logprobs),v.push({type:"text",text:b.text,providerMetadata:{openai:{itemId:k.id}}}),b.annotations))"url_citation"===l.type?v.push({type:"source",sourceType:"url",id:null!=(g=null==(f=(e=this.config).generateId)?void 0:f.call(e))?g:dh(),url:l.url,title:l.title}):"file_citation"===l.type&&v.push({type:"source",sourceType:"document",id:null!=(j=null==(i=(h=this.config).generateId)?void 0:i.call(h))?j:dh(),mediaType:"text/plain",title:l.quote,filename:l.file_id});break;case"function_call":v.push({type:"tool-call",toolCallId:k.call_id,toolName:k.name,input:k.arguments,providerMetadata:{openai:{itemId:k.id}}});break;case"web_search_call":v.push({type:"tool-call",toolCallId:k.id,toolName:"web_search_preview",input:JSON.stringify({action:k.action}),providerExecuted:!0}),v.push({type:"tool-result",toolCallId:k.id,toolName:"web_search_preview",result:{status:k.status},providerExecuted:!0});break;case"computer_call":v.push({type:"tool-call",toolCallId:k.id,toolName:"computer_use",input:"",providerExecuted:!0}),v.push({type:"tool-result",toolCallId:k.id,toolName:"computer_use",result:{type:"computer_use_tool_result",status:k.status||"completed"},providerExecuted:!0});break;case"file_search_call":v.push({type:"tool-call",toolCallId:k.id,toolName:"file_search",input:"",providerExecuted:!0}),v.push({type:"tool-result",toolCallId:k.id,toolName:"file_search",result:{type:"file_search_tool_result",status:k.status||"completed",...k.queries&&{queries:k.queries},...k.results&&{results:k.results}},providerExecuted:!0})}let x={openai:{responseId:t.id}};return w.length>0&&(x.openai.logprobs=w),{content:v,finishReason:hv({finishReason:null==(k=t.incomplete_details)?void 0:k.reason,hasToolCalls:v.some(a=>"tool-call"===a.type)}),usage:{inputTokens:t.usage.input_tokens,outputTokens:t.usage.output_tokens,totalTokens:t.usage.input_tokens+t.usage.output_tokens,reasoningTokens:null!=(m=null==(l=t.usage.output_tokens_details)?void 0:l.reasoning_tokens)?m:void 0,cachedInputTokens:null!=(o=null==(n=t.usage.input_tokens_details)?void 0:n.cached_tokens)?o:void 0},request:{body:p},response:{id:t.id,timestamp:new Date(1e3*t.created_at),modelId:t.model,headers:s,body:u},providerMetadata:x,warnings:q}}async doStream(a){let{args:b,warnings:c}=await this.getArgs(a),{responseHeaders:d,value:e}=await dC({url:this.config.url({path:"/responses",modelId:this.modelId}),headers:dc(this.config.headers(),a.headers),body:{...b,stream:!0},failedResponseHandler:gX,successfulResponseHandler:dI(hC),abortSignal:a.abortSignal,fetch:this.config.fetch}),f=this,g="unknown",h={inputTokens:void 0,outputTokens:void 0,totalTokens:void 0},i=[],j=null,k={},l=!1,m={};return{stream:e.pipeThrough(new TransformStream({start(a){a.enqueue({type:"stream-start",warnings:c})},transform(b,c){var d,e,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D;if(a.includeRawChunks&&c.enqueue({type:"raw",rawValue:b.rawValue}),!b.success){g="error",c.enqueue({type:"error",error:b.error});return}let E=b.value;if(hE(E)){"function_call"===E.item.type?(k[E.output_index]={toolName:E.item.name,toolCallId:E.item.call_id},c.enqueue({type:"tool-input-start",id:E.item.call_id,toolName:E.item.name})):"web_search_call"===E.item.type?(k[E.output_index]={toolName:"web_search_preview",toolCallId:E.item.id},c.enqueue({type:"tool-input-start",id:E.item.id,toolName:"web_search_preview"})):"computer_call"===E.item.type?(k[E.output_index]={toolName:"computer_use",toolCallId:E.item.id},c.enqueue({type:"tool-input-start",id:E.item.id,toolName:"computer_use"})):"file_search_call"===E.item.type?(k[E.output_index]={toolName:"file_search",toolCallId:E.item.id},c.enqueue({type:"tool-input-start",id:E.item.id,toolName:"file_search"})):"message"===E.item.type?c.enqueue({type:"text-start",id:E.item.id,providerMetadata:{openai:{itemId:E.item.id}}}):hE(B=E)&&"reasoning"===B.item.type&&(m[E.item.id]={encryptedContent:E.item.encrypted_content,summaryParts:[0]},c.enqueue({type:"reasoning-start",id:`${E.item.id}:0`,providerMetadata:{openai:{itemId:E.item.id,reasoningEncryptedContent:null!=(d=E.item.encrypted_content)?d:null}}}))}else if(hD(E))if("function_call"===E.item.type)k[E.output_index]=void 0,l=!0,c.enqueue({type:"tool-input-end",id:E.item.call_id}),c.enqueue({type:"tool-call",toolCallId:E.item.call_id,toolName:E.item.name,input:E.item.arguments,providerMetadata:{openai:{itemId:E.item.id}}});else if("web_search_call"===E.item.type)k[E.output_index]=void 0,l=!0,c.enqueue({type:"tool-input-end",id:E.item.id}),c.enqueue({type:"tool-call",toolCallId:E.item.id,toolName:"web_search_preview",input:JSON.stringify({action:E.item.action}),providerExecuted:!0}),c.enqueue({type:"tool-result",toolCallId:E.item.id,toolName:"web_search_preview",result:{status:E.item.status},providerExecuted:!0});else if("computer_call"===E.item.type)k[E.output_index]=void 0,l=!0,c.enqueue({type:"tool-input-end",id:E.item.id}),c.enqueue({type:"tool-call",toolCallId:E.item.id,toolName:"computer_use",input:"",providerExecuted:!0}),c.enqueue({type:"tool-result",toolCallId:E.item.id,toolName:"computer_use",result:{type:"computer_use_tool_result",status:E.item.status||"completed"},providerExecuted:!0});else if("file_search_call"===E.item.type)k[E.output_index]=void 0,l=!0,c.enqueue({type:"tool-input-end",id:E.item.id}),c.enqueue({type:"tool-call",toolCallId:E.item.id,toolName:"file_search",input:"",providerExecuted:!0}),c.enqueue({type:"tool-result",toolCallId:E.item.id,toolName:"file_search",result:{type:"file_search_tool_result",status:E.item.status||"completed",...E.item.queries&&{queries:E.item.queries},...E.item.results&&{results:E.item.results}},providerExecuted:!0});else if("message"===E.item.type)c.enqueue({type:"text-end",id:E.item.id});else{if(hD(C=E)&&"reasoning"===C.item.type){for(let a of m[E.item.id].summaryParts)c.enqueue({type:"reasoning-end",id:`${E.item.id}:${a}`,providerMetadata:{openai:{itemId:E.item.id,reasoningEncryptedContent:null!=(e=E.item.encrypted_content)?e:null}}});delete m[E.item.id]}}else if("response.function_call_arguments.delta"===E.type){let a=k[E.output_index];null!=a&&c.enqueue({type:"tool-input-delta",id:a.toolCallId,delta:E.delta})}else{"response.created"===E.type?(j=E.response.id,c.enqueue({type:"response-metadata",id:E.response.id,timestamp:new Date(1e3*E.response.created_at),modelId:E.response.model})):"response.output_text.delta"===E.type?(c.enqueue({type:"text-delta",id:E.item_id,delta:E.delta}),E.logprobs&&i.push(E.logprobs)):"response.reasoning_summary_part.added"===E.type?E.summary_index>0&&(null==(n=m[E.item_id])||n.summaryParts.push(E.summary_index),c.enqueue({type:"reasoning-start",id:`${E.item_id}:${E.summary_index}`,providerMetadata:{openai:{itemId:E.item_id,reasoningEncryptedContent:null!=(p=null==(o=m[E.item_id])?void 0:o.encryptedContent)?p:null}}})):"response.reasoning_summary_text.delta"===E.type?c.enqueue({type:"reasoning-delta",id:`${E.item_id}:${E.summary_index}`,delta:E.delta,providerMetadata:{openai:{itemId:E.item_id}}}):"response.completed"===(D=E).type||"response.incomplete"===D.type?(g=hv({finishReason:null==(q=E.response.incomplete_details)?void 0:q.reason,hasToolCalls:l}),h.inputTokens=E.response.usage.input_tokens,h.outputTokens=E.response.usage.output_tokens,h.totalTokens=E.response.usage.input_tokens+E.response.usage.output_tokens,h.reasoningTokens=null!=(s=null==(r=E.response.usage.output_tokens_details)?void 0:r.reasoning_tokens)?s:void 0,h.cachedInputTokens=null!=(u=null==(t=E.response.usage.input_tokens_details)?void 0:t.cached_tokens)?u:void 0):"response.output_text.annotation.added"===E.type?"url_citation"===E.annotation.type?c.enqueue({type:"source",sourceType:"url",id:null!=(x=null==(w=(v=f.config).generateId)?void 0:w.call(v))?x:dh(),url:E.annotation.url,title:E.annotation.title}):"file_citation"===E.annotation.type&&c.enqueue({type:"source",sourceType:"document",id:null!=(A=null==(z=(y=f.config).generateId)?void 0:z.call(y))?A:dh(),mediaType:"text/plain",title:E.annotation.quote,filename:E.annotation.file_id}):"error"===E.type&&c.enqueue({type:"error",error:E})}},flush(a){let b={openai:{responseId:j}};i.length>0&&(b.openai.logprobs=i),a.enqueue({type:"finish",finishReason:g,usage:h,providerMetadata:b})}})),request:{body:b},response:{headers:d}}}},hz=gs({input_tokens:gd(),input_tokens_details:gs({cached_tokens:gd().nullish()}).nullish(),output_tokens:gd(),output_tokens_details:gs({reasoning_tokens:gd().nullish()}).nullish()}),hA=gs({type:gF("response.output_text.delta"),item_id:fT(),delta:fT(),logprobs:hx.nullish()}),hB=gs({type:gF("error"),code:fT(),message:fT(),param:fT().nullish(),sequence_number:gd()}),hC=gw([hA,gs({type:gD(["response.completed","response.incomplete"]),response:gs({incomplete_details:gs({reason:fT()}).nullish(),usage:hz})}),gs({type:gF("response.created"),response:gs({id:fT(),created_at:gd(),model:fT()})}),gs({type:gF("response.output_item.added"),output_index:gd(),item:gy("type",[gs({type:gF("message"),id:fT()}),gs({type:gF("reasoning"),id:fT(),encrypted_content:fT().nullish()}),gs({type:gF("function_call"),id:fT(),call_id:fT(),name:fT(),arguments:fT()}),gs({type:gF("web_search_call"),id:fT(),status:fT(),action:gs({type:gF("search"),query:fT().optional()}).nullish()}),gs({type:gF("computer_call"),id:fT(),status:fT()}),gs({type:gF("file_search_call"),id:fT(),status:fT(),queries:gq(fT()).nullish(),results:gq(gs({attributes:gs({file_id:fT(),filename:fT(),score:gd(),text:fT()})})).optional()})])}),gs({type:gF("response.output_item.done"),output_index:gd(),item:gy("type",[gs({type:gF("message"),id:fT()}),gs({type:gF("reasoning"),id:fT(),encrypted_content:fT().nullish()}),gs({type:gF("function_call"),id:fT(),call_id:fT(),name:fT(),arguments:fT(),status:gF("completed")}),hw,gs({type:gF("computer_call"),id:fT(),status:gF("completed")}),gs({type:gF("file_search_call"),id:fT(),status:gF("completed"),queries:gq(fT()).nullish(),results:gq(gs({attributes:gs({file_id:fT(),filename:fT(),score:gd(),text:fT()})})).nullish()})])}),gs({type:gF("response.function_call_arguments.delta"),item_id:fT(),output_index:gd(),delta:fT()}),gs({type:gF("response.output_text.annotation.added"),annotation:gy("type",[gs({type:gF("url_citation"),url:fT(),title:fT()}),gs({type:gF("file_citation"),file_id:fT(),quote:fT()})])}),gs({type:gF("response.reasoning_summary_part.added"),item_id:fT(),summary_index:gd()}),gs({type:gF("response.reasoning_summary_text.delta"),item_id:fT(),summary_index:gd(),delta:fT()}),hB,gs({type:fT()}).loose()]);function hD(a){return"response.output_item.done"===a.type}function hE(a){return"response.output_item.added"===a.type}var hF=gs({metadata:gk().nullish(),parallelToolCalls:gh().nullish(),previousResponseId:fT().nullish(),store:gh().nullish(),user:fT().nullish(),reasoningEffort:fT().nullish(),strictJsonSchema:gh().nullish(),instructions:fT().nullish(),reasoningSummary:fT().nullish(),serviceTier:gD(["auto","flex","priority"]).nullish(),include:gq(gD(["reasoning.encrypted_content","file_search_call.results","message.output_text.logprobs"])).nullish(),textVerbosity:gD(["low","medium","high"]).nullish(),promptCacheKey:fT().nullish(),safetyIdentifier:fT().nullish(),logprobs:gw([gh(),gd().min(1).max(20)]).optional()}),hG=gs({instructions:fT().nullish(),speed:gd().min(.25).max(4).default(1).nullish()}),hH=class{constructor(a,b){this.modelId=a,this.config=b,this.specificationVersion="v2"}get provider(){return this.config.provider}async getArgs({text:a,voice:b="alloy",outputFormat:c="mp3",speed:d,instructions:e,language:f,providerOptions:g}){let h=[],i=await dA({provider:"openai",providerOptions:g,schema:hG}),j={model:this.modelId,input:a,voice:b,response_format:"mp3",speed:d,instructions:e};if(c&&(["mp3","opus","aac","flac","wav","pcm"].includes(c)?j.response_format=c:h.push({type:"unsupported-setting",setting:"outputFormat",details:`Unsupported output format: ${c}. Using mp3 instead.`})),i){let a={};for(let b in a){let c=a[b];void 0!==c&&(j[b]=c)}}return f&&h.push({type:"unsupported-setting",setting:"language",details:`OpenAI speech models do not support language selection. Language parameter "${f}" was ignored.`}),{requestBody:j,warnings:h}}async doGenerate(a){var b,c,d;let e=null!=(d=null==(c=null==(b=this.config._internal)?void 0:b.currentDate)?void 0:c.call(b))?d:new Date,{requestBody:f,warnings:g}=await this.getArgs(a),{value:h,responseHeaders:i,rawValue:j}=await dC({url:this.config.url({path:"/audio/speech",modelId:this.modelId}),headers:dc(this.config.headers(),a.headers),body:f,failedResponseHandler:gX,successfulResponseHandler:async({response:a,url:b,requestBodyValues:c})=>{let d=df(a);if(!a.body)throw new aq({message:"Response body is empty",url:b,requestBodyValues:c,statusCode:a.status,responseHeaders:d,responseBody:void 0});try{let b=await a.arrayBuffer();return{responseHeaders:d,value:new Uint8Array(b)}}catch(e){throw new aq({message:"Failed to read response as array buffer",url:b,requestBodyValues:c,statusCode:a.status,responseHeaders:d,responseBody:void 0,cause:e})}},abortSignal:a.abortSignal,fetch:this.config.fetch});return{audio:h,warnings:g,request:{body:JSON.stringify(f)},response:{timestamp:e,modelId:this.modelId,headers:i,body:j}}}},hI=gs({include:gq(fT()).optional(),language:fT().optional(),prompt:fT().optional(),temperature:gd().min(0).max(1).default(0).optional(),timestampGranularities:gq(gD(["word","segment"])).default(["segment"]).optional()}),hJ={afrikaans:"af",arabic:"ar",armenian:"hy",azerbaijani:"az",belarusian:"be",bosnian:"bs",bulgarian:"bg",catalan:"ca",chinese:"zh",croatian:"hr",czech:"cs",danish:"da",dutch:"nl",english:"en",estonian:"et",finnish:"fi",french:"fr",galician:"gl",german:"de",greek:"el",hebrew:"he",hindi:"hi",hungarian:"hu",icelandic:"is",indonesian:"id",italian:"it",japanese:"ja",kannada:"kn",kazakh:"kk",korean:"ko",latvian:"lv",lithuanian:"lt",macedonian:"mk",malay:"ms",marathi:"mr",maori:"mi",nepali:"ne",norwegian:"no",persian:"fa",polish:"pl",portuguese:"pt",romanian:"ro",russian:"ru",serbian:"sr",slovak:"sk",slovenian:"sl",spanish:"es",swahili:"sw",swedish:"sv",tagalog:"tl",tamil:"ta",thai:"th",turkish:"tr",ukrainian:"uk",urdu:"ur",vietnamese:"vi",welsh:"cy"},hK=class{constructor(a,b){this.modelId=a,this.config=b,this.specificationVersion="v2"}get provider(){return this.config.provider}async getArgs({audio:a,mediaType:b,providerOptions:c}){let d=await dA({provider:"openai",providerOptions:c,schema:hI}),e=new FormData,f=a instanceof Uint8Array?new Blob([a]):new Blob([dP(a)]);if(e.append("model",this.modelId),e.append("file",new File([f],"audio",{type:b})),d)for(let[a,b]of Object.entries({include:d.include,language:d.language,prompt:d.prompt,response_format:"verbose_json",temperature:d.temperature,timestamp_granularities:d.timestampGranularities}))null!=b&&e.append(a,String(b));return{formData:e,warnings:[]}}async doGenerate(a){var b,c,d,e,f,g,h,i;let j=null!=(d=null==(c=null==(b=this.config._internal)?void 0:b.currentDate)?void 0:c.call(b))?d:new Date,{formData:k,warnings:l}=await this.getArgs(a),{value:m,responseHeaders:n,rawValue:o}=await dD({url:this.config.url({path:"/audio/transcriptions",modelId:this.modelId}),headers:dc(this.config.headers(),a.headers),formData:k,failedResponseHandler:gX,successfulResponseHandler:dJ(hL),abortSignal:a.abortSignal,fetch:this.config.fetch}),p=null!=m.language&&m.language in hJ?hJ[m.language]:void 0;return{text:m.text,segments:null!=(h=null!=(g=null==(e=m.segments)?void 0:e.map(a=>({text:a.text,startSecond:a.start,endSecond:a.end})))?g:null==(f=m.words)?void 0:f.map(a=>({text:a.word,startSecond:a.start,endSecond:a.end})))?h:[],language:p,durationInSeconds:null!=(i=m.duration)?i:void 0,warnings:l,response:{timestamp:j,modelId:this.modelId,headers:n,body:o}}}},hL=gs({text:fT(),language:fT().nullish(),duration:gd().nullish(),words:gq(gs({word:fT(),start:gd(),end:gd()})).nullish(),segments:gq(gs({id:gd(),seek:gd(),start:gd(),end:gd(),text:fT(),tokens:gq(gd()),temperature:gd(),avg_logprob:gd(),compression_ratio:gd(),no_speech_prob:gd()})).nullish()}),hM=function(a={}){var b,c;let d=null!=(b=dS(a.baseURL))?b:"https://api.openai.com/v1",e=null!=(c=a.name)?c:"openai",f=()=>({Authorization:`Bearer ${function({apiKey:a,environmentVariableName:b,apiKeyParameterName:c="apiKey",description:d}){if("string"==typeof a)return a;if(null!=a)throw new aP({message:`${d} API key must be a string.`});if("undefined"==typeof process)throw new aP({message:`${d} API key is missing. Pass it using the '${c}' parameter. Environment variables is not supported in this environment.`});if(null==(a=process.env[b]))throw new aP({message:`${d} API key is missing. Pass it using the '${c}' parameter or the ${b} environment variable.`});if("string"!=typeof a)throw new aP({message:`${d} API key must be a string. The value of the ${b} environment variable is not a string.`});return a}({apiKey:a.apiKey,environmentVariableName:"OPENAI_API_KEY",description:"OpenAI"})}`,"OpenAI-Organization":a.organization,"OpenAI-Project":a.project,...a.headers}),g=b=>new hk(b,{provider:`${e}.embedding`,url:({path:a})=>`${d}${a}`,headers:f,fetch:a.fetch}),h=b=>new ho(b,{provider:`${e}.image`,url:({path:a})=>`${d}${a}`,headers:f,fetch:a.fetch}),i=b=>new hK(b,{provider:`${e}.transcription`,url:({path:a})=>`${d}${a}`,headers:f,fetch:a.fetch}),j=b=>new hH(b,{provider:`${e}.speech`,url:({path:a})=>`${d}${a}`,headers:f,fetch:a.fetch}),k=a=>{if(new.target)throw Error("The OpenAI model function cannot be called with the new keyword.");return l(a)},l=b=>new hy(b,{provider:`${e}.responses`,url:({path:a})=>`${d}${a}`,headers:f,fetch:a.fetch,fileIdPrefixes:["file-"]}),m=function(a){return k(a)};return m.languageModel=k,m.chat=b=>new g6(b,{provider:`${e}.chat`,url:({path:a})=>`${d}${a}`,headers:f,fetch:a.fetch}),m.completion=b=>new hf(b,{provider:`${e}.completion`,url:({path:a})=>`${d}${a}`,headers:f,fetch:a.fetch}),m.responses=l,m.embedding=g,m.textEmbedding=g,m.textEmbeddingModel=g,m.image=h,m.imageModel=h,m.transcription=i,m.transcriptionModel=i,m.speech=j,m.speechModel=j,m.tools=hr,m}(),hN=Symbol.for("vercel.ai.gateway.error"),hO=class a extends(w=Error,v=hN,w){constructor({message:a,statusCode:b=500,cause:c}){super(a),this[v]=!0,this.statusCode=b,this.cause=c}static isInstance(b){return a.hasMarker(b)}static hasMarker(a){return"object"==typeof a&&null!==a&&hN in a&&!0===a[hN]}},hP="GatewayAuthenticationError",hQ=Symbol.for(`vercel.ai.gateway.error.${hP}`),hR=class a extends(y=hO,x=hQ,y){constructor({message:a="Authentication failed",statusCode:b=401,cause:c}={}){super({message:a,statusCode:b,cause:c}),this[x]=!0,this.name=hP,this.type="authentication_error"}static isInstance(a){return hO.hasMarker(a)&&hQ in a}static createContextualError({apiKeyProvided:b,oidcTokenProvided:c,message:d="Authentication failed",statusCode:e=401,cause:f}){return new a({message:b?`AI Gateway authentication failed: Invalid API key provided.

The token is expected to be provided via the 'apiKey' option or 'AI_GATEWAY_API_KEY' environment variable.`:c?`AI Gateway authentication failed: Invalid OIDC token provided.

The token is expected to be provided via the 'VERCEL_OIDC_TOKEN' environment variable. It expires every 12 hours.
- make sure your Vercel project settings have OIDC enabled
- if running locally with 'vercel dev', the token is automatically obtained and refreshed
- if running locally with your own dev server, run 'vercel env pull' to fetch the token
- in production/preview, the token is automatically obtained and refreshed

Alternative: Provide an API key via 'apiKey' option or 'AI_GATEWAY_API_KEY' environment variable.`:`AI Gateway authentication failed: No authentication provided.

Provide either an API key or OIDC token.

API key instructions:

The token is expected to be provided via the 'apiKey' option or 'AI_GATEWAY_API_KEY' environment variable.

OIDC token instructions:

The token is expected to be provided via the 'VERCEL_OIDC_TOKEN' environment variable. It expires every 12 hours.
- make sure your Vercel project settings have OIDC enabled
- if running locally with 'vercel dev', the token is automatically obtained and refreshed
- if running locally with your own dev server, run 'vercel env pull' to fetch the token
- in production/preview, the token is automatically obtained and refreshed`,statusCode:e,cause:f})}},hS="GatewayInvalidRequestError",hT=Symbol.for(`vercel.ai.gateway.error.${hS}`),hU=class extends(A=hO,z=hT,A){constructor({message:a="Invalid request",statusCode:b=400,cause:c}={}){super({message:a,statusCode:b,cause:c}),this[z]=!0,this.name=hS,this.type="invalid_request_error"}static isInstance(a){return hO.hasMarker(a)&&hT in a}},hV="GatewayRateLimitError",hW=Symbol.for(`vercel.ai.gateway.error.${hV}`),hX=class extends(C=hO,B=hW,C){constructor({message:a="Rate limit exceeded",statusCode:b=429,cause:c}={}){super({message:a,statusCode:b,cause:c}),this[B]=!0,this.name=hV,this.type="rate_limit_exceeded"}static isInstance(a){return hO.hasMarker(a)&&hW in a}},hY="GatewayModelNotFoundError",hZ=Symbol.for(`vercel.ai.gateway.error.${hY}`),h$=gs({modelId:fT()}),h_=class extends(E=hO,D=hZ,E){constructor({message:a="Model not found",statusCode:b=404,modelId:c,cause:d}={}){super({message:a,statusCode:b,cause:d}),this[D]=!0,this.name=hY,this.type="model_not_found",this.modelId=c}static isInstance(a){return hO.hasMarker(a)&&hZ in a}},h0="GatewayInternalServerError",h1=Symbol.for(`vercel.ai.gateway.error.${h0}`),h2=class extends(G=hO,F=h1,G){constructor({message:a="Internal server error",statusCode:b=500,cause:c}={}){super({message:a,statusCode:b,cause:c}),this[F]=!0,this.name=h0,this.type="internal_server_error"}static isInstance(a){return hO.hasMarker(a)&&h1 in a}},h3="GatewayResponseError",h4=Symbol.for(`vercel.ai.gateway.error.${h3}`),h5=class extends(I=hO,H=h4,I){constructor({message:a="Invalid response from Gateway",statusCode:b=502,response:c,validationError:d,cause:e}={}){super({message:a,statusCode:b,cause:e}),this[H]=!0,this.name=h3,this.type="response_error",this.response=c,this.validationError=d}static isInstance(a){return hO.hasMarker(a)&&h4 in a}};function h6({response:a,statusCode:b,defaultMessage:c="Gateway request failed",cause:d,authMethod:e}){let f=h7.safeParse(a);if(!f.success)return new h5({message:`Invalid error response format: ${c}`,statusCode:b,response:a,validationError:f.error,cause:d});let g=f.data,h=g.error.type,i=g.error.message;switch(h){case"authentication_error":return hR.createContextualError({apiKeyProvided:"api-key"===e,oidcTokenProvided:"oidc"===e,statusCode:b,cause:d});case"invalid_request_error":return new hU({message:i,statusCode:b,cause:d});case"rate_limit_exceeded":return new hX({message:i,statusCode:b,cause:d});case"model_not_found":{let a=h$.safeParse(g.error.param);return new h_({message:i,statusCode:b,modelId:a.success?a.data.modelId:void 0,cause:d})}default:return new h2({message:i,statusCode:b,cause:d})}}var h7=gs({error:gs({message:fT(),type:fT().nullish(),param:gm().nullish(),code:gw([fT(),gd()]).nullish()})});function h8(a,b){var c;return hO.isInstance(a)?a:aq.isInstance(a)?h6({response:function(a){if(void 0!==a.data)return a.data;if(null!=a.responseBody)try{return JSON.parse(a.responseBody)}catch(b){return a.responseBody}return{}}(a),statusCode:null!=(c=a.statusCode)?c:500,defaultMessage:"Gateway request failed",cause:a,authMethod:b}):h6({response:{},statusCode:500,defaultMessage:a instanceof Error?`Gateway request failed: ${a.message}`:"Unknown Gateway error",cause:a,authMethod:b})}var h9="ai-gateway-auth-method";function ia(a){let b=ib.safeParse(a[h9]);return b.success?b.data:void 0}var ib=gw([gF("api-key"),gF("oidc")]),ic=class{constructor(a){this.config=a}async getAvailableModels(){try{let{value:a}=await dp({url:`${this.config.baseURL}/config`,headers:await dG(this.config.headers()),successfulResponseHandler:dJ(ih),failedResponseHandler:dH({errorSchema:gk(),errorToMessage:a=>a}),fetch:this.config.fetch});return a}catch(a){throw h8(a)}}},id=gs({specificationVersion:gF("v2"),provider:fT(),modelId:fT()}),ie=gs({input:fT(),output:fT()}),ig=gs({id:fT(),name:fT(),description:fT().nullish(),pricing:ie.nullish(),specification:id}),ih=gs({models:gq(ig)}),ii=class{constructor(a,b){this.modelId=a,this.config=b,this.specificationVersion="v2",this.supportedUrls={"*/*":[/.*/]}}get provider(){return this.config.provider}async getArgs(a){let{abortSignal:b,...c}=a;return{args:this.maybeEncodeFileParts(c),warnings:[]}}async doGenerate(a){let{args:b,warnings:c}=await this.getArgs(a),{abortSignal:d}=a,e=await dG(this.config.headers());try{let{responseHeaders:f,value:g,rawValue:h}=await dC({url:this.getUrl(),headers:dc(e,a.headers,this.getModelConfigHeaders(this.modelId,!1),await dG(this.config.o11yHeaders)),body:b,successfulResponseHandler:dJ(gk()),failedResponseHandler:dH({errorSchema:gk(),errorToMessage:a=>a}),...d&&{abortSignal:d},fetch:this.config.fetch});return{...g,request:{body:b},response:{headers:f,body:h},warnings:c}}catch(a){throw h8(a,ia(e))}}async doStream(a){let{args:b,warnings:c}=await this.getArgs(a),{abortSignal:d}=a,e=await dG(this.config.headers());try{let{value:f,responseHeaders:g}=await dC({url:this.getUrl(),headers:dc(e,a.headers,this.getModelConfigHeaders(this.modelId,!0),await dG(this.config.o11yHeaders)),body:b,successfulResponseHandler:dI(gk()),failedResponseHandler:dH({errorSchema:gk(),errorToMessage:a=>a}),...d&&{abortSignal:d},fetch:this.config.fetch});return{stream:f.pipeThrough(new TransformStream({start(a){c.length>0&&a.enqueue({type:"stream-start",warnings:c})},transform(b,c){if(b.success){let d=b.value;("raw"!==d.type||a.includeRawChunks)&&("response-metadata"===d.type&&d.timestamp&&"string"==typeof d.timestamp&&(d.timestamp=new Date(d.timestamp)),c.enqueue(d))}else c.error(b.error)}})),request:{body:b},response:{headers:g}}}catch(a){throw h8(a,ia(e))}}isFilePart(a){return a&&"object"==typeof a&&"type"in a&&"file"===a.type}maybeEncodeFileParts(a){for(let b of a.prompt)for(let a of b.content)if(this.isFilePart(a)&&a.data instanceof Uint8Array){let b=Uint8Array.from(a.data),c=Buffer.from(b).toString("base64");a.data=new URL(`data:${a.mediaType||"application/octet-stream"};base64,${c}`)}return a}getUrl(){return`${this.config.baseURL}/language-model`}getModelConfigHeaders(a,b){return{"ai-language-model-specification-version":"2","ai-language-model-id":a,"ai-language-model-streaming":String(b)}}},ij=class{constructor(a,b){this.modelId=a,this.config=b,this.specificationVersion="v2",this.maxEmbeddingsPerCall=2048,this.supportsParallelCalls=!0}get provider(){return this.config.provider}async doEmbed({values:a,headers:b,abortSignal:c,providerOptions:d}){var e;let f=await dG(this.config.headers());try{let{responseHeaders:g,value:h,rawValue:i}=await dC({url:this.getUrl(),headers:dc(f,null!=b?b:{},this.getModelConfigHeaders(),await dG(this.config.o11yHeaders)),body:{input:1===a.length?a[0]:a,...null!=d?d:{}},successfulResponseHandler:dJ(ik),failedResponseHandler:dH({errorSchema:gk(),errorToMessage:a=>a}),...c&&{abortSignal:c},fetch:this.config.fetch});return{embeddings:h.embeddings,usage:null!=(e=h.usage)?e:void 0,providerMetadata:h.providerMetadata,response:{headers:g,body:i}}}catch(a){throw h8(a,ia(f))}}getUrl(){return`${this.config.baseURL}/embedding-model`}getModelConfigHeaders(){return{"ai-embedding-model-specification-version":"2","ai-model-id":this.modelId}}},ik=gs({embeddings:gq(gq(gd())),usage:gs({tokens:gd()}).nullish(),providerMetadata:gB(fT(),gB(fT(),gm())).optional()});async function il(){var a,b;let c=null!=(b=null==(a=ip().headers)?void 0:a["x-vercel-oidc-token"])?b:process.env.VERCEL_OIDC_TOKEN;if(!c)throw new hR({message:"OIDC token not available",statusCode:401});return c}async function im(){var a;return null==(a=ip().headers)?void 0:a["x-vercel-id"]}var io=Symbol.for("@vercel/request-context");function ip(){var a,b,c;return null!=(c=null==(b=null==(a=globalThis[io])?void 0:a.get)?void 0:b.call(a))?c:{}}var iq=function(a={}){var b,c;let d=null,e=null,f=null!=(b=a.metadataCacheRefreshMillis)?b:3e5,g=0,h=null!=(c=dS(a.baseURL))?c:"https://ai-gateway.vercel.sh/v1/ai",i=async()=>{let b=await ir(a);if(b)return{Authorization:`Bearer ${b.token}`,"ai-gateway-protocol-version":"0.0.1",[h9]:b.authMethod,...a.headers};throw hR.createContextualError({apiKeyProvided:!1,oidcTokenProvided:!1,statusCode:401})},j=()=>{let a=dq({settingValue:void 0,environmentVariableName:"VERCEL_DEPLOYMENT_ID"}),b=dq({settingValue:void 0,environmentVariableName:"VERCEL_ENV"}),c=dq({settingValue:void 0,environmentVariableName:"VERCEL_REGION"});return async()=>{let d=await im();return{...a&&{"ai-o11y-deployment-id":a},...b&&{"ai-o11y-environment":b},...c&&{"ai-o11y-region":c},...d&&{"ai-o11y-request-id":d}}}},k=b=>new ii(b,{provider:"gateway",baseURL:h,headers:i,fetch:a.fetch,o11yHeaders:j()}),l=function(a){if(new.target)throw Error("The Gateway Provider model function cannot be called with the new keyword.");return k(a)};return l.getAvailableModels=async()=>{var b,c,j;let k=null!=(j=null==(c=null==(b=a._internal)?void 0:b.currentDate)?void 0:c.call(b).getTime())?j:Date.now();return(!d||k-g>f)&&(g=k,d=new ic({baseURL:h,headers:i,fetch:a.fetch}).getAvailableModels().then(a=>(e=a,a)).catch(async a=>{throw h8(a,ia(await i()))})),e?Promise.resolve(e):d},l.imageModel=a=>{throw new aT({modelId:a,modelType:"imageModel"})},l.languageModel=k,l.textEmbeddingModel=b=>new ij(b,{provider:"gateway",baseURL:h,headers:i,fetch:a.fetch,o11yHeaders:j()}),l}();async function ir(a){let b=dq({settingValue:a.apiKey,environmentVariableName:"AI_GATEWAY_API_KEY"});if(b)return{token:b,authMethod:"api-key"};try{return{token:await il(),authMethod:"oidc"}}catch(a){return null}}var is=c(6895),it=c(9131),iu=Object.defineProperty,iv="AI_NoOutputSpecifiedError",iw=`vercel.ai.error.${iv}`,ix=Symbol.for(iw),iy=class extends am{constructor({message:a="No output specified."}={}){super({name:iv,message:a}),this[J]=!0}static isInstance(a){return am.hasMarker(a,iw)}};J=ix;var iz="AI_InvalidArgumentError",iA=`vercel.ai.error.${iz}`,iB=Symbol.for(iA),iC=class extends am{constructor({parameter:a,value:b,message:c}){super({name:iz,message:`Invalid argument for parameter ${a}: ${c}`}),this[K]=!0,this.parameter=a,this.value=b}static isInstance(a){return am.hasMarker(a,iA)}};K=iB,Symbol.for("vercel.ai.error.AI_InvalidStreamPartError");var iD="AI_InvalidToolInputError",iE=`vercel.ai.error.${iD}`,iF=Symbol.for(iE),iG=class extends am{constructor({toolInput:a,toolName:b,cause:c,message:d=`Invalid input for tool ${b}: ${av(c)}`}){super({name:iD,message:d,cause:c}),this[L]=!0,this.toolInput=a,this.toolName=b}static isInstance(a){return am.hasMarker(a,iE)}};L=iF,Symbol.for("vercel.ai.error.AI_MCPClientError"),Symbol.for("vercel.ai.error.AI_NoImageGeneratedError");var iH="AI_NoObjectGeneratedError",iI=`vercel.ai.error.${iH}`,iJ=Symbol.for(iI),iK=class extends am{constructor({message:a="No object generated.",cause:b,text:c,response:d,usage:e,finishReason:f}){super({name:iH,message:a,cause:b}),this[M]=!0,this.text=c,this.response=d,this.usage=e,this.finishReason=f}static isInstance(a){return am.hasMarker(a,iI)}};M=iJ;var iL="AI_NoOutputGeneratedError",iM=`vercel.ai.error.${iL}`,iN=Symbol.for(iM),iO=class extends am{constructor({message:a="No output generated.",cause:b}={}){super({name:iL,message:a,cause:b}),this[N]=!0}static isInstance(a){return am.hasMarker(a,iM)}};N=iN;var iP="AI_NoSuchToolError",iQ=`vercel.ai.error.${iP}`,iR=Symbol.for(iQ),iS=class extends am{constructor({toolName:a,availableTools:b,message:c=`Model tried to call unavailable tool '${a}'. ${void 0===b?"No tools are available.":`Available tools: ${b.join(", ")}.`}`}){super({name:iP,message:c}),this[O]=!0,this.toolName=a,this.availableTools=b}static isInstance(a){return am.hasMarker(a,iQ)}};O=iR;var iT="AI_ToolCallRepairError",iU=`vercel.ai.error.${iT}`,iV=Symbol.for(iU),iW=class extends am{constructor({cause:a,originalError:b,message:c=`Error repairing tool call: ${av(a)}`}){super({name:iT,message:c,cause:a}),this[P]=!0,this.originalError=b}static isInstance(a){return am.hasMarker(a,iU)}};P=iV;var iX=class extends am{constructor(a){super({name:"AI_UnsupportedModelVersionError",message:`Unsupported model version ${a.version} for provider "${a.provider}" and model "${a.modelId}". AI SDK 5 only supports models that implement specification version "v2".`}),this.version=a.version,this.provider=a.provider,this.modelId=a.modelId}},iY=(Symbol.for("vercel.ai.error.AI_InvalidDataContentError"),"AI_InvalidMessageRoleError"),iZ=`vercel.ai.error.${iY}`,i$=Symbol.for(iZ),i_=class extends am{constructor({role:a,message:b=`Invalid message role: '${a}'. Must be one of: "system", "user", "assistant", "tool".`}){super({name:iY,message:b}),this[Q]=!0,this.role=a}static isInstance(a){return am.hasMarker(a,iZ)}};Q=i$;var i0="AI_MessageConversionError",i1=`vercel.ai.error.${i0}`,i2=Symbol.for(i1),i3=class extends am{constructor({originalMessage:a,message:b}){super({name:i0,message:b}),this[R]=!0,this.originalMessage=a}static isInstance(a){return am.hasMarker(a,i1)}};R=i2;var i4="AI_DownloadError",i5=`vercel.ai.error.${i4}`,i6=Symbol.for(i5),i7=class extends am{constructor({url:a,statusCode:b,statusText:c,cause:d,message:e=null==d?`Failed to download ${a}: ${b} ${c}`:`Failed to download ${a}: ${d}`}){super({name:i4,message:e,cause:d}),this[S]=!0,this.url=a,this.statusCode=b,this.statusText=c}static isInstance(a){return am.hasMarker(a,i5)}};S=i6;var i8="AI_RetryError",i9=`vercel.ai.error.${i8}`,ja=Symbol.for(i9),jb=class extends am{constructor({message:a,reason:b,errors:c}){super({name:i8,message:a}),this[T]=!0,this.reason=b,this.errors=c,this.lastError=c[c.length-1]}static isInstance(a){return am.hasMarker(a,i9)}};function jc(a){var b;if("string"!=typeof a){if("v2"!==a.specificationVersion)throw new iX({version:a.specificationVersion,provider:a.provider,modelId:a.modelId});return a}return(null!=(b=globalThis.AI_SDK_DEFAULT_PROVIDER)?b:iq).languageModel(a)}T=ja;var jd=[{mediaType:"image/gif",bytesPrefix:[71,73,70],base64Prefix:"R0lG"},{mediaType:"image/png",bytesPrefix:[137,80,78,71],base64Prefix:"iVBORw"},{mediaType:"image/jpeg",bytesPrefix:[255,216],base64Prefix:"/9j/"},{mediaType:"image/webp",bytesPrefix:[82,73,70,70],base64Prefix:"UklGRg"},{mediaType:"image/bmp",bytesPrefix:[66,77],base64Prefix:"Qk"},{mediaType:"image/tiff",bytesPrefix:[73,73,42,0],base64Prefix:"SUkqAA"},{mediaType:"image/tiff",bytesPrefix:[77,77,0,42],base64Prefix:"TU0AKg"},{mediaType:"image/avif",bytesPrefix:[0,0,0,32,102,116,121,112,97,118,105,102],base64Prefix:"AAAAIGZ0eXBhdmlm"},{mediaType:"image/heic",bytesPrefix:[0,0,0,32,102,116,121,112,104,101,105,99],base64Prefix:"AAAAIGZ0eXBoZWlj"}];async function je({url:a}){var b;let c=a.toString();try{let a=await fetch(c);if(!a.ok)throw new i7({url:c,statusCode:a.status,statusText:a.statusText});return{data:new Uint8Array(await a.arrayBuffer()),mediaType:null!=(b=a.headers.get("content-type"))?b:void 0}}catch(a){if(i7.isInstance(a))throw a;throw new i7({url:c,cause:a})}}var jf=gw([fT(),gV(Uint8Array),gV(ArrayBuffer),function(a,b){let c=bn(b);return c.abort??(c.abort=!0),new gU({type:"custom",check:"custom",fn:a??(()=>!0),...c})}(a=>{var b,c;return null!=(c=null==(b=globalThis.Buffer)?void 0:b.isBuffer(a))&&c},{message:"Must be a Buffer"})]);function jg(a){if(a instanceof Uint8Array)return{data:a,mediaType:void 0};if(a instanceof ArrayBuffer)return{data:new Uint8Array(a),mediaType:void 0};if("string"==typeof a)try{a=new URL(a)}catch(a){}if(a instanceof URL&&"data:"===a.protocol){let{mediaType:b,base64Content:c}=function(a){try{let[b,c]=a.split(",");return{mediaType:b.split(";")[0].split(":")[1],base64Content:c}}catch(a){return{mediaType:void 0,base64Content:void 0}}}(a.toString());if(null==b||null==c)throw new am({name:"InvalidDataContentError",message:`Invalid data URL format in content ${a.toString()}`});return{data:c,mediaType:b}}return{data:a,mediaType:void 0}}async function jh({prompt:a,supportedUrls:b,downloadImplementation:c=je}){let d=await ji(a.messages,c,b);return[...null!=a.system?[{role:"system",content:a.system}]:[],...a.messages.map(a=>(function({message:a,downloadedAssets:b}){let c=a.role;switch(c){case"system":return{role:"system",content:a.content,providerOptions:a.providerOptions};case"user":if("string"==typeof a.content)return{role:"user",content:[{type:"text",text:a.content}],providerOptions:a.providerOptions};return{role:"user",content:a.content.map(a=>(function(a,b){var c;let d;if("text"===a.type)return{type:"text",text:a.text,providerOptions:a.providerOptions};let e=a.type;switch(e){case"image":d=a.image;break;case"file":d=a.data;break;default:throw Error(`Unsupported part type: ${e}`)}let{data:f,mediaType:g}=jg(d),h=null!=g?g:a.mediaType,i=f;if(i instanceof URL){let a=b[i.toString()];a&&(i=a.data,null!=h||(h=a.mediaType))}switch(e){case"image":return(i instanceof Uint8Array||"string"==typeof i)&&(h=null!=(c=function({data:a,signatures:b}){let c="string"==typeof a&&a.startsWith("SUQz")||"string"!=typeof a&&a.length>10&&73===a[0]&&68===a[1]&&51===a[2]?(a=>{let b="string"==typeof a?dP(a):a,c=(127&b[6])<<21|(127&b[7])<<14|(127&b[8])<<7|127&b[9];return b.slice(c+10)})(a):a;for(let a of b)if("string"==typeof c?c.startsWith(a.base64Prefix):c.length>=a.bytesPrefix.length&&a.bytesPrefix.every((a,b)=>c[b]===a))return a.mediaType}({data:i,signatures:jd}))?c:h),{type:"file",mediaType:null!=h?h:"image/*",filename:void 0,data:i,providerOptions:a.providerOptions};case"file":if(null==h)throw Error("Media type is missing for file part");return{type:"file",mediaType:h,filename:a.filename,data:i,providerOptions:a.providerOptions}}})(a,b)).filter(a=>"text"!==a.type||""!==a.text),providerOptions:a.providerOptions};case"assistant":if("string"==typeof a.content)return{role:"assistant",content:[{type:"text",text:a.content}],providerOptions:a.providerOptions};return{role:"assistant",content:a.content.filter(a=>"text"!==a.type||""!==a.text).map(a=>{let b=a.providerOptions;switch(a.type){case"file":{let{data:c,mediaType:d}=jg(a.data);return{type:"file",data:c,filename:a.filename,mediaType:null!=d?d:a.mediaType,providerOptions:b}}case"reasoning":return{type:"reasoning",text:a.text,providerOptions:b};case"text":return{type:"text",text:a.text,providerOptions:b};case"tool-call":return{type:"tool-call",toolCallId:a.toolCallId,toolName:a.toolName,input:a.input,providerExecuted:a.providerExecuted,providerOptions:b};case"tool-result":return{type:"tool-result",toolCallId:a.toolCallId,toolName:a.toolName,output:a.output,providerOptions:b}}}),providerOptions:a.providerOptions};case"tool":return{role:"tool",content:a.content.map(a=>({type:"tool-result",toolCallId:a.toolCallId,toolName:a.toolName,output:a.output,providerOptions:a.providerOptions})),providerOptions:a.providerOptions};default:throw new i_({role:c})}})({message:a,downloadedAssets:d}))]}async function ji(a,b,c){let d=a.filter(a=>"user"===a.role).map(a=>a.content).filter(a=>Array.isArray(a)).flat().filter(a=>"image"===a.type||"file"===a.type).map(a=>{var b;let c=null!=(b=a.mediaType)?b:"image"===a.type?"image/*":void 0,d="image"===a.type?a.image:a.data;if("string"==typeof d)try{d=new URL(d)}catch(a){}return{mediaType:c,data:d}}).filter(a=>a.data instanceof URL&&null!=a.mediaType&&!function({mediaType:a,url:b,supportedUrls:c}){return b=b.toLowerCase(),a=a.toLowerCase(),Object.entries(c).map(([a,b])=>{let c=a.toLowerCase();return"*"===c||"*/*"===c?{mediaTypePrefix:"",regexes:b}:{mediaTypePrefix:c.replace(/\*/,""),regexes:b}}).filter(({mediaTypePrefix:b})=>a.startsWith(b)).flatMap(({regexes:a})=>a).some(a=>a.test(b))}({url:a.data.toString(),mediaType:a.mediaType,supportedUrls:c})).map(a=>a.data);return Object.fromEntries((await Promise.all(d.map(async a=>({url:a,data:await b({url:a})})))).map(({url:a,data:b})=>[a.toString(),b]))}var jj=gT(()=>gw([new gi({type:"null",...bn(void 0)}),fT(),gd(),gh(),gB(fT(),jj),gq(jj)])),jk=gB(fT(),gB(fT(),jj)),jl=gs({type:gF("text"),text:fT(),providerOptions:jk.optional()}),jm=gs({type:gF("image"),image:gw([jf,gV(URL)]),mediaType:fT().optional(),providerOptions:jk.optional()}),jn=gs({type:gF("file"),data:gw([jf,gV(URL)]),filename:fT().optional(),mediaType:fT(),providerOptions:jk.optional()}),jo=gs({type:gF("reasoning"),text:fT(),providerOptions:jk.optional()}),jp=gs({type:gF("tool-call"),toolCallId:fT(),toolName:fT(),input:gm(),providerOptions:jk.optional(),providerExecuted:gh().optional()}),jq=gy("type",[gs({type:gF("text"),value:fT()}),gs({type:gF("json"),value:jj}),gs({type:gF("error-text"),value:fT()}),gs({type:gF("error-json"),value:jj}),gs({type:gF("content"),value:gq(gw([gs({type:gF("text"),text:fT()}),gs({type:gF("media"),data:fT(),mediaType:fT()})]))})]),jr=gs({type:gF("tool-result"),toolCallId:fT(),toolName:fT(),output:jq,providerOptions:jk.optional()}),js=gs({role:gF("system"),content:fT(),providerOptions:jk.optional()}),jt=gs({role:gF("user"),content:gw([fT(),gq(gw([jl,jm,jn]))]),providerOptions:jk.optional()}),ju=gs({role:gF("assistant"),content:gw([fT(),gq(gw([jl,jn,jo,jp,jr]))]),providerOptions:jk.optional()}),jv=gw([js,jt,ju,gs({role:gF("tool"),content:gq(jr),providerOptions:jk.optional()})]);async function jw(a){let b;if(null==a.prompt&&null==a.messages)throw new aD({prompt:a,message:"prompt or messages must be defined"});if(null!=a.prompt&&null!=a.messages)throw new aD({prompt:a,message:"prompt and messages cannot be defined at the same time"});if(null!=a.system&&"string"!=typeof a.system)throw new aD({prompt:a,message:"system must be a string"});if(null!=a.prompt&&"string"==typeof a.prompt)b=[{role:"user",content:a.prompt}];else if(null!=a.prompt&&Array.isArray(a.prompt))b=a.prompt;else if(null!=a.messages)b=a.messages;else throw new aD({prompt:a,message:"prompt or messages must be defined"});if(0===b.length)throw new aD({prompt:a,message:"messages must not be empty"});let c=await dw({value:b,schema:gq(jv)});if(!c.success)throw new aD({prompt:a,message:"The messages must be a ModelMessage[]. If you have passed a UIMessage[], you can use convertToModelMessages to convert them.",cause:c.error});return{messages:b,system:a.system}}function jx({operationId:a,telemetry:b}){return{"operation.name":`${a}${(null==b?void 0:b.functionId)!=null?` ${b.functionId}`:""}`,"resource.name":null==b?void 0:b.functionId,"ai.operationId":a,"ai.telemetry.functionId":null==b?void 0:b.functionId}}var jy={startSpan:()=>jz,startActiveSpan:(a,b,c,d)=>"function"==typeof b?b(jz):"function"==typeof c?c(jz):"function"==typeof d?d(jz):void 0},jz={spanContext:()=>jA,setAttribute(){return this},setAttributes(){return this},addEvent(){return this},addLink(){return this},addLinks(){return this},setStatus(){return this},updateName(){return this},end(){return this},isRecording:()=>!1,recordException(){return this}},jA={traceId:"",spanId:"",traceFlags:0};function jB({name:a,tracer:b,attributes:c,fn:d,endWhenDone:e=!0}){return b.startActiveSpan(a,{attributes:c},async a=>{try{let b=await d(a);return e&&a.end(),b}catch(b){try{jC(a,b)}finally{a.end()}throw b}})}function jC(a,b){b instanceof Error?(a.recordException({name:b.name,message:b.message,stack:b.stack}),a.setStatus({code:it.s.ERROR,message:b.message})):a.setStatus({code:it.s.ERROR})}function jD({telemetry:a,attributes:b}){return(null==a?void 0:a.isEnabled)!==!0?{}:Object.entries(b).reduce((b,[c,d])=>{if(null==d)return b;if("object"==typeof d&&"input"in d&&"function"==typeof d.input){if((null==a?void 0:a.recordInputs)===!1)return b;let e=d.input();return null==e?b:{...b,[c]:e}}if("object"==typeof d&&"output"in d&&"function"==typeof d.output){if((null==a?void 0:a.recordOutputs)===!1)return b;let e=d.output();return null==e?b:{...b,[c]:e}}return{...b,[c]:d}},{})}function jE(a,b){return null==a&&null==b?void 0:(null!=a?a:0)+(null!=b?b:0)}function jF(a){return void 0===a?[]:Array.isArray(a)?a:[a]}async function jG(a,{maxRetries:b,delayInMs:c,backoffFactor:d,abortSignal:e},f=[]){try{return await a()}catch(j){if(dj(j)||0===b)throw j;let g=di(j),h=[...f,j],i=h.length;if(i>b)throw new jb({message:`Failed after ${i} attempts. Last error: ${g}`,reason:"maxRetriesExceeded",errors:h});if(j instanceof Error&&aq.isInstance(j)&&!0===j.isRetryable&&i<=b)return await dd(function({error:a,exponentialBackoffDelay:b}){let c,d=a.responseHeaders;if(!d)return b;let e=d["retry-after-ms"];if(e){let a=parseFloat(e);Number.isNaN(a)||(c=a)}let f=d["retry-after"];if(f&&void 0===c){let a=parseFloat(f);c=Number.isNaN(a)?Date.parse(f)-Date.now():1e3*a}return null!=c&&!Number.isNaN(c)&&0<=c&&(c<6e4||c<b)?c:b}({error:j,exponentialBackoffDelay:c}),{abortSignal:e}),jG(a,{maxRetries:b,delayInMs:d*c,backoffFactor:d,abortSignal:e},h);if(1===i)throw j;throw new jb({message:`Failed after ${i} attempts with non-retryable error: '${g}'`,reason:"errorNotRetryable",errors:h})}}var jH=class{constructor({data:a,mediaType:b}){let c=a instanceof Uint8Array;this.base64Data=c?void 0:a,this.uint8ArrayData=c?a:void 0,this.mediaType=b}get base64(){return null==this.base64Data&&(this.base64Data=dQ(this.uint8ArrayData)),this.base64Data}get uint8Array(){return null==this.uint8ArrayData&&(this.uint8ArrayData=dP(this.base64Data)),this.uint8ArrayData}},jI=class extends jH{constructor(a){super(a),this.type="file"}};async function jJ({toolCall:a,tools:b,repairToolCall:c,system:d,messages:e}){try{if(null==b)throw new iS({toolName:a.toolName});try{return await jK({toolCall:a,tools:b})}catch(g){if(null==c||!(iS.isInstance(g)||iG.isInstance(g)))throw g;let f=null;try{f=await c({toolCall:a,tools:b,inputSchema:({toolName:a})=>{let{inputSchema:c}=b[a];return dM(c).jsonSchema},system:d,messages:e,error:g})}catch(a){throw new iW({cause:a,originalError:g})}if(null==f)throw g;return await jK({toolCall:f,tools:b})}}catch(d){let b=await dy({text:a.input}),c=b.success?b.value:a.input;return{type:"tool-call",toolCallId:a.toolCallId,toolName:a.toolName,input:c,dynamic:!0,invalid:!0,error:d}}}async function jK({toolCall:a,tools:b}){let c=a.toolName,d=b[c];if(null==d)throw new iS({toolName:a.toolName,availableTools:Object.keys(b)});let e=dM(d.inputSchema),f=""===a.input.trim()?await dw({value:{},schema:e}):await dy({text:a.input,schema:e});if(!1===f.success)throw new iG({toolName:c,toolInput:a.input,cause:f.error});return"dynamic"===d.type?{type:"tool-call",toolCallId:a.toolCallId,toolName:a.toolName,input:f.value,providerExecuted:a.providerExecuted,providerMetadata:a.providerMetadata,dynamic:!0}:{type:"tool-call",toolCallId:a.toolCallId,toolName:c,input:f.value,providerExecuted:a.providerExecuted,providerMetadata:a.providerMetadata}}var jL=class{constructor({content:a,finishReason:b,usage:c,warnings:d,request:e,response:f,providerMetadata:g}){this.content=a,this.finishReason=b,this.usage=c,this.warnings=d,this.request=e,this.response=f,this.providerMetadata=g}get text(){return this.content.filter(a=>"text"===a.type).map(a=>a.text).join("")}get reasoning(){return this.content.filter(a=>"reasoning"===a.type)}get reasoningText(){return 0===this.reasoning.length?void 0:this.reasoning.map(a=>a.text).join("")}get files(){return this.content.filter(a=>"file"===a.type).map(a=>a.file)}get sources(){return this.content.filter(a=>"source"===a.type)}get toolCalls(){return this.content.filter(a=>"tool-call"===a.type)}get staticToolCalls(){return this.toolCalls.filter(a=>!1===a.dynamic)}get dynamicToolCalls(){return this.toolCalls.filter(a=>!0===a.dynamic)}get toolResults(){return this.content.filter(a=>"tool-result"===a.type)}get staticToolResults(){return this.toolResults.filter(a=>!1===a.dynamic)}get dynamicToolResults(){return this.toolResults.filter(a=>!0===a.dynamic)}};async function jM({stopConditions:a,steps:b}){return(await Promise.all(a.map(a=>a({steps:b})))).some(a=>a)}function jN({output:a,tool:b,errorMode:c}){return"text"===c?{type:"error-text",value:av(a)}:"json"===c?{type:"error-json",value:jO(a)}:(null==b?void 0:b.toModelOutput)?b.toModelOutput(a):"string"==typeof a?{type:"text",value:a}:{type:"json",value:jO(a)}}function jO(a){return void 0===a?null:a}function jP({content:a,tools:b}){let c=[],d=a.filter(a=>"source"!==a.type).filter(a=>("tool-result"!==a.type||a.providerExecuted)&&("tool-error"!==a.type||a.providerExecuted)).filter(a=>"text"!==a.type||a.text.length>0).map(a=>{switch(a.type){case"text":return{type:"text",text:a.text,providerOptions:a.providerMetadata};case"reasoning":return{type:"reasoning",text:a.text,providerOptions:a.providerMetadata};case"file":return{type:"file",data:a.file.base64,mediaType:a.file.mediaType,providerOptions:a.providerMetadata};case"tool-call":return{type:"tool-call",toolCallId:a.toolCallId,toolName:a.toolName,input:a.input,providerExecuted:a.providerExecuted,providerOptions:a.providerMetadata};case"tool-result":return{type:"tool-result",toolCallId:a.toolCallId,toolName:a.toolName,output:jN({tool:null==b?void 0:b[a.toolName],output:a.output,errorMode:"none"}),providerExecuted:!0,providerOptions:a.providerMetadata};case"tool-error":return{type:"tool-result",toolCallId:a.toolCallId,toolName:a.toolName,output:jN({tool:null==b?void 0:b[a.toolName],output:a.error,errorMode:"json"}),providerOptions:a.providerMetadata}}});d.length>0&&c.push({role:"assistant",content:d});let e=a.filter(a=>"tool-result"===a.type||"tool-error"===a.type).filter(a=>!a.providerExecuted).map(a=>({type:"tool-result",toolCallId:a.toolCallId,toolName:a.toolName,output:jN({tool:null==b?void 0:b[a.toolName],output:"tool-result"===a.type?a.output:a.error,errorMode:"tool-error"===a.type?"text":"none"})}));return e.length>0&&c.push({role:"tool",content:e}),c}function jQ(a,b){let c=new Headers(null!=a?a:{});for(let[a,d]of Object.entries(b))c.has(a)||c.set(a,d);return c}function jR({response:a,status:b,statusText:c,headers:d,stream:e}){a.writeHead(null!=b?b:200,c,d);let f=e.getReader();(async()=>{try{for(;;){let{done:b,value:c}=await f.read();if(b)break;a.write(c)}}catch(a){throw a}finally{a.end()}})()}dg({prefix:"aitxt",size:24});var jS=class extends TransformStream{constructor(){super({transform(a,b){b.enqueue(`data: ${JSON.stringify(a)}

`)},flush(a){a.enqueue("data: [DONE]\n\n")}})}},jT={"content-type":"text/event-stream","cache-control":"no-cache",connection:"keep-alive","x-vercel-ai-ui-message-stream":"v1","x-accel-buffering":"no"};async function jU(a){if(void 0===a)return{value:void 0,state:"undefined-input"};let b=await dy({text:a});return b.success?{value:b.value,state:"successful-parse"}:(b=await dy({text:function(a){let b=["ROOT"],c=-1,d=null;function e(a,e,f){switch(a){case'"':c=e,b.pop(),b.push(f),b.push("INSIDE_STRING");break;case"f":case"t":case"n":c=e,d=e,b.pop(),b.push(f),b.push("INSIDE_LITERAL");break;case"-":b.pop(),b.push(f),b.push("INSIDE_NUMBER");break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":c=e,b.pop(),b.push(f),b.push("INSIDE_NUMBER");break;case"{":c=e,b.pop(),b.push(f),b.push("INSIDE_OBJECT_START");break;case"[":c=e,b.pop(),b.push(f),b.push("INSIDE_ARRAY_START")}}function f(a,d){switch(a){case",":b.pop(),b.push("INSIDE_OBJECT_AFTER_COMMA");break;case"}":c=d,b.pop()}}function g(a,d){switch(a){case",":b.pop(),b.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":c=d,b.pop()}}for(let h=0;h<a.length;h++){let i=a[h];switch(b[b.length-1]){case"ROOT":e(i,h,"FINISH");break;case"INSIDE_OBJECT_START":switch(i){case'"':b.pop(),b.push("INSIDE_OBJECT_KEY");break;case"}":c=h,b.pop()}break;case"INSIDE_OBJECT_AFTER_COMMA":'"'===i&&(b.pop(),b.push("INSIDE_OBJECT_KEY"));break;case"INSIDE_OBJECT_KEY":'"'===i&&(b.pop(),b.push("INSIDE_OBJECT_AFTER_KEY"));break;case"INSIDE_OBJECT_AFTER_KEY":":"===i&&(b.pop(),b.push("INSIDE_OBJECT_BEFORE_VALUE"));break;case"INSIDE_OBJECT_BEFORE_VALUE":e(i,h,"INSIDE_OBJECT_AFTER_VALUE");break;case"INSIDE_OBJECT_AFTER_VALUE":f(i,h);break;case"INSIDE_STRING":switch(i){case'"':b.pop(),c=h;break;case"\\":b.push("INSIDE_STRING_ESCAPE");break;default:c=h}break;case"INSIDE_ARRAY_START":"]"===i?(c=h,b.pop()):(c=h,e(i,h,"INSIDE_ARRAY_AFTER_VALUE"));break;case"INSIDE_ARRAY_AFTER_VALUE":switch(i){case",":b.pop(),b.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":c=h,b.pop();break;default:c=h}break;case"INSIDE_ARRAY_AFTER_COMMA":e(i,h,"INSIDE_ARRAY_AFTER_VALUE");break;case"INSIDE_STRING_ESCAPE":b.pop(),c=h;break;case"INSIDE_NUMBER":switch(i){case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":c=h;break;case"e":case"E":case"-":case".":break;case",":b.pop(),"INSIDE_ARRAY_AFTER_VALUE"===b[b.length-1]&&g(i,h),"INSIDE_OBJECT_AFTER_VALUE"===b[b.length-1]&&f(i,h);break;case"}":b.pop(),"INSIDE_OBJECT_AFTER_VALUE"===b[b.length-1]&&f(i,h);break;case"]":b.pop(),"INSIDE_ARRAY_AFTER_VALUE"===b[b.length-1]&&g(i,h);break;default:b.pop()}break;case"INSIDE_LITERAL":{let e=a.substring(d,h+1);"false".startsWith(e)||"true".startsWith(e)||"null".startsWith(e)?c=h:(b.pop(),"INSIDE_OBJECT_AFTER_VALUE"===b[b.length-1]?f(i,h):"INSIDE_ARRAY_AFTER_VALUE"===b[b.length-1]&&g(i,h))}}}let h=a.slice(0,c+1);for(let c=b.length-1;c>=0;c--)switch(b[c]){case"INSIDE_STRING":h+='"';break;case"INSIDE_OBJECT_KEY":case"INSIDE_OBJECT_AFTER_KEY":case"INSIDE_OBJECT_AFTER_COMMA":case"INSIDE_OBJECT_START":case"INSIDE_OBJECT_BEFORE_VALUE":case"INSIDE_OBJECT_AFTER_VALUE":h+="}";break;case"INSIDE_ARRAY_START":case"INSIDE_ARRAY_AFTER_COMMA":case"INSIDE_ARRAY_AFTER_VALUE":h+="]";break;case"INSIDE_LITERAL":{let b=a.substring(d,a.length);"true".startsWith(b)?h+="true".slice(b.length):"false".startsWith(b)?h+="false".slice(b.length):"null".startsWith(b)&&(h+="null".slice(b.length))}}return h}(a)})).success?{value:b.value,state:"repaired-parse"}:{value:void 0,state:"failed-parse"}}function jV(a){return a.type.startsWith("tool-")}function jW(a){return a.type.split("-").slice(1).join("-")}function jX(a){let b=a.pipeThrough(new TransformStream);return b[Symbol.asyncIterator]=()=>{let a=b.getReader();return{async next(){let{done:b,value:c}=await a.read();return b?{done:!0,value:void 0}:{done:!1,value:c}}}},b}async function jY({stream:a,onError:b}){let c=a.getReader();try{for(;;){let{done:a}=await c.read();if(a)break}}catch(a){null==b||b(a)}finally{c.releaseLock()}}function jZ(){let a,b;return{promise:new Promise((c,d)=>{a=c,b=d}),resolve:a,reject:b}}gw([gt({type:gF("text-start"),id:fT(),providerMetadata:jk.optional()}),gt({type:gF("text-delta"),id:fT(),delta:fT(),providerMetadata:jk.optional()}),gt({type:gF("text-end"),id:fT(),providerMetadata:jk.optional()}),gt({type:gF("error"),errorText:fT()}),gt({type:gF("tool-input-start"),toolCallId:fT(),toolName:fT(),providerExecuted:gh().optional(),dynamic:gh().optional()}),gt({type:gF("tool-input-delta"),toolCallId:fT(),inputTextDelta:fT()}),gt({type:gF("tool-input-available"),toolCallId:fT(),toolName:fT(),input:gm(),providerExecuted:gh().optional(),providerMetadata:jk.optional(),dynamic:gh().optional()}),gt({type:gF("tool-input-error"),toolCallId:fT(),toolName:fT(),input:gm(),providerExecuted:gh().optional(),providerMetadata:jk.optional(),dynamic:gh().optional(),errorText:fT()}),gt({type:gF("tool-output-available"),toolCallId:fT(),output:gm(),providerExecuted:gh().optional(),dynamic:gh().optional(),preliminary:gh().optional()}),gt({type:gF("tool-output-error"),toolCallId:fT(),errorText:fT(),providerExecuted:gh().optional(),dynamic:gh().optional()}),gt({type:gF("reasoning"),text:fT(),providerMetadata:jk.optional()}),gt({type:gF("reasoning-start"),id:fT(),providerMetadata:jk.optional()}),gt({type:gF("reasoning-delta"),id:fT(),delta:fT(),providerMetadata:jk.optional()}),gt({type:gF("reasoning-end"),id:fT(),providerMetadata:jk.optional()}),gt({type:gF("reasoning-part-finish")}),gt({type:gF("source-url"),sourceId:fT(),url:fT(),title:fT().optional(),providerMetadata:jk.optional()}),gt({type:gF("source-document"),sourceId:fT(),mediaType:fT(),title:fT(),filename:fT().optional(),providerMetadata:jk.optional()}),gt({type:gF("file"),url:fT(),mediaType:fT(),providerMetadata:jk.optional()}),gt({type:fT().startsWith("data-"),id:fT().optional(),data:gm(),transient:gh().optional()}),gt({type:gF("start-step")}),gt({type:gF("finish-step")}),gt({type:gF("start"),messageId:fT().optional(),messageMetadata:gm().optional()}),gt({type:gF("finish"),messageMetadata:gm().optional()}),gt({type:gF("abort")}),gt({type:gF("message-metadata"),messageMetadata:gm()})]);var j$=class{constructor(){this.status={type:"pending"},this._resolve=void 0,this._reject=void 0}get promise(){return this._promise||(this._promise=new Promise((a,b)=>{"resolved"===this.status.type?a(this.status.value):"rejected"===this.status.type&&b(this.status.error),this._resolve=a,this._reject=b})),this._promise}resolve(a){var b;this.status={type:"resolved",value:a},this._promise&&(null==(b=this._resolve)||b.call(this,a))}reject(a){var b;this.status={type:"rejected",error:a},this._promise&&(null==(b=this._reject)||b.call(this,a))}},j_=dg({prefix:"aitxt",size:24}),j0=class{constructor({model:a,telemetry:b,headers:c,settings:d,maxRetries:e,abortSignal:f,system:g,prompt:h,messages:i,tools:j,toolChoice:k,transforms:l,activeTools:m,repairToolCall:n,stopConditions:o,output:p,providerOptions:q,prepareStep:r,includeRawChunks:s,now:t,currentDate:u,generateId:v,onChunk:w,onError:x,onFinish:y,onAbort:z,onStepFinish:A,experimental_context:B}){let C,D,E,F;this._totalUsage=new j$,this._finishReason=new j$,this._steps=new j$,this.output=p,this.includeRawChunks=s,this.tools=j;let G=[],H=[],I={},J=[],K=[],L={},M={},N=new TransformStream({async transform(a,b){var c,d,e,f;b.enqueue(a);let{part:g}=a;if(("text-delta"===g.type||"reasoning-delta"===g.type||"source"===g.type||"tool-call"===g.type||"tool-result"===g.type||"tool-input-start"===g.type||"tool-input-delta"===g.type||"raw"===g.type)&&await (null==w?void 0:w({chunk:g})),"error"===g.type&&await x({error:(f=g.error,hR.isInstance(f)||h_.isInstance(f)?new am({name:"GatewayError",message:"Vercel AI Gateway access failed. If you want to use AI SDK providers directly, use the providers, e.g. @ai-sdk/openai, or register a different global default provider.",cause:f}):f)}),"text-start"===g.type&&(L[g.id]={type:"text",text:"",providerMetadata:g.providerMetadata},G.push(L[g.id])),"text-delta"===g.type){let a=L[g.id];if(null==a)return void b.enqueue({part:{type:"error",error:`text part ${g.id} not found`},partialOutput:void 0});a.text+=g.text,a.providerMetadata=null!=(c=g.providerMetadata)?c:a.providerMetadata}if("text-end"===g.type&&delete L[g.id],"reasoning-start"===g.type&&(M[g.id]={type:"reasoning",text:"",providerMetadata:g.providerMetadata},G.push(M[g.id])),"reasoning-delta"===g.type){let a=M[g.id];if(null==a)return void b.enqueue({part:{type:"error",error:`reasoning part ${g.id} not found`},partialOutput:void 0});a.text+=g.text,a.providerMetadata=null!=(d=g.providerMetadata)?d:a.providerMetadata}if("reasoning-end"===g.type){let a=M[g.id];if(null==a)return void b.enqueue({part:{type:"error",error:`reasoning part ${g.id} not found`},partialOutput:void 0});a.providerMetadata=null!=(e=g.providerMetadata)?e:a.providerMetadata,delete M[g.id]}if("file"===g.type&&G.push({type:"file",file:g.file}),"source"===g.type&&G.push(g),"tool-call"===g.type&&G.push(g),"tool-result"!==g.type||g.preliminary||G.push(g),"tool-error"===g.type&&G.push(g),"start-step"===g.type&&(I=g.request,J=g.warnings),"finish-step"===g.type){let a=jP({content:G,tools:j}),b=new jL({content:G,finishReason:g.finishReason,usage:g.usage,warnings:J,request:I,response:{...g.response,messages:[...H,...a]},providerMetadata:g.providerMetadata});await (null==A?void 0:A(b)),K.push(b),G=[],M={},L={},H.push(...a),C.resolve()}"finish"===g.type&&(F=g.totalUsage,E=g.finishReason)},async flush(a){try{if(0===K.length){let a=new iO({message:"No output generated. Check the stream for errors."});V._finishReason.reject(a),V._totalUsage.reject(a),V._steps.reject(a);return}let a=null!=E?E:"unknown",c=null!=F?F:{inputTokens:void 0,outputTokens:void 0,totalTokens:void 0};V._finishReason.resolve(a),V._totalUsage.resolve(c),V._steps.resolve(K);let d=K[K.length-1];await (null==y?void 0:y({finishReason:a,totalUsage:c,usage:d.usage,content:d.content,text:d.text,reasoningText:d.reasoningText,reasoning:d.reasoning,files:d.files,sources:d.sources,toolCalls:d.toolCalls,staticToolCalls:d.staticToolCalls,dynamicToolCalls:d.dynamicToolCalls,toolResults:d.toolResults,staticToolResults:d.staticToolResults,dynamicToolResults:d.dynamicToolResults,request:d.request,response:d.response,warnings:d.warnings,providerMetadata:d.providerMetadata,steps:K})),D.setAttributes(jD({telemetry:b,attributes:{"ai.response.finishReason":a,"ai.response.text":{output:()=>d.text},"ai.response.toolCalls":{output:()=>{var a;return(null==(a=d.toolCalls)?void 0:a.length)?JSON.stringify(d.toolCalls):void 0}},"ai.response.providerMetadata":JSON.stringify(d.providerMetadata),"ai.usage.inputTokens":c.inputTokens,"ai.usage.outputTokens":c.outputTokens,"ai.usage.totalTokens":c.totalTokens,"ai.usage.reasoningTokens":c.reasoningTokens,"ai.usage.cachedInputTokens":c.cachedInputTokens}}))}catch(b){a.error(b)}finally{D.end()}}}),O=function(){let a=[],b=null,c=!1,d=jZ(),e=()=>{c=!0,d.resolve(),a.forEach(a=>a.cancel()),a=[],null==b||b.close()},f=async()=>{if(c&&0===a.length){null==b||b.close();return}if(0===a.length)return d=jZ(),await d.promise,f();try{let{value:d,done:e}=await a[0].read();e?(a.shift(),a.length>0?await f():c&&(null==b||b.close())):null==b||b.enqueue(d)}catch(c){null==b||b.error(c),a.shift(),e()}};return{stream:new ReadableStream({start(a){b=a},pull:f,async cancel(){for(let b of a)await b.cancel();a=[],c=!0}}),addStream:b=>{if(c)throw Error("Cannot add inner stream: outer stream is closed");a.push(b.getReader()),d.resolve()},close:()=>{c=!0,d.resolve(),0===a.length&&(null==b||b.close())},terminate:e}}();this.addStream=O.addStream,this.closeStream=O.close;let P=O.stream;for(let a of(P=(P=function(a,b){return new ReadableStream({async start(c){let d=a.getReader();try{for(;;){let{done:a,value:b}=await d.read();if(a){c.close();break}c.enqueue(b)}}catch(a){await b({error:a,controller:c})}},cancel:b=>a.cancel(b)})}(P,({error:a,controller:b})=>{dj(a)&&(null==f?void 0:f.aborted)?(null==z||z({steps:K}),b.enqueue({type:"abort"}),b.close()):b.error(a)})).pipeThrough(new TransformStream({start(a){a.enqueue({type:"start"})}})),l))P=P.pipeThrough(a({tools:j,stopStream(){O.terminate()}}));this.baseStream=P.pipeThrough(function(a){let b;if(!a)return new TransformStream({transform(a,b){b.enqueue({part:a,partialOutput:void 0})}});let c="",d="",e="";function f({controller:a,partialOutput:c}){a.enqueue({part:{type:"text-delta",id:b,text:d},partialOutput:c}),d=""}return new TransformStream({async transform(g,h){if("finish-step"===g.type&&d.length>0&&f({controller:h}),"text-delta"!==g.type&&"text-start"!==g.type&&"text-end"!==g.type)return void h.enqueue({part:g,partialOutput:void 0});if(null==b)b=g.id;else if(g.id!==b)return void h.enqueue({part:g,partialOutput:void 0});if("text-start"===g.type)return void h.enqueue({part:g,partialOutput:void 0});if("text-end"===g.type){d.length>0&&f({controller:h}),h.enqueue({part:g,partialOutput:void 0});return}c+=g.text,d+=g.text;let i=await a.parsePartial({text:c});if(null!=i){let a=JSON.stringify(i.partial);a!==e&&(f({controller:h,partialOutput:i.partial}),e=a)}}})}(p)).pipeThrough(N);let{maxRetries:Q,retry:R}=function({maxRetries:a,abortSignal:b}){if(null!=a){if(!Number.isInteger(a))throw new iC({parameter:"maxRetries",value:a,message:"maxRetries must be an integer"});if(a<0)throw new iC({parameter:"maxRetries",value:a,message:"maxRetries must be >= 0"})}let c=null!=a?a:2;return{maxRetries:c,retry:(({maxRetries:a=2,initialDelayInMs:b=2e3,backoffFactor:c=2,abortSignal:d}={})=>async e=>jG(e,{maxRetries:a,delayInMs:b,backoffFactor:c,abortSignal:d}))({maxRetries:c,abortSignal:b})}}({maxRetries:e,abortSignal:f}),S=function({isEnabled:a=!1,tracer:b}={}){return a?b||is.u.getTracer("ai"):jy}(b),T=function({maxOutputTokens:a,temperature:b,topP:c,topK:d,presencePenalty:e,frequencyPenalty:f,seed:g,stopSequences:h}){if(null!=a){if(!Number.isInteger(a))throw new iC({parameter:"maxOutputTokens",value:a,message:"maxOutputTokens must be an integer"});if(a<1)throw new iC({parameter:"maxOutputTokens",value:a,message:"maxOutputTokens must be >= 1"})}if(null!=b&&"number"!=typeof b)throw new iC({parameter:"temperature",value:b,message:"temperature must be a number"});if(null!=c&&"number"!=typeof c)throw new iC({parameter:"topP",value:c,message:"topP must be a number"});if(null!=d&&"number"!=typeof d)throw new iC({parameter:"topK",value:d,message:"topK must be a number"});if(null!=e&&"number"!=typeof e)throw new iC({parameter:"presencePenalty",value:e,message:"presencePenalty must be a number"});if(null!=f&&"number"!=typeof f)throw new iC({parameter:"frequencyPenalty",value:f,message:"frequencyPenalty must be a number"});if(null!=g&&!Number.isInteger(g))throw new iC({parameter:"seed",value:g,message:"seed must be an integer"});return{maxOutputTokens:a,temperature:b,topP:c,topK:d,presencePenalty:e,frequencyPenalty:f,stopSequences:h,seed:g}}(d),U=function({model:a,settings:b,telemetry:c,headers:d}){var e;return{"ai.model.provider":a.provider,"ai.model.id":a.modelId,...Object.entries(b).reduce((a,[b,c])=>(a[`ai.settings.${b}`]=c,a),{}),...Object.entries(null!=(e=null==c?void 0:c.metadata)?e:{}).reduce((a,[b,c])=>(a[`ai.telemetry.metadata.${b}`]=c,a),{}),...Object.entries(null!=d?d:{}).reduce((a,[b,c])=>(void 0!==c&&(a[`ai.request.headers.${b}`]=c),a),{})}}({model:a,telemetry:b,headers:c,settings:{...T,maxRetries:Q}}),V=this;jB({name:"ai.streamText",attributes:jD({telemetry:b,attributes:{...jx({operationId:"ai.streamText",telemetry:b}),...U,"ai.prompt":{input:()=>JSON.stringify({system:g,prompt:h,messages:i})}}}),tracer:S,endWhenDone:!1,fn:async d=>{async function e({currentStep:d,responseMessages:l,usage:s}){var w,x,y,z,A;let D,E,F=V.includeRawChunks;C=new j$;let G=await jw({system:g,prompt:h,messages:i}),H=[...G.messages,...l],I=await (null==r?void 0:r({model:a,steps:K,stepNumber:K.length,messages:H})),J=await jh({prompt:{system:null!=(w=null==I?void 0:I.system)?w:G.system,messages:null!=(x=null==I?void 0:I.messages)?x:H},supportedUrls:await a.supportedUrls}),L=jc(null!=(y=null==I?void 0:I.model)?y:a),{toolChoice:M,tools:N}=function({tools:a,toolChoice:b,activeTools:c}){return null!=a&&Object.keys(a).length>0?{tools:(null!=c?Object.entries(a).filter(([a])=>c.includes(a)):Object.entries(a)).map(([a,b])=>{let c=b.type;switch(c){case void 0:case"dynamic":case"function":return{type:"function",name:a,description:b.description,inputSchema:dM(b.inputSchema).jsonSchema,providerOptions:b.providerOptions};case"provider-defined":return{type:"provider-defined",name:a,id:b.id,args:b.args};default:throw Error(`Unsupported tool type: ${c}`)}}),toolChoice:null==b?{type:"auto"}:"string"==typeof b?{type:b}:{type:"tool",toolName:b.toolName}}:{tools:void 0,toolChoice:void 0}}({tools:j,toolChoice:null!=(z=null==I?void 0:I.toolChoice)?z:k,activeTools:null!=(A=null==I?void 0:I.activeTools)?A:m}),{result:{stream:O,response:P,request:Q},doStreamSpan:W,startTimestampMs:X}=await R(()=>jB({name:"ai.streamText.doStream",attributes:jD({telemetry:b,attributes:{...jx({operationId:"ai.streamText.doStream",telemetry:b}),...U,"ai.model.provider":L.provider,"ai.model.id":L.modelId,"ai.prompt.messages":{input:()=>JSON.stringify(J.map(a=>({...a,content:"string"==typeof a.content?a.content:a.content.map(a=>{var b;return"file"===a.type?{...a,data:a.data instanceof Uint8Array?"string"==typeof(b=a.data)?b:b instanceof ArrayBuffer?dQ(new Uint8Array(b)):dQ(b):a.data}:a})})))},"ai.prompt.tools":{input:()=>null==N?void 0:N.map(a=>JSON.stringify(a))},"ai.prompt.toolChoice":{input:()=>null!=M?JSON.stringify(M):void 0},"gen_ai.system":L.provider,"gen_ai.request.model":L.modelId,"gen_ai.request.frequency_penalty":T.frequencyPenalty,"gen_ai.request.max_tokens":T.maxOutputTokens,"gen_ai.request.presence_penalty":T.presencePenalty,"gen_ai.request.stop_sequences":T.stopSequences,"gen_ai.request.temperature":T.temperature,"gen_ai.request.top_k":T.topK,"gen_ai.request.top_p":T.topP}}),tracer:S,endWhenDone:!1,fn:async a=>({startTimestampMs:t(),doStreamSpan:a,result:await L.doStream({...T,tools:N,toolChoice:M,responseFormat:null==p?void 0:p.responseFormat,prompt:J,providerOptions:q,abortSignal:f,headers:c,includeRawChunks:F})})})),Y=function({tools:a,generatorStream:b,tracer:c,telemetry:d,system:e,messages:f,abortSignal:g,repairToolCall:h,experimental_context:i}){let j,k=null,l=new ReadableStream({start(a){k=a}}),m=new Set,n=new Map,o=!1;function p(){o&&0===m.size&&(null!=j&&k.enqueue(j),k.close())}let q=new TransformStream({async transform(b,l){let o=b.type;switch(o){case"stream-start":case"text-start":case"text-delta":case"text-end":case"reasoning-start":case"reasoning-delta":case"reasoning-end":case"tool-input-start":case"tool-input-delta":case"tool-input-end":case"source":case"response-metadata":case"error":case"raw":l.enqueue(b);break;case"file":l.enqueue({type:"file",file:new jI({data:b.data,mediaType:b.mediaType})});break;case"finish":j={type:"finish",finishReason:b.finishReason,usage:b.usage,providerMetadata:b.providerMetadata};break;case"tool-call":try{let j=await jJ({toolCall:b,tools:a,repairToolCall:h,system:e,messages:f});if(l.enqueue(j),j.invalid){k.enqueue({type:"tool-error",toolCallId:j.toolCallId,toolName:j.toolName,input:j.input,error:di(j.error),dynamic:!0});break}let o=a[j.toolName];if(n.set(j.toolCallId,j.input),null!=o.onInputAvailable&&await o.onInputAvailable({input:j.input,toolCallId:j.toolCallId,messages:f,abortSignal:g,experimental_context:i}),null!=o.execute&&!0!==j.providerExecuted){let a=dh();m.add(a),jB({name:"ai.toolCall",attributes:jD({telemetry:d,attributes:{...jx({operationId:"ai.toolCall",telemetry:d}),"ai.toolCall.name":j.toolName,"ai.toolCall.id":j.toolCallId,"ai.toolCall.args":{output:()=>JSON.stringify(j.input)}}}),tracer:c,fn:async b=>{let c;try{for await(let a of dT({execute:o.execute.bind(o),input:j.input,options:{toolCallId:j.toolCallId,messages:f,abortSignal:g,experimental_context:i}}))k.enqueue({...j,type:"tool-result",output:a.output,..."preliminary"===a.type&&{preliminary:!0}}),"final"===a.type&&(c=a.output)}catch(c){jC(b,c),k.enqueue({...j,type:"tool-error",error:c}),m.delete(a),p();return}m.delete(a),p();try{b.setAttributes(jD({telemetry:d,attributes:{"ai.toolCall.result":{output:()=>JSON.stringify(c)}}}))}catch(a){}}})}}catch(a){k.enqueue({type:"error",error:a})}break;case"tool-result":{let a=b.toolName;b.isError?k.enqueue({type:"tool-error",toolCallId:b.toolCallId,toolName:a,input:n.get(b.toolCallId),providerExecuted:b.providerExecuted,error:b.result}):l.enqueue({type:"tool-result",toolCallId:b.toolCallId,toolName:a,input:n.get(b.toolCallId),output:b.result,providerExecuted:b.providerExecuted});break}default:throw Error(`Unhandled chunk type: ${o}`)}},flush(){o=!0,p()}});return new ReadableStream({start:async a=>Promise.all([b.pipeThrough(q).pipeTo(new WritableStream({write(b){a.enqueue(b)},close(){}})),l.pipeTo(new WritableStream({write(b){a.enqueue(b)},close(){a.close()}}))])})}({tools:j,generatorStream:O,tracer:S,telemetry:b,system:g,messages:H,repairToolCall:n,abortSignal:f,experimental_context:B}),Z=null!=Q?Q:{},$=[],_=[],aa={},ab="unknown",ac={inputTokens:void 0,outputTokens:void 0,totalTokens:void 0},ad=!0,ae={id:v(),timestamp:u(),modelId:a.modelId},af="";V.addStream(Y.pipeThrough(new TransformStream({async transform(a,b){var c,d,e,g;if("stream-start"===a.type){D=a.warnings;return}if(ad){let a=t()-X;ad=!1,W.addEvent("ai.stream.firstChunk",{"ai.response.msToFirstChunk":a}),W.setAttributes({"ai.response.msToFirstChunk":a}),b.enqueue({type:"start-step",request:Z,warnings:null!=D?D:[]})}let h=a.type;switch(h){case"text-start":case"text-end":case"reasoning-start":case"reasoning-end":case"file":case"source":b.enqueue(a);break;case"text-delta":a.delta.length>0&&(b.enqueue({type:"text-delta",id:a.id,text:a.delta,providerMetadata:a.providerMetadata}),af+=a.delta);break;case"reasoning-delta":b.enqueue({type:"reasoning-delta",id:a.id,text:a.delta,providerMetadata:a.providerMetadata});break;case"tool-call":b.enqueue(a),$.push(a);break;case"tool-result":b.enqueue(a),a.preliminary||_.push(a);break;case"tool-error":b.enqueue(a),_.push(a);break;case"response-metadata":ae={id:null!=(c=a.id)?c:ae.id,timestamp:null!=(d=a.timestamp)?d:ae.timestamp,modelId:null!=(e=a.modelId)?e:ae.modelId};break;case"finish":{ac=a.usage,ab=a.finishReason,E=a.providerMetadata;let b=t()-X;W.addEvent("ai.stream.finish"),W.setAttributes({"ai.response.msToFinish":b,"ai.response.avgOutputTokensPerSecond":1e3*(null!=(g=ac.outputTokens)?g:0)/b});break}case"tool-input-start":{aa[a.id]=a.toolName;let c=null==j?void 0:j[a.toolName];(null==c?void 0:c.onInputStart)!=null&&await c.onInputStart({toolCallId:a.id,messages:H,abortSignal:f,experimental_context:B}),b.enqueue({...a,dynamic:(null==c?void 0:c.type)==="dynamic"});break}case"tool-input-end":delete aa[a.id],b.enqueue(a);break;case"tool-input-delta":{let c=aa[a.id],d=null==j?void 0:j[c];(null==d?void 0:d.onInputDelta)!=null&&await d.onInputDelta({inputTextDelta:a.delta,toolCallId:a.id,messages:H,abortSignal:f,experimental_context:B}),b.enqueue(a);break}case"error":b.enqueue(a),ab="error";break;case"raw":F&&b.enqueue(a);break;default:throw Error(`Unknown chunk type: ${h}`)}},async flush(a){var c;let f=$.length>0?JSON.stringify($):void 0;try{W.setAttributes(jD({telemetry:b,attributes:{"ai.response.finishReason":ab,"ai.response.text":{output:()=>af},"ai.response.toolCalls":{output:()=>f},"ai.response.id":ae.id,"ai.response.model":ae.modelId,"ai.response.timestamp":ae.timestamp.toISOString(),"ai.response.providerMetadata":JSON.stringify(E),"ai.usage.inputTokens":ac.inputTokens,"ai.usage.outputTokens":ac.outputTokens,"ai.usage.totalTokens":ac.totalTokens,"ai.usage.reasoningTokens":ac.reasoningTokens,"ai.usage.cachedInputTokens":ac.cachedInputTokens,"gen_ai.response.finish_reasons":[ab],"gen_ai.response.id":ae.id,"gen_ai.response.model":ae.modelId,"gen_ai.usage.input_tokens":ac.inputTokens,"gen_ai.usage.output_tokens":ac.outputTokens}}))}catch(a){}finally{W.end()}a.enqueue({type:"finish-step",finishReason:ab,usage:ac,providerMetadata:E,response:{...ae,headers:null==P?void 0:P.headers}});let g=(c=ac,{inputTokens:jE(s.inputTokens,c.inputTokens),outputTokens:jE(s.outputTokens,c.outputTokens),totalTokens:jE(s.totalTokens,c.totalTokens),reasoningTokens:jE(s.reasoningTokens,c.reasoningTokens),cachedInputTokens:jE(s.cachedInputTokens,c.cachedInputTokens)});await C.promise;let h=$.filter(a=>!0!==a.providerExecuted),i=_.filter(a=>!0!==a.providerExecuted);if(h.length>0&&i.length===h.length&&!await jM({stopConditions:o,steps:K})){l.push(...jP({content:K[K.length-1].content,tools:j}));try{await e({currentStep:d+1,responseMessages:l,usage:g})}catch(b){a.enqueue({type:"error",error:b}),V.closeStream()}}else a.enqueue({type:"finish",finishReason:ab,totalUsage:g}),V.closeStream()}})))}D=d,await e({currentStep:0,responseMessages:[],usage:{inputTokens:void 0,outputTokens:void 0,totalTokens:void 0}})}}).catch(a=>{V.addStream(new ReadableStream({start(b){b.enqueue({type:"error",error:a}),b.close()}})),V.closeStream()})}get steps(){return this.consumeStream(),this._steps.promise}get finalStep(){return this.steps.then(a=>a[a.length-1])}get content(){return this.finalStep.then(a=>a.content)}get warnings(){return this.finalStep.then(a=>a.warnings)}get providerMetadata(){return this.finalStep.then(a=>a.providerMetadata)}get text(){return this.finalStep.then(a=>a.text)}get reasoningText(){return this.finalStep.then(a=>a.reasoningText)}get reasoning(){return this.finalStep.then(a=>a.reasoning)}get sources(){return this.finalStep.then(a=>a.sources)}get files(){return this.finalStep.then(a=>a.files)}get toolCalls(){return this.finalStep.then(a=>a.toolCalls)}get staticToolCalls(){return this.finalStep.then(a=>a.staticToolCalls)}get dynamicToolCalls(){return this.finalStep.then(a=>a.dynamicToolCalls)}get toolResults(){return this.finalStep.then(a=>a.toolResults)}get staticToolResults(){return this.finalStep.then(a=>a.staticToolResults)}get dynamicToolResults(){return this.finalStep.then(a=>a.dynamicToolResults)}get usage(){return this.finalStep.then(a=>a.usage)}get request(){return this.finalStep.then(a=>a.request)}get response(){return this.finalStep.then(a=>a.response)}get totalUsage(){return this.consumeStream(),this._totalUsage.promise}get finishReason(){return this.consumeStream(),this._finishReason.promise}teeStream(){let[a,b]=this.baseStream.tee();return this.baseStream=b,a}get textStream(){return jX(this.teeStream().pipeThrough(new TransformStream({transform({part:a},b){"text-delta"===a.type&&b.enqueue(a.text)}})))}get fullStream(){return jX(this.teeStream().pipeThrough(new TransformStream({transform({part:a},b){b.enqueue(a)}})))}async consumeStream(a){var b;try{await jY({stream:this.fullStream,onError:null==a?void 0:a.onError})}catch(c){null==(b=null==a?void 0:a.onError)||b.call(a,c)}}get experimental_partialOutputStream(){if(null==this.output)throw new iy;return jX(this.teeStream().pipeThrough(new TransformStream({transform({partialOutput:a},b){null!=a&&b.enqueue(a)}})))}toUIMessageStream({originalMessages:a,generateMessageId:b,onFinish:c,messageMetadata:d,sendReasoning:e=!0,sendSources:f=!1,sendStart:g=!0,sendFinish:h=!0,onError:i=av}={}){let j=null!=b?function({originalMessages:a,responseMessageId:b}){if(null==a)return;let c=a[a.length-1];return(null==c?void 0:c.role)==="assistant"?c.id:"function"==typeof b?b():b}({originalMessages:a,responseMessageId:b}):void 0,k={},l=a=>{var b,c;let d=k[a];return(null==(c=null==(b=this.tools)?void 0:b[d])?void 0:c.type)==="dynamic"||void 0};return jX(function({messageId:a,originalMessages:b=[],onFinish:c,onError:d,stream:e}){let f=null==b?void 0:b[b.length-1];(null==f?void 0:f.role)!=="assistant"?f=void 0:a=f.id;let g=!1,h=e.pipeThrough(new TransformStream({transform(b,c){"start"===b.type&&null==b.messageId&&null!=a&&(b.messageId=a),"abort"===b.type&&(g=!0),c.enqueue(b)}}));if(null==c)return h;let i=function({lastMessage:a,messageId:b}){return{message:(null==a?void 0:a.role)==="assistant"?a:{id:b,metadata:void 0,role:"assistant",parts:[]},activeTextParts:{},activeReasoningParts:{},partialToolCalls:{}}}({lastMessage:f?structuredClone(f):void 0,messageId:null!=a?a:""});return(function({stream:a,messageMetadataSchema:b,dataPartSchemas:c,runUpdateMessageJob:d,onError:e,onToolCall:f,onData:g}){return a.pipeThrough(new TransformStream({async transform(a,h){await d(async({state:d,write:i})=>{var j,k,l,m;function n(a){let b=d.message.parts.filter(jV).find(b=>b.toolCallId===a);if(null==b)throw Error("tool-output-error must be preceded by a tool-input-available");return b}function o(a){let b=d.message.parts.filter(a=>"dynamic-tool"===a.type).find(b=>b.toolCallId===a);if(null==b)throw Error("tool-output-error must be preceded by a tool-input-available");return b}function p(a){var b;let c=d.message.parts.find(b=>jV(b)&&b.toolCallId===a.toolCallId);null!=c?(c.state=a.state,c.input=a.input,c.output=a.output,c.errorText=a.errorText,c.rawInput=a.rawInput,c.preliminary=a.preliminary,c.providerExecuted=null!=(b=a.providerExecuted)?b:c.providerExecuted,null!=a.providerMetadata&&"input-available"===c.state&&(c.callProviderMetadata=a.providerMetadata)):d.message.parts.push({type:`tool-${a.toolName}`,toolCallId:a.toolCallId,state:a.state,input:a.input,output:a.output,rawInput:a.rawInput,errorText:a.errorText,providerExecuted:a.providerExecuted,preliminary:a.preliminary,...null!=a.providerMetadata?{callProviderMetadata:a.providerMetadata}:{}})}function q(a){var b;let c=d.message.parts.find(b=>"dynamic-tool"===b.type&&b.toolCallId===a.toolCallId);null!=c?(c.state=a.state,c.toolName=a.toolName,c.input=a.input,c.output=a.output,c.errorText=a.errorText,c.rawInput=null!=(b=a.rawInput)?b:c.rawInput,c.preliminary=a.preliminary,null!=a.providerMetadata&&"input-available"===c.state&&(c.callProviderMetadata=a.providerMetadata)):d.message.parts.push({type:"dynamic-tool",toolName:a.toolName,toolCallId:a.toolCallId,state:a.state,input:a.input,output:a.output,errorText:a.errorText,preliminary:a.preliminary,...null!=a.providerMetadata?{callProviderMetadata:a.providerMetadata}:{}})}async function r(a){if(null!=a){let c=null!=d.message.metadata?function a(b,c){if(void 0===b&&void 0===c)return;if(void 0===b)return c;if(void 0===c)return b;let d={...b};for(let e in c)if(Object.prototype.hasOwnProperty.call(c,e)){let f=c[e];if(void 0===f)continue;let g=e in b?b[e]:void 0,h=null!==f&&"object"==typeof f&&!Array.isArray(f)&&!(f instanceof Date)&&!(f instanceof RegExp),i=null!=g&&"object"==typeof g&&!Array.isArray(g)&&!(g instanceof Date)&&!(g instanceof RegExp);h&&i?d[e]=a(g,f):d[e]=f}return d}(d.message.metadata,a):a;null!=b&&await dv({value:c,schema:b}),d.message.metadata=c}}switch(a.type){case"text-start":{let b={type:"text",text:"",providerMetadata:a.providerMetadata,state:"streaming"};d.activeTextParts[a.id]=b,d.message.parts.push(b),i();break}case"text-delta":{let b=d.activeTextParts[a.id];b.text+=a.delta,b.providerMetadata=null!=(j=a.providerMetadata)?j:b.providerMetadata,i();break}case"text-end":{let b=d.activeTextParts[a.id];b.state="done",b.providerMetadata=null!=(k=a.providerMetadata)?k:b.providerMetadata,delete d.activeTextParts[a.id],i();break}case"reasoning-start":{let b={type:"reasoning",text:"",providerMetadata:a.providerMetadata,state:"streaming"};d.activeReasoningParts[a.id]=b,d.message.parts.push(b),i();break}case"reasoning-delta":{let b=d.activeReasoningParts[a.id];b.text+=a.delta,b.providerMetadata=null!=(l=a.providerMetadata)?l:b.providerMetadata,i();break}case"reasoning-end":{let b=d.activeReasoningParts[a.id];b.providerMetadata=null!=(m=a.providerMetadata)?m:b.providerMetadata,b.state="done",delete d.activeReasoningParts[a.id],i();break}case"file":d.message.parts.push({type:"file",mediaType:a.mediaType,url:a.url}),i();break;case"source-url":d.message.parts.push({type:"source-url",sourceId:a.sourceId,url:a.url,title:a.title,providerMetadata:a.providerMetadata}),i();break;case"source-document":d.message.parts.push({type:"source-document",sourceId:a.sourceId,mediaType:a.mediaType,title:a.title,filename:a.filename,providerMetadata:a.providerMetadata}),i();break;case"tool-input-start":{let b=d.message.parts.filter(jV);d.partialToolCalls[a.toolCallId]={text:"",toolName:a.toolName,index:b.length,dynamic:a.dynamic},a.dynamic?q({toolCallId:a.toolCallId,toolName:a.toolName,state:"input-streaming",input:void 0}):p({toolCallId:a.toolCallId,toolName:a.toolName,state:"input-streaming",input:void 0,providerExecuted:a.providerExecuted}),i();break}case"tool-input-delta":{let b=d.partialToolCalls[a.toolCallId];b.text+=a.inputTextDelta;let{value:c}=await jU(b.text);b.dynamic?q({toolCallId:a.toolCallId,toolName:b.toolName,state:"input-streaming",input:c}):p({toolCallId:a.toolCallId,toolName:b.toolName,state:"input-streaming",input:c}),i();break}case"tool-input-available":a.dynamic?q({toolCallId:a.toolCallId,toolName:a.toolName,state:"input-available",input:a.input,providerMetadata:a.providerMetadata}):p({toolCallId:a.toolCallId,toolName:a.toolName,state:"input-available",input:a.input,providerExecuted:a.providerExecuted,providerMetadata:a.providerMetadata}),i(),f&&!a.providerExecuted&&await f({toolCall:a});break;case"tool-input-error":a.dynamic?q({toolCallId:a.toolCallId,toolName:a.toolName,state:"output-error",input:a.input,errorText:a.errorText,providerMetadata:a.providerMetadata}):p({toolCallId:a.toolCallId,toolName:a.toolName,state:"output-error",input:void 0,rawInput:a.input,errorText:a.errorText,providerExecuted:a.providerExecuted,providerMetadata:a.providerMetadata}),i();break;case"tool-output-available":if(a.dynamic){let b=o(a.toolCallId);q({toolCallId:a.toolCallId,toolName:b.toolName,state:"output-available",input:b.input,output:a.output,preliminary:a.preliminary})}else{let b=n(a.toolCallId);p({toolCallId:a.toolCallId,toolName:jW(b),state:"output-available",input:b.input,output:a.output,providerExecuted:a.providerExecuted,preliminary:a.preliminary})}i();break;case"tool-output-error":if(a.dynamic){let b=o(a.toolCallId);q({toolCallId:a.toolCallId,toolName:b.toolName,state:"output-error",input:b.input,errorText:a.errorText})}else{let b=n(a.toolCallId);p({toolCallId:a.toolCallId,toolName:jW(b),state:"output-error",input:b.input,rawInput:b.rawInput,errorText:a.errorText})}i();break;case"start-step":d.message.parts.push({type:"step-start"});break;case"finish-step":d.activeTextParts={},d.activeReasoningParts={};break;case"start":null!=a.messageId&&(d.message.id=a.messageId),await r(a.messageMetadata),(null!=a.messageId||null!=a.messageMetadata)&&i();break;case"finish":case"message-metadata":await r(a.messageMetadata),null!=a.messageMetadata&&i();break;case"error":null==e||e(Error(a.errorText));break;default:if(a.type.startsWith("data-")){if((null==c?void 0:c[a.type])!=null&&await dv({value:a.data,schema:c[a.type]}),a.transient){null==g||g(a);break}let b=null!=a.id?d.message.parts.find(b=>a.type===b.type&&a.id===b.id):void 0;null!=b?b.data=a.data:d.message.parts.push(a),null==g||g(a),i()}}h.enqueue(a)})}}))})({stream:h,runUpdateMessageJob:async a=>{await a({state:i,write:()=>{}})},onError:d}).pipeThrough(new TransformStream({transform(a,b){b.enqueue(a)},async flush(){let a=i.message.id===(null==f?void 0:f.id);await c({isAborted:g,isContinuation:a,responseMessage:i.message,messages:[...a?b.slice(0,-1):b,i.message]})}}))}({stream:this.fullStream.pipeThrough(new TransformStream({transform:async(a,b)=>{let c=null==d?void 0:d({part:a}),m=a.type;switch(m){case"text-start":b.enqueue({type:"text-start",id:a.id,...null!=a.providerMetadata?{providerMetadata:a.providerMetadata}:{}});break;case"text-delta":b.enqueue({type:"text-delta",id:a.id,delta:a.text,...null!=a.providerMetadata?{providerMetadata:a.providerMetadata}:{}});break;case"text-end":b.enqueue({type:"text-end",id:a.id,...null!=a.providerMetadata?{providerMetadata:a.providerMetadata}:{}});break;case"reasoning-start":b.enqueue({type:"reasoning-start",id:a.id,...null!=a.providerMetadata?{providerMetadata:a.providerMetadata}:{}});break;case"reasoning-delta":e&&b.enqueue({type:"reasoning-delta",id:a.id,delta:a.text,...null!=a.providerMetadata?{providerMetadata:a.providerMetadata}:{}});break;case"reasoning-end":b.enqueue({type:"reasoning-end",id:a.id,...null!=a.providerMetadata?{providerMetadata:a.providerMetadata}:{}});break;case"file":b.enqueue({type:"file",mediaType:a.file.mediaType,url:`data:${a.file.mediaType};base64,${a.file.base64}`});break;case"source":f&&"url"===a.sourceType&&b.enqueue({type:"source-url",sourceId:a.id,url:a.url,title:a.title,...null!=a.providerMetadata?{providerMetadata:a.providerMetadata}:{}}),f&&"document"===a.sourceType&&b.enqueue({type:"source-document",sourceId:a.id,mediaType:a.mediaType,title:a.title,filename:a.filename,...null!=a.providerMetadata?{providerMetadata:a.providerMetadata}:{}});break;case"tool-input-start":{k[a.id]=a.toolName;let c=l(a.id);b.enqueue({type:"tool-input-start",toolCallId:a.id,toolName:a.toolName,...null!=a.providerExecuted?{providerExecuted:a.providerExecuted}:{},...null!=c?{dynamic:c}:{}});break}case"tool-input-delta":b.enqueue({type:"tool-input-delta",toolCallId:a.id,inputTextDelta:a.delta});break;case"tool-call":{k[a.toolCallId]=a.toolName;let c=l(a.toolCallId);a.invalid?b.enqueue({type:"tool-input-error",toolCallId:a.toolCallId,toolName:a.toolName,input:a.input,...null!=a.providerExecuted?{providerExecuted:a.providerExecuted}:{},...null!=a.providerMetadata?{providerMetadata:a.providerMetadata}:{},...null!=c?{dynamic:c}:{},errorText:i(a.error)}):b.enqueue({type:"tool-input-available",toolCallId:a.toolCallId,toolName:a.toolName,input:a.input,...null!=a.providerExecuted?{providerExecuted:a.providerExecuted}:{},...null!=a.providerMetadata?{providerMetadata:a.providerMetadata}:{},...null!=c?{dynamic:c}:{}});break}case"tool-result":{let c=l(a.toolCallId);b.enqueue({type:"tool-output-available",toolCallId:a.toolCallId,output:a.output,...null!=a.providerExecuted?{providerExecuted:a.providerExecuted}:{},...null!=a.preliminary?{preliminary:a.preliminary}:{},...null!=c?{dynamic:c}:{}});break}case"tool-error":{let c=l(a.toolCallId);b.enqueue({type:"tool-output-error",toolCallId:a.toolCallId,errorText:i(a.error),...null!=a.providerExecuted?{providerExecuted:a.providerExecuted}:{},...null!=c?{dynamic:c}:{}});break}case"error":b.enqueue({type:"error",errorText:i(a.error)});break;case"start-step":b.enqueue({type:"start-step"});break;case"finish-step":b.enqueue({type:"finish-step"});break;case"start":g&&b.enqueue({type:"start",...null!=c?{messageMetadata:c}:{},...null!=j?{messageId:j}:{}});break;case"finish":h&&b.enqueue({type:"finish",...null!=c?{messageMetadata:c}:{}});break;case"abort":b.enqueue(a);break;case"tool-input-end":case"raw":break;default:throw Error(`Unknown chunk type: ${m}`)}null!=c&&"start"!==m&&"finish"!==m&&b.enqueue({type:"message-metadata",messageMetadata:c})}})),messageId:null!=j?j:null==b?void 0:b(),originalMessages:a,onFinish:c,onError:i}))}pipeUIMessageStreamToResponse(a,{originalMessages:b,generateMessageId:c,onFinish:d,messageMetadata:e,sendReasoning:f,sendSources:g,sendFinish:h,sendStart:i,onError:j,...k}={}){!function({response:a,status:b,statusText:c,headers:d,stream:e,consumeSseStream:f}){let g=e.pipeThrough(new jS);if(f){let[a,b]=g.tee();g=a,f({stream:b})}jR({response:a,status:b,statusText:c,headers:Object.fromEntries(jQ(d,jT).entries()),stream:g.pipeThrough(new TextEncoderStream)})}({response:a,stream:this.toUIMessageStream({originalMessages:b,generateMessageId:c,onFinish:d,messageMetadata:e,sendReasoning:f,sendSources:g,sendFinish:h,sendStart:i,onError:j}),...k})}pipeTextStreamToResponse(a,b){!function({response:a,status:b,statusText:c,headers:d,textStream:e}){jR({response:a,status:b,statusText:c,headers:Object.fromEntries(jQ(d,{"content-type":"text/plain; charset=utf-8"}).entries()),stream:e.pipeThrough(new TextEncoderStream)})}({response:a,textStream:this.textStream,...b})}toUIMessageStreamResponse({originalMessages:a,generateMessageId:b,onFinish:c,messageMetadata:d,sendReasoning:e,sendSources:f,sendFinish:g,sendStart:h,onError:i,...j}={}){return function({status:a,statusText:b,headers:c,stream:d,consumeSseStream:e}){let f=d.pipeThrough(new jS);if(e){let[a,b]=f.tee();f=a,e({stream:b})}return new Response(f.pipeThrough(new TextEncoderStream),{status:a,statusText:b,headers:jQ(c,jT)})}({stream:this.toUIMessageStream({originalMessages:a,generateMessageId:b,onFinish:c,messageMetadata:d,sendReasoning:e,sendSources:f,sendFinish:g,sendStart:h,onError:i}),...j})}toTextStreamResponse(a){return function({status:a,statusText:b,headers:c,textStream:d}){return new Response(d.pipeThrough(new TextEncoderStream),{status:null!=a?a:200,statusText:b,headers:jQ(c,{"content-type":"text/plain; charset=utf-8"})})}({textStream:this.textStream,...a})}};dg({prefix:"aiobj",size:24}),dg({prefix:"aiobj",size:24}),((a,b)=>{for(var c in b)iu(a,c,{get:b[c],enumerable:!0})})({},{object:()=>j2,text:()=>j1});var j1=()=>({type:"text",responseFormat:{type:"text"},parsePartial:async({text:a})=>({partial:a}),parseOutput:async({text:a})=>a}),j2=({schema:a})=>{let b=dM(a);return{type:"object",responseFormat:{type:"json",schema:b.jsonSchema},async parsePartial({text:a}){let b=await jU(a);switch(b.state){case"failed-parse":case"undefined-input":return;case"repaired-parse":case"successful-parse":return{partial:b.value};default:{let a=b.state;throw Error(`Unsupported parse state: ${a}`)}}},async parseOutput({text:a},c){let d=await dy({text:a});if(!d.success)throw new iK({message:"No object generated: could not parse the response.",cause:d.error,text:a,response:c.response,usage:c.usage,finishReason:c.finishReason});let e=await dw({value:d.value,schema:b});if(!e.success)throw new iK({message:"No object generated: response did not match schema.",cause:e.error,text:a,response:c.response,usage:c.usage,finishReason:c.finishReason});return e.value}}},j3=(Symbol.for("vercel.ai.error.AI_NoSuchProviderError"),gu({name:fT(),version:fT()})),j4=gu({_meta:gI(gs({}).loose())}),j5=gs({method:fT(),params:gI(j4)}),j6=gu({experimental:gI(gs({}).loose()),logging:gI(gs({}).loose()),prompts:gI(gu({listChanged:gI(gh())})),resources:gI(gu({subscribe:gI(gh()),listChanged:gI(gh())})),tools:gI(gu({listChanged:gI(gh())}))});j4.extend({protocolVersion:fT(),capabilities:j6,serverInfo:j3,instructions:gI(fT())});var j7=j4.extend({nextCursor:gI(fT())}),j8=gs({name:fT(),description:gI(fT()),inputSchema:gs({type:gF("object"),properties:gI(gs({}).loose())}).loose()}).loose();j7.extend({tools:gq(j8)});var j9=gs({type:gF("text"),text:fT()}).loose(),ka=gs({type:gF("image"),data:fC(f8,void 0),mimeType:fT()}).loose(),kb=gs({uri:fT(),mimeType:gI(fT())}).loose(),kc=kb.extend({text:fT()}),kd=kb.extend({blob:fC(f8,void 0)}),ke=gs({type:gF("resource"),resource:gw([kc,kd])}).loose();j4.extend({content:gq(gw([j9,ka,ke])),isError:gh().default(!1).optional()}).or(j4.extend({toolResult:gm()}));var kf=gs({jsonrpc:gF("2.0"),id:gw([fT(),gd().int()])}).merge(j5).strict(),kg=gs({jsonrpc:gF("2.0"),id:gw([fT(),gd().int()]),result:j4}).strict(),kh=gs({jsonrpc:gF("2.0"),id:gw([fT(),gd().int()]),error:gs({code:gd().int(),message:fT(),data:gI(gm())})}).strict();gw([kf,gs({jsonrpc:gF("2.0")}).merge(gs({method:fT(),params:gI(j4)})).strict(),kg,kh]);var ki=gs({type:gF("text"),text:fT(),state:gD(["streaming","done"]).optional(),providerMetadata:jk.optional()}),kj=gs({type:gF("reasoning"),text:fT(),state:gD(["streaming","done"]).optional(),providerMetadata:jk.optional()}),kk=gs({type:gF("source-url"),sourceId:fT(),url:fT(),title:fT().optional(),providerMetadata:jk.optional()}),kl=gs({type:gF("source-document"),sourceId:fT(),mediaType:fT(),title:fT(),filename:fT().optional(),providerMetadata:jk.optional()}),km=gs({type:gF("file"),mediaType:fT(),filename:fT().optional(),url:fT(),providerMetadata:jk.optional()}),kn=gs({type:gF("step-start")}),ko=gs({type:fT().startsWith("data-"),id:fT().optional(),data:gm()}),kp=[gs({type:gF("dynamic-tool"),toolName:fT(),toolCallId:fT(),state:gF("input-streaming"),input:gm().optional(),output:go().optional(),errorText:go().optional()}),gs({type:gF("dynamic-tool"),toolName:fT(),toolCallId:fT(),state:gF("input-available"),input:gm(),output:go().optional(),errorText:go().optional(),callProviderMetadata:jk.optional()}),gs({type:gF("dynamic-tool"),toolName:fT(),toolCallId:fT(),state:gF("output-available"),input:gm(),output:gm(),errorText:go().optional(),callProviderMetadata:jk.optional(),preliminary:gh().optional()}),gs({type:gF("dynamic-tool"),toolName:fT(),toolCallId:fT(),state:gF("output-error"),input:gm(),output:go().optional(),errorText:fT(),callProviderMetadata:jk.optional()})],kq=[gs({type:fT().startsWith("tool-"),toolCallId:fT(),state:gF("input-streaming"),input:gm().optional(),output:go().optional(),errorText:go().optional()}),gs({type:fT().startsWith("tool-"),toolCallId:fT(),state:gF("input-available"),input:gm(),output:go().optional(),errorText:go().optional(),callProviderMetadata:jk.optional()}),gs({type:fT().startsWith("tool-"),toolCallId:fT(),state:gF("output-available"),input:gm(),output:gm(),errorText:go().optional(),callProviderMetadata:jk.optional(),preliminary:gh().optional()}),gs({type:fT().startsWith("tool-"),toolCallId:fT(),state:gF("output-error"),input:gm(),output:go().optional(),errorText:fT(),callProviderMetadata:jk.optional()})];async function kr(a){let{messages:b}=await a.json();return(function({model:a,tools:b,toolChoice:c,system:d,prompt:e,messages:f,maxRetries:g,abortSignal:h,headers:i,stopWhen:j=function(a){return({steps:a})=>1===a.length}(0),experimental_output:k,experimental_telemetry:l,prepareStep:m,providerOptions:n,experimental_activeTools:o,activeTools:p=o,experimental_repairToolCall:q,experimental_transform:r,includeRawChunks:s=!1,onChunk:t,onError:u=({error:a})=>{console.error(a)},onFinish:v,onAbort:w,onStepFinish:x,experimental_context:y,_internal:{now:z=function(){var a,b;return null!=(b=null==(a=null==globalThis?void 0:globalThis.performance)?void 0:a.now())?b:Date.now()},generateId:A=j_,currentDate:B=()=>new Date}={},...C}){return new j0({model:jc(a),telemetry:l,headers:i,settings:C,maxRetries:g,abortSignal:h,system:d,prompt:e,messages:f,tools:b,toolChoice:c,transforms:jF(r),activeTools:p,repairToolCall:q,stopConditions:jF(j),output:k,providerOptions:n,prepareStep:m,includeRawChunks:s,onChunk:t,onError:u,onFinish:v,onAbort:w,onStepFinish:x,now:z,currentDate:B,generateId:A,experimental_context:y})})({model:hM("gpt-4o"),messages:function(a,b){let c=[];for(let d of a)switch(d.role){case"system":{let a=d.parts.filter(a=>"text"===a.type),b=a.reduce((a,b)=>null!=b.providerMetadata?{...a,...b.providerMetadata}:a,{});c.push({role:"system",content:a.map(a=>a.text).join(""),...Object.keys(b).length>0?{providerOptions:b}:{}});break}case"user":c.push({role:"user",content:d.parts.filter(a=>"text"===a.type||"file"===a.type).map(a=>{switch(a.type){case"text":return{type:"text",text:a.text,...null!=a.providerMetadata?{providerOptions:a.providerMetadata}:{}};case"file":return{type:"file",mediaType:a.mediaType,filename:a.filename,data:a.url,...null!=a.providerMetadata?{providerOptions:a.providerMetadata}:{}};default:return a}})});break;case"assistant":if(null!=d.parts){let a=function(){var a,f;if(0===e.length)return;let g=[];for(let b of e)if("text"===b.type)g.push({type:"text",text:b.text,...null!=b.providerMetadata?{providerOptions:b.providerMetadata}:{}});else if("file"===b.type)g.push({type:"file",mediaType:b.mediaType,filename:b.filename,data:b.url});else if("reasoning"===b.type)g.push({type:"reasoning",text:b.text,providerOptions:b.providerMetadata});else if("dynamic-tool"===b.type){let a=b.toolName;if("input-streaming"===b.state)throw new i3({originalMessage:d,message:`incomplete tool input is not supported: ${b.toolCallId}`});g.push({type:"tool-call",toolCallId:b.toolCallId,toolName:a,input:b.input,...null!=b.callProviderMetadata?{providerOptions:b.callProviderMetadata}:{}})}else if(jV(b)){let c=jW(b);if("input-streaming"===b.state)throw new i3({originalMessage:d,message:`incomplete tool input is not supported: ${b.toolCallId}`});g.push({type:"tool-call",toolCallId:b.toolCallId,toolName:c,input:"output-error"===b.state?null!=(a=b.input)?a:b.rawInput:b.input,providerExecuted:b.providerExecuted,...null!=b.callProviderMetadata?{providerOptions:b.callProviderMetadata}:{}}),!0===b.providerExecuted&&("output-available"===b.state||"output-error"===b.state)&&g.push({type:"tool-result",toolCallId:b.toolCallId,toolName:c,output:jN({output:"output-error"===b.state?b.errorText:b.output,tool:(f=void 0,void 0),errorMode:"output-error"===b.state?"json":"none"})})}else throw Error(`Unsupported part: ${b}`);c.push({role:"assistant",content:g});let h=e.filter(a=>jV(a)&&!0!==a.providerExecuted||"dynamic-tool"===a.type);h.length>0&&c.push({role:"tool",content:h.map(a=>{var c;switch(a.state){case"output-error":case"output-available":{let d="dynamic-tool"===a.type?a.toolName:jW(a);return{type:"tool-result",toolCallId:a.toolCallId,toolName:d,output:jN({output:"output-error"===a.state?a.errorText:a.output,tool:null==(c=null==b?void 0:b.tools)?void 0:c[d],errorMode:"output-error"===a.state?"text":"none"})}}default:throw new i3({originalMessage:d,message:`Unsupported tool part state: ${a.state}`})}})}),e=[]},e=[];for(let b of d.parts)"text"===b.type||"reasoning"===b.type||"file"===b.type||"dynamic-tool"===b.type||jV(b)?e.push(b):"step-start"===b.type&&a();a()}break;default:{let a=d.role;throw new i3({originalMessage:d,message:`Unsupported role: ${a}`})}}return c}(b)}).toUIMessageStreamResponse()}gs({id:fT(),role:gD(["system","user","assistant"]),metadata:gm().optional(),parts:gq(gw([ki,kj,kk,kl,km,kn,ko,...kp,...kq]))});let ks=new V.AppRouteRouteModule({definition:{kind:W.RouteKind.APP_ROUTE,page:"/api/chat/route",pathname:"/api/chat",filename:"route",bundlePath:"app/api/chat/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Users/<USER>/work/Instrument/frontend-assistant-ui/app/api/chat/route.ts",nextConfigOutput:"",userland:U}),{workAsyncStorage:kt,workUnitAsyncStorage:ku,serverHooks:kv}=ks;function kw(){return(0,X.patchFetch)({workAsyncStorage:kt,workUnitAsyncStorage:ku})}async function kx(a,b,c){var d;let e="/api/chat/route";"/index"===e&&(e="/");let f=await ks.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!f)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:g,params:h,nextConfig:i,isDraftMode:j,prerenderManifest:k,routerServerContext:l,isOnDemandRevalidate:m,revalidateOnlyGenerated:n,resolvedPathname:o}=f,p=(0,$.normalizeAppPath)(e),q=!!(k.dynamicRoutes[p]||k.routes[o]);if(q&&!j){let a=!!k.routes[o],b=k.dynamicRoutes[p];if(b&&!1===b.fallback&&!a)throw new ah.NoFallbackError}let r=null;!q||ks.isDev||j||(r="/index"===(r=o)?"/":r);let s=!0===ks.isDev||!q,t=q&&!s,u=a.method||"GET",v=(0,Z.getTracer)(),w=v.getActiveScopeSpan(),x={params:h,prerenderManifest:k,renderOpts:{experimental:{dynamicIO:!!i.experimental.dynamicIO,authInterrupts:!!i.experimental.authInterrupts},supportsDynamicResponse:s,incrementalCache:(0,Y.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=i.experimental)?void 0:d.cacheLife,isRevalidate:t,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>ks.onRequestError(a,b,d,l)},sharedContext:{buildId:g}},y=new _.NodeNextRequest(a),z=new _.NodeNextResponse(b),A=aa.NextRequestAdapter.fromNodeNextRequest(y,(0,aa.signalFromNodeResponse)(b));try{let d=async c=>ks.handle(A,x).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=v.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==ab.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${u} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${u} ${a.url}`)}),f=async f=>{var g,h;let o=async({previousCacheEntry:g})=>{try{if(!(0,Y.getRequestMeta)(a,"minimalMode")&&m&&n&&!g)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(f);a.fetchMetrics=x.renderOpts.fetchMetrics;let h=x.renderOpts.pendingWaitUntil;h&&c.waitUntil&&(c.waitUntil(h),h=void 0);let i=x.renderOpts.collectedTags;if(!q)return await (0,ad.I)(y,z,e,x.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,ae.toNodeOutgoingHttpHeaders)(e.headers);i&&(b[ag.NEXT_CACHE_TAGS_HEADER]=i),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==x.renderOpts.collectedRevalidate&&!(x.renderOpts.collectedRevalidate>=ag.INFINITE_CACHE)&&x.renderOpts.collectedRevalidate,d=void 0===x.renderOpts.collectedExpire||x.renderOpts.collectedExpire>=ag.INFINITE_CACHE?void 0:x.renderOpts.collectedExpire;return{value:{kind:ai.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==g?void 0:g.isStale)&&await ks.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,ac.c)({isRevalidate:t,isOnDemandRevalidate:m})},l),b}},p=await ks.handleResponse({req:a,nextConfig:i,cacheKey:r,routeKind:W.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:k,isRoutePPREnabled:!1,isOnDemandRevalidate:m,revalidateOnlyGenerated:n,responseGenerator:o,waitUntil:c.waitUntil});if(!q)return null;if((null==p||null==(g=p.value)?void 0:g.kind)!==ai.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==p||null==(h=p.value)?void 0:h.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,Y.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",m?"REVALIDATED":p.isMiss?"MISS":p.isStale?"STALE":"HIT"),j&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let s=(0,ae.fromNodeOutgoingHttpHeaders)(p.value.headers);return(0,Y.getRequestMeta)(a,"minimalMode")&&q||s.delete(ag.NEXT_CACHE_TAGS_HEADER),!p.cacheControl||b.getHeader("Cache-Control")||s.get("Cache-Control")||s.set("Cache-Control",(0,af.getCacheControlHeader)(p.cacheControl)),await (0,ad.I)(y,z,new Response(p.value.body,{headers:s,status:p.value.status||200})),null};w?await f(w):await v.withPropagatedContext(a.headers,()=>v.trace(ab.BaseServerSpan.handleRequest,{spanName:`${u} ${a.url}`,kind:Z.SpanKind.SERVER,attributes:{"http.method":u,"http.target":a.url}},f))}catch(b){if(w||b instanceof ah.NoFallbackError||await ks.onRequestError(a,b,{routerKind:"App Router",routePath:p,routeType:"route",revalidateReason:(0,ac.c)({isRevalidate:t,isOnDemandRevalidate:m})}),q)throw b;return await (0,ad.I)(y,z,new Response(null,{status:500})),null}}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431],()=>b(b.s=9808));module.exports=c})();